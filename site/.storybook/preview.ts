import { withRouter } from "storybook-addon-remix-react-router";
import type { Preview } from "@storybook/react";
import { withI18next } from "./decorators/WithI18next";
import "../src/index.css";

export { globalTypes } from "./decorators/WithI18next";

const preview: Preview = {
  decorators: [withI18next, withRouter],
  parameters: {
    actions: { argTypesRegex: "^on[A-Z].*" },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
};

export default preview;
