import {
  <PERSON><PERSON>,
  <PERSON>,
  DatePicker,
  Dropdown,
  Heading,
  Image,
  Input,
  Modal,
  Tag,
  Text,
} from "@/components";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer, ModalHeader } from "@/components/Modal";
import { MegaphoneIcon, TicketIcon } from "@heroicons/react/24/outline";
import {
  ExclamationTriangleIcon,
  SparklesIcon,
  XCircleIcon,
} from "@heroicons/react/24/solid";
import { useState } from "react";
import formatDummy from "@/assets/imageFormatDummy.jpeg";
import {
  RoundedEyeIcon,
  RoundedFacebookIconLarge,
  RoundedInstagramIconLarge,
  RoundedLinkedinIconLarge,
  RoundedTikTokIconLarge,
  RoundedWhatsAppIconLarge,
  RoundedXIconLarge,
  RoundedYoutubeIconLarge,
} from "@/components/CustomIcons";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { SubmitHand<PERSON>, useForm } from "react-hook-form";
import { eachDayOfInterval } from "date-fns";
import { useTranslation } from "react-i18next";

export type CampaignBlocksProps = {
  onFinished: (data: Inputs) => void;
};

export interface Inputs {
  area: string;
  objective: string;
  startDate: Date;
  endDate: Date;
  postsPerDay: number;
  postsHours: string[];
  weekDays: string[];
  format: string;
  socialNetworks: string[];
  style: string;
  amountPosts?: number;
}

export function CampaignBlocks({ onFinished }: CampaignBlocksProps) {
  const { t } = useTranslation();
  const [openModal, setOpenModal] = useState(false);

  function showModal() {
    setOpenModal(!openModal);
  }

  const today = new Date();
  const tomorrow = new Date(
    today.getFullYear(),
    today.getMonth(),
    today.getDate() + 1,
  );

  const schema = yup.object().shape({
    area: yup.string().required(t("create-campaign.form.errors.required")),
    objective: yup.string().required(t("create-campaign.form.errors.required")),
    startDate: yup
      .date()
      .required(t("create-campaign.form.errors.required"))
      .default(today),
    endDate: yup
      .date()
      .required(t("create-campaign.form.errors.required"))
      .default(tomorrow),
    postsPerDay: yup
      .number()
      .required(t("create-campaign.form.errors.required-posts-per-day")),
    postsHours: yup
      .array()
      .required(t("create-campaign.form.errors.required-hours"))
      .test(
        "sameLength",
        t("create-campaign.form.errors.required-hours"),
        function (values) {
          let validation = true;
          values.forEach((value) => {
            validation = value != null;
          });
          return validation;
        },
      ),
    weekDays: yup
      .array()
      .required(t("create-campaign.form.errors.required-week-days"))
      .test(
        "sameLength",
        t("create-campaign.form.errors.required-week-days"),
        function (values) {
          return values.length > 0;
        },
      ),
    format: yup
      .string()
      .required(t("create-campaign.form.errors.required-format"))
      .test(
        "notEmpty",
        t("create-campaign.form.errors.required-format"),
        function (values) {
          return values.length > 0;
        },
      ),
    socialNetworks: yup
      .array()
      .required(t("create-campaign.form.errors.required-social-network"))
      .test(
        "sameLength",
        t("create-campaign.form.errors.required-social-network"),
        function (values) {
          return values.length > 0;
        },
      ),
    style: yup
      .string()
      .required(t("create-campaign.form.errors.required-style"))
      .test(
        "notEmpty",
        t("create-campaign.form.errors.required-style"),
        function (values) {
          return values.length > 0;
        },
      ),
    amountPosts: yup.number(),
  });

  const {
    handleSubmit,
    watch,
    getValues,
    setValue,
    register,
    clearErrors,
    trigger,
    formState: { errors },
  } = useForm<Inputs>({
    resolver: yupResolver(schema),
    delayError: 1000,
    mode: "onChange",
    shouldFocusError: true,
  });
  const onValidate: SubmitHandler<Inputs> = confirmSubmition;

  const postsDays = watch("postsPerDay");
  const weekDays = watch("weekDays");
  const format = watch("format");
  const socialNetworks = watch("socialNetworks");
  const style = watch("style");

  //DropDown Values:
  const amountPostsOptions = [
    { value: "1", label: "1" },
    { value: "2", label: "2" },
    { value: "3", label: "3" },
  ];
  const postsHoursOptions = [
    { value: "01:00AM", label: "01:00 AM" },
    { value: "02:00AM", label: "02:00 AM" },
    { value: "03:00AM", label: "03:00 AM" },
    { value: "04:00AM", label: "04:00 AM" },
    { value: "05:00AM", label: "05:00 AM" },
    { value: "06:00AM", label: "06:00 AM" },
    { value: "07:00AM", label: "07:00 AM" },
    { value: "08:00AM", label: "08:00 AM" },
    { value: "09:00AM", label: "09:00 AM" },
    { value: "10:00AM", label: "10:00 AM" },
    { value: "11:00AM", label: "11:00 AM" },
    { value: "12:00PM", label: "12:00 PM" },
    { value: "01:00PM", label: "01:00 PM" },
    { value: "02:00PM", label: "02:00 PM" },
    { value: "03:00PM", label: "03:00 PM" },
    { value: "04:00PM", label: "04:00 PM" },
    { value: "05:00PM", label: "05:00 PM" },
    { value: "06:00PM", label: "06:00 PM" },
    { value: "07:00PM", label: "07:00 PM" },
    { value: "08:00PM", label: "08:00 PM" },
    { value: "09:00PM", label: "09:00 PM" },
    { value: "10:00PM", label: "10:00 PM" },
    { value: "11:00PM", label: "11:00 PM" },
    { value: "12:00AM", label: "12:00 AM" },
  ];
  const weekDaysOptions = [
    { value: "Monday", label: t("create-campaign.form.week-days.Monday") },
    { value: "Tuesday", label: t("create-campaign.form.week-days.Tuesday") },
    {
      value: "Wednesday",
      label: t("create-campaign.form.week-days.Wednesday"),
    },
    { value: "Thursday", label: t("create-campaign.form.week-days.Thursday") },
    { value: "Friday", label: t("create-campaign.form.week-days.Friday") },
    { value: "Saturday", label: t("create-campaign.form.week-days.Saturday") },
    { value: "Sunday", label: t("create-campaign.form.week-days.Sunday") },
  ];

  // DropDown functions
  const filterdropdown = (
    inputValue: string,
    options: { value: string; label: string }[],
  ) => {
    return options.filter((i) =>
      i.label.toLowerCase().includes(inputValue.toLowerCase()),
    );
  };
  const loadOptions = (
    inputValue: string,
    callback: (options: { value: string; label: string }[]) => void,
    options: { value: string; label: string }[],
  ) => {
    setTimeout(() => {
      callback(filterdropdown(inputValue, options));
    }, 1000);
  };

  // Posts days functions
  function setAmountPosts(amount: number) {
    clearErrors("postsPerDay");
    setValue("postsPerDay", amount);
    setValue("postsHours", new Array(amount).fill(null));
    trigger("postsPerDay");
  }
  function addNewHour(index: number, inputValue: string) {
    if (getValues("postsHours")) {
      const newHoursList = [];
      for (let i = 0; i < getValues("postsHours").length; i++) {
        if (i === index) {
          newHoursList.push(inputValue);
        } else {
          newHoursList.push(getValues("postsHours")[i]);
        }
      }
      setValue("postsHours", newHoursList);
    } else {
      setValue("postsHours", [inputValue]);
    }
    trigger("postsHours");
  }

  // Week days functions
  function addNewWeekDay(value: string) {
    const newWeekDayList = getValues("weekDays") ? getValues("weekDays") : [];
    if (!newWeekDayList.find((weekDay) => weekDay === value)) {
      newWeekDayList.push(value);
      setValue("weekDays", newWeekDayList);
    }
    trigger("weekDays");
  }
  function removeWeekDay(index: number) {
    if (getValues("weekDays")) {
      const newWeekDayList = getValues("weekDays");
      newWeekDayList.splice(index, 1);
      setValue("weekDays", newWeekDayList);
    }
    trigger("weekDays");
  }

  // Social Networks functions
  function findSocialNetwork(name: string): boolean {
    return socialNetworks
      ? socialNetworks.find((socialNetwork) => socialNetwork === name)
        ? true
        : false
      : false;
  }
  function setSocialNetwork(name: string) {
    if (socialNetworks) {
      if (findSocialNetwork(name) === false) {
        const newSocialNetworks = getValues("socialNetworks")
          ? getValues("socialNetworks")
          : [];
        newSocialNetworks.push(name);
        setValue("socialNetworks", newSocialNetworks);
      } else {
        const newSocialNetworks = getValues("socialNetworks")
          ? getValues("socialNetworks")
          : [];
        newSocialNetworks.splice(newSocialNetworks.indexOf(name), 1);
        setValue("socialNetworks", newSocialNetworks);
      }
    } else {
      setValue("socialNetworks", [name]);
    }
    trigger("socialNetworks");
  }

  // Amount Posts calculator functions
  function confirmSelecetedDay(day: number) {
    switch (day) {
      case 0:
        return getValues("weekDays").includes("Sunday");
      case 1:
        return getValues("weekDays").includes("Monday");
      case 2:
        return getValues("weekDays").includes("Tuesday");
      case 3:
        return getValues("weekDays").includes("Wednesday");
      case 4:
        return getValues("weekDays").includes("Thursday");
      case 5:
        return getValues("weekDays").includes("Friday");
      case 6:
        return getValues("weekDays").includes("Saturday");
      default:
        return false;
    }
  }
  function totalPosts() {
    let totalPosts = 0;
    if (!getValues("startDate")) {
      setValue("startDate", today);
    }
    if (!getValues("endDate")) {
      setValue("endDate", tomorrow);
    }
    const datesInterval = eachDayOfInterval({
      start: getValues("startDate"),
      end: getValues("endDate"),
    });
    datesInterval.forEach((date) => {
      if (confirmSelecetedDay(date.getDay())) {
        totalPosts++;
      }
    });
    return (
      totalPosts * getValues("postsPerDay") * getValues("socialNetworks").length
    );
  }

  function changeFormat(
    option: "1:1" | "9:16" | "4:5" | "5:7" | "3:4" | "3:5" | "2:3",
  ) {
    if (getValues("format") === option) {
      setValue("format", "");
    } else {
      setValue("format", option);
    }
    trigger("format");
  }

  function changeStyle(
    option:
      | "Realistic"
      | "Abstract"
      | "Aquarelle"
      | "SciFi"
      | "Fantasy"
      | "3d"
      | "Painting"
      | "Portrait",
  ) {
    if (getValues("style") === option) {
      setValue("style", "");
    } else {
      setValue("style", option);
    }
    trigger("style");
  }

  function confirmSubmition() {
    setValue("amountPosts", totalPosts());
    showModal();
  }

  return (
    <>
      <Heading size="3" className="flex items-center mt-14">
        <MegaphoneIcon className="w-6 mr-1 stroke-[0.13rem]" />
        {t("create-campaign.title")}
      </Heading>
      <div className="bg-white w-full mx-auto p-4 rounded-xl mt-4">
        <Modal
          show={openModal}
          onClose={() => setOpenModal(false)}
          dismissible
          size="md"
        >
          <ModalHeader>
            <RoundedEyeIcon className="mx-auto" />
            <Heading size="3" className="mt-6">
              {t("create-campaign.modal.title")}
            </Heading>
          </ModalHeader>
          <ModalBody>
            <Heading size="4" className="mt-[-1rem]">
              Aquisição de Alunos
            </Heading>
            <div className="flex items-center gap-2 mt-2">
              <TicketIcon height="1.5rem" />
              <Text className="font-outfit font-semibold">
                {t("create-campaign.modal.credits")}
              </Text>
              <Text className="font-outfit font-semibold text-yellow-900 ml-[-.4rem]">
                30540
              </Text>
            </div>
            <hr className="my-4 bg-gray-700 h-[2px]" />
            <div className="flex justify-between">
              <Text className="font-outfit font-semibold">
                {t("create-campaign.modal.client")}
              </Text>
              <Text className="font-outfit font-semibold">Eric Personal</Text>
            </div>
            <div className="flex justify-between mt-4">
              <Text className="font-outfit font-semibold">
                {t("create-campaign.modal.total-posts")}
              </Text>
              <Text className="font-outfit font-semibold">
                {getValues("amountPosts")}
              </Text>
            </div>
            <div className="flex justify-between mt-4">
              <Text className="font-outfit font-semibold">
                {t("create-campaign.modal.used-credits")}
              </Text>
              <Text className="font-outfit font-semibold">-28440</Text>
            </div>
            <hr className="mt-4 bg-gray-700 h-[2px]" />
          </ModalBody>
          <ModalFooter>
            <Button color="secondary" onClick={() => setOpenModal(false)}>
              {t("create-campaign.modal.cancel")}
            </Button>
            <Button
              className="flex items-center justify-center gap-2 h-10"
              onClick={handleSubmit(onFinished, showModal)}
            >
              {t("create-campaign.generate-post")}{" "}
              <SparklesIcon width="1.5rem" />
            </Button>
          </ModalFooter>
        </Modal>
        <Heading size="4">
          {t("create-campaign.form.blocks-titles.campaign-data")}
        </Heading>
        <div className="grid lg:grid-cols-10 grid-cols-2 gap-4 mt-4 items-top">
          <div className="lg:col-span-3">
            <Text className="font-outfit mb-1 font-semibold">
              {t("create-campaign.form.labels.area")}
            </Text>
            <Input
              {...register("area")}
              state={errors.area ? "error" : "default"}
            />
            {errors.area && (
              <div className="flex gap-1 ml-1 mt-1 items-center">
                <ExclamationTriangleIcon className="h-4 text-red-600" />
                <Text className="text-red-600 text-left">
                  {errors.area.message}
                </Text>
              </div>
            )}
          </div>
          <div className="lg:col-span-3">
            <Text className="font-outfit mb-1 font-semibold">
              {t("create-campaign.form.labels.objective")}
            </Text>
            <Input
              {...register("objective")}
              state={errors.objective ? "error" : "default"}
            />
            {errors.objective && (
              <div className="flex gap-1 ml-1 mt-1 items-center">
                <ExclamationTriangleIcon className="h-4 text-red-600" />
                <Text className="text-red-600 text-left">
                  {errors.objective.message}
                </Text>
              </div>
            )}
          </div>
          <div className="lg:col-span-2">
            <Text className="mb-1 font-outfit font-semibold">
              {t("create-campaign.form.labels.start-date")}
            </Text>
            <DatePicker
              minDate={today}
              onSelectedDateChanged={(date) => setValue("startDate", date)}
            />
          </div>
          <div className="lg:col-span-2">
            <Text className="mb-1 font-outfit font-semibold">
              {t("create-campaign.form.labels.end-date")}
            </Text>
            <DatePicker
              minDate={tomorrow}
              onSelectedDateChanged={(date) => setValue("endDate", date)}
            />
          </div>
        </div>
        <div className="flex gap-4 mt-4 items-start flex-wrap">
          <div>
            <Text className="mb-1 font-outfit font-semibold">
              {t("create-campaign.form.labels.posts-per-day")}
            </Text>
            <Dropdown
              loadOptions={(inputValue, callback) =>
                loadOptions(inputValue, callback, amountPostsOptions)
              }
              onChange={(e) => setAmountPosts(Number(e?.value))}
              variant={errors.postsPerDay ? "error" : "default"}
            />
          </div>
          {postsDays &&
            getValues("postsHours").map((_, index) => (
              <div key={`post-hour-${index}`}>
                <Text className="mb-1 font-outfit font-semibold">
                  {t("create-campaign.form.labels.post-hours")}
                  {index + 1}
                </Text>
                <Dropdown
                  loadOptions={(inputValue, callback) =>
                    loadOptions(inputValue, callback, postsHoursOptions)
                  }
                  onChange={(e) => e && addNewHour(index, e.value)}
                />
              </div>
            ))}
        </div>
        {errors.postsPerDay ? (
          <div className="flex gap-1 ml-1 mt-1 items-center">
            <ExclamationTriangleIcon className="h-4 text-red-600" />
            <Text className="text-red-600 text-left">
              {errors.postsPerDay.message}
            </Text>
          </div>
        ) : (
          errors.postsHours && (
            <div className="flex gap-1 ml-1 mt-1 items-center">
              <ExclamationTriangleIcon className="h-4 text-red-600" />
              <Text className="text-red-600 text-left">
                {errors.postsHours.message}
              </Text>
            </div>
          )
        )}
        <div className="flex items-end gap-4 mt-4 flex-wrap">
          <div>
            <Text className="mb-1 font-outfit font-semibold">
              {t("create-campaign.form.labels.week-days")}
            </Text>
            <Dropdown
              loadOptions={(inputValue, callback) =>
                loadOptions(inputValue, callback, weekDaysOptions)
              }
              onChange={(e) => addNewWeekDay(e?.value ? e.value : "")}
              variant={errors.weekDays ? "error" : "default"}
            />
          </div>
          {weekDays &&
            weekDays.map((value, index) => (
              <div key={`week-days-${index}-${value}`}>
                <Tag>
                  <Text className="font-normal font-outfit">
                    {t(`create-campaign.form.week-days.${value}`)}
                  </Text>
                  <XCircleIcon
                    className="h-6 text-gray-800 cursor-pointer"
                    onClick={() => removeWeekDay(index)}
                  />
                </Tag>
              </div>
            ))}
        </div>
        {errors.weekDays && (
          <div className="flex gap-1 ml-1 mt-1 items-center">
            <ExclamationTriangleIcon className="h-4 text-red-600" />
            <Text className="text-red-600 text-left">
              {errors.weekDays.message}
            </Text>
          </div>
        )}
        <Heading size="4" className="mt-8">
          {t("create-campaign.form.blocks-titles.format")}
        </Heading>
        <div className="flex items-end gap-4 flex-wrap mt-4">
          <div className="px-4">
            <Card
              color="quaternary"
              fullWidth
              className="cursor-pointer rounded-none border"
              onClick={() => changeFormat("1:1")}
              checked={format && format === "1:1" ? true : false}
            >
              <Image
                src={formatDummy}
                className={`w-20 h-20 transition-opacity duration-500 ${
                  format && format != "1:1" && "opacity-25 "
                }`}
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              1:1
            </Text>
          </div>
          <div className="px-4">
            <Card
              color="quaternary"
              fullWidth
              className="cursor-pointer rounded-none border"
              onClick={() => changeFormat("9:16")}
              checked={format && format === "9:16" ? true : false}
            >
              <Image
                src={formatDummy}
                className={`w-20 h-[8.88298rem] transition-opacity duration-500 ${
                  format && format != "9:16" && "opacity-25 "
                }`}
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              9:16
            </Text>
          </div>
          <div className="px-4">
            <Card
              color="quaternary"
              fullWidth
              className="cursor-pointer rounded-none border"
              onClick={() => changeFormat("4:5")}
              checked={format && format === "4:5" ? true : false}
            >
              <Image
                src={formatDummy}
                className={`w-20 h-[6.25rem] transition-opacity duration-500 ${
                  format && format != "4:5" && "opacity-25 "
                }`}
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              4:5
            </Text>
          </div>
          <div className="px-4">
            <Card
              color="quaternary"
              fullWidth
              className="cursor-pointer rounded-none border"
              onClick={() => changeFormat("5:7")}
              checked={format && format === "5:7" ? true : false}
            >
              <Image
                src={formatDummy}
                className={`w-20 h-28 transition-opacity duration-500 ${
                  format && format != "5:7" && "opacity-25 "
                }`}
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              5:7
            </Text>
          </div>
          <div className="px-4">
            <Card
              color="quaternary"
              fullWidth
              className="cursor-pointer rounded-none border"
              onClick={() => changeFormat("3:4")}
              checked={format && format === "3:4" ? true : false}
            >
              <Image
                src={formatDummy}
                className={`w-20 h-[6.66667rem] transition-opacity duration-500 ${
                  format && format != "3:4" && "opacity-25 "
                }`}
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              3:4
            </Text>
          </div>
          <div className="px-4">
            <Card
              color="quaternary"
              fullWidth
              className="cursor-pointer rounded-none border"
              onClick={() => changeFormat("3:5")}
              checked={format && format === "3:5" ? true : false}
            >
              <Image
                src={formatDummy}
                className={`w-20 h-[8.3333rem] transition-opacity duration-500 ${
                  format && format != "3:5" && "opacity-25 "
                }`}
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              3:5
            </Text>
          </div>
          <div className="px-4">
            <Card
              color="quaternary"
              fullWidth
              className="cursor-pointer rounded-none border"
              onClick={() => changeFormat("2:3")}
              checked={format && format === "2:3" ? true : false}
            >
              <Image
                src={formatDummy}
                className={`w-20 h-[7.5rem] transition-opacity duration-500 ${
                  format && format != "2:3" && "opacity-25 "
                }`}
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              2:3
            </Text>
          </div>
        </div>
        {errors.format && (
          <div className="flex gap-1 ml-1 mt-1 items-center">
            <ExclamationTriangleIcon className="h-4 text-red-600" />
            <Text className="text-red-600 text-left">
              {errors.format.message}
            </Text>
          </div>
        )}
        <Heading size="4" className="mt-8">
          {t("create-campaign.form.blocks-titles.publish-in")}
        </Heading>
        <div className="flex items-center gap-4 flex-wrap mt-4">
          <div>
            <Card
              className="p-6 cursor-pointer"
              checked={findSocialNetwork("Facebook")}
              onClick={() => setSocialNetwork("Facebook")}
            >
              <RoundedFacebookIconLarge
                grayscale={
                  socialNetworks
                    ? socialNetworks.length > 0
                      ? !findSocialNetwork("Facebook")
                      : false
                    : false
                }
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              Facebook
            </Text>
          </div>
          <div>
            <Card
              className="p-6 cursor-pointer"
              checked={findSocialNetwork("Instagram")}
              onClick={() => setSocialNetwork("Instagram")}
            >
              <RoundedInstagramIconLarge
                grayscale={
                  socialNetworks
                    ? socialNetworks.length > 0
                      ? !findSocialNetwork("Instagram")
                      : false
                    : false
                }
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              Instagram
            </Text>
          </div>
          <div>
            <Card
              className="p-6 cursor-pointer"
              checked={findSocialNetwork("LinkedIn")}
              onClick={() => setSocialNetwork("LinkedIn")}
            >
              <RoundedLinkedinIconLarge
                grayscale={
                  socialNetworks
                    ? socialNetworks.length > 0
                      ? !findSocialNetwork("LinkedIn")
                      : false
                    : false
                }
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              Linkedin
            </Text>
          </div>
          <div>
            <Card
              className="p-6 cursor-pointer"
              checked={findSocialNetwork("X")}
              onClick={() => setSocialNetwork("X")}
            >
              <RoundedXIconLarge
                grayscale={
                  socialNetworks
                    ? socialNetworks.length > 0
                      ? !findSocialNetwork("X")
                      : false
                    : false
                }
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              X
            </Text>
          </div>
          <div>
            <Card
              className="p-6 cursor-pointer"
              checked={findSocialNetwork("TikTok")}
              onClick={() => setSocialNetwork("TikTok")}
            >
              <RoundedTikTokIconLarge
                grayscale={
                  socialNetworks
                    ? socialNetworks.length > 0
                      ? !findSocialNetwork("TikTok")
                      : false
                    : false
                }
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              Tik Tok
            </Text>
          </div>
          <div>
            <Card
              className="p-6 cursor-pointer"
              checked={findSocialNetwork("Youtube")}
              onClick={() => setSocialNetwork("Youtube")}
            >
              <RoundedYoutubeIconLarge
                grayscale={
                  socialNetworks
                    ? socialNetworks.length > 0
                      ? !findSocialNetwork("Youtube")
                      : false
                    : false
                }
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              Youtube
            </Text>
          </div>
          <div>
            <Card
              className="p-6 cursor-pointer"
              checked={findSocialNetwork("WhatsApp")}
              onClick={() => setSocialNetwork("WhatsApp")}
            >
              <RoundedWhatsAppIconLarge
                grayscale={
                  socialNetworks
                    ? socialNetworks.length > 0
                      ? !findSocialNetwork("WhatsApp")
                      : false
                    : false
                }
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              WhatsApp
            </Text>
          </div>
        </div>
        {errors.socialNetworks && (
          <div className="flex gap-1 items-center">
            <ExclamationTriangleIcon className="h-4 text-red-600" />
            <Text className="text-red-600 text-left">
              {errors.socialNetworks.message}
            </Text>
          </div>
        )}
        <Heading size="4" className="mt-8">
          {t("create-campaign.form.blocks-titles.style")}
        </Heading>
        <div className="flex items-center gap-4 flex-wrap mt-4">
          <div>
            <Card
              fullWidth
              color="quaternary"
              className="cursor-pointer rounded-none border"
              checked={style && style === "Realistic" ? true : false}
              onClick={() => changeStyle("Realistic")}
            >
              <Image
                src={formatDummy}
                className={`w-[7.20rem] h-[7.20rem] transition-opacity duration-500 ${
                  style && style != "Realistic" && "opacity-25"
                }`}
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              {t("create-campaign.form.styles.realistic")}
            </Text>
          </div>
          <div>
            <Card
              fullWidth
              color="quaternary"
              className="cursor-pointer rounded-none border"
              checked={style && style === "Abstract" ? true : false}
              onClick={() => changeStyle("Abstract")}
            >
              <Image
                src={formatDummy}
                className={`w-[7.20rem] h-[7.20rem] transition-opacity duration-500 ${
                  style && style != "Abstract" && "opacity-25"
                }`}
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              {t("create-campaign.form.styles.abstract")}
            </Text>
          </div>
          <div>
            <Card
              fullWidth
              color="quaternary"
              className="cursor-pointer rounded-none border"
              checked={style && style === "Aquarelle" ? true : false}
              onClick={() => changeStyle("Aquarelle")}
            >
              <Image
                src={formatDummy}
                className={`w-[7.20rem] h-[7.20rem] transition-opacity duration-500 ${
                  style && style != "Aquarelle" && "opacity-25"
                }`}
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              {t("create-campaign.form.styles.aquarelle")}
            </Text>
          </div>
          <div>
            <Card
              fullWidth
              color="quaternary"
              className="cursor-pointer rounded-none border"
              checked={style && style === "SciFi" ? true : false}
              onClick={() => changeStyle("SciFi")}
            >
              <Image
                src={formatDummy}
                className={`w-[7.20rem] h-[7.20rem] transition-opacity duration-500 ${
                  style && style != "SciFi" && "opacity-25"
                }`}
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              {t("create-campaign.form.styles.sciFi")}
            </Text>
          </div>
          <div>
            <Card
              fullWidth
              color="quaternary"
              className="cursor-pointer rounded-none border"
              checked={style && style === "Fantasy" ? true : false}
              onClick={() => changeStyle("Fantasy")}
            >
              <Image
                src={formatDummy}
                className={`w-[7.20rem] h-[7.20rem] transition-opacity duration-500 ${
                  style && style != "Fantasy" && "opacity-25"
                }`}
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              {t("create-campaign.form.styles.fantasy")}
            </Text>
          </div>
          <div>
            <Card
              fullWidth
              color="quaternary"
              className="cursor-pointer rounded-none border"
              checked={style && style === "3d" ? true : false}
              onClick={() => changeStyle("3d")}
            >
              <Image
                src={formatDummy}
                className={`w-[7.20rem] h-[7.20rem] transition-opacity duration-500 ${
                  style && style != "3d" && "opacity-25"
                }`}
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              {t("create-campaign.form.styles.3d")}
            </Text>
          </div>
          <div>
            <Card
              fullWidth
              color="quaternary"
              className="cursor-pointer rounded-none border"
              checked={style && style === "Painting" ? true : false}
              onClick={() => changeStyle("Painting")}
            >
              <Image
                src={formatDummy}
                className={`w-[7.20rem] h-[7.20rem] transition-opacity duration-500 ${
                  style && style != "Painting" && "opacity-25"
                }`}
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              {t("create-campaign.form.styles.painting")}
            </Text>
          </div>
          <div>
            <Card
              fullWidth
              color="quaternary"
              className="cursor-pointer rounded-none border"
              checked={style && style === "Portrait" ? true : false}
              onClick={() => changeStyle("Portrait")}
            >
              <Image
                src={formatDummy}
                className={`w-[7.20rem] h-[7.20rem] transition-opacity duration-500 ${
                  style && style != "Portrait" && "opacity-25"
                }`}
              />
            </Card>
            <Text className="font-outfit font-semibold w-fit mt-2 mx-auto">
              {t("create-campaign.form.styles.portrait")}
            </Text>
          </div>
        </div>
        {errors.style && (
          <div className="flex gap-1 items-center">
            <ExclamationTriangleIcon className="h-4 text-red-600" />
            <Text className="text-red-600 text-left">
              {errors.style.message}
            </Text>
          </div>
        )}
        <Button
          className="flex items-center gap-2 w-fit ml-auto mr-8 mt-4"
          onClick={handleSubmit(onValidate)}
        >
          {t("create-campaign.generate-post")} <SparklesIcon width="1.5rem" />
        </Button>
      </div>
    </>
  );
}

export default CampaignBlocks;
