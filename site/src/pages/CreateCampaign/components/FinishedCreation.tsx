import { Heading, Image, Text } from "@/components";
import generatingIcon from "@/assets/generatingPost.svg";
import { Trans, useTranslation } from "react-i18next";

export type FinishedCreationProps = {
  amountPosts?: number;
};

export function FinishedCreation({ amountPosts }: FinishedCreationProps) {
  const { t } = useTranslation();

  return (
    <>
      <Heading size="3" className="max-w-xs mx-auto text-center mt-36">
        {t("create-campaign.finished.title")}
      </Heading>
      <Text className="max-w-sm mx-auto text-center font-600 font-outfit mt-8">
        <Trans
          i18nKey="create-campaign.finished.description"
          values={{ amountPosts }}
        >
          *** <strong className="text-yellow-900 font-semibold">***</strong> ***
        </Trans>
      </Text>
      <Image src={generatingIcon} className="mx-auto mt-8" />
      <Text className="w-fit mx-auto font-600 font-outfit text-base mt-8">
        {t("create-campaign.finished.generating-posts")}
      </Text>
    </>
  );
}

export default FinishedCreation;
