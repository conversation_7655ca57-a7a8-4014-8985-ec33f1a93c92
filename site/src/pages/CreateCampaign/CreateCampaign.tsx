import { MainLayout } from "@/layout/MainLayout";
import { useState } from "react";
import CampaignBlocks from "./components/CampaignBlocks";
import FinishedCreation from "./components/FinishedCreation";
import { Inputs } from "./components/CampaignBlocks";

export function CreateCampaign() {
  const [values, setValues] = useState<Inputs>();

  function handleOnFinished(data: Inputs) {
    setValues(data);
    console.log(data);
  }

  return (
    <MainLayout>
      <div className="container mx-auto" data-testeid="create-campaign">
        {values ? (
          <FinishedCreation amountPosts={values.amountPosts} />
        ) : (
          <CampaignBlocks onFinished={handleOnFinished} />
        )}
      </div>
    </MainLayout>
  );
}

export default CreateCampaign;
