import { fireEvent, render, screen, waitFor } from "@tests/render";
import ResetPassword from "./ResetPassword";

describe("<ResetPassword />", () => {
  it("should render the page", () => {
    render(<ResetPassword onSend={jest.fn()} isLoading={false} />);
    expect(screen.getByTestId("reset-password")).toMatchSnapshot();
  });

  it("should validate the form fields", async () => {
    const onSend = jest.fn();
    render(<ResetPassword isLoading={false} onSend={onSend} />);

    const emailInput = screen.getByPlaceholderText(
      /reset-password.form.placeholders.email/i,
    );
    const submitBtn = screen.getByTestId("reset-btn");

    fireEvent.change(emailInput, {
      target: { value: "invalid-email" },
    });

    fireEvent.submit(submitBtn);

    await waitFor(() => {
      expect(
        screen.getByText("reset-password.form.errors.invalid-email"),
      ).toBeTruthy();
      expect(onSend).toHaveBeenCalledTimes(0);
    });
  });

  it("should show the error", async () => {
    const onSend = jest.fn();
    const error = new Error("Invalid");
    render(<ResetPassword onSend={onSend} isLoading={false} error={error} />);

    expect(screen.getByText("Invalid")).toBeTruthy();
  });

  it("should call onSend with correct data when form is submitted with valid data", async () => {
    const onSend = jest.fn();
    render(<ResetPassword onSend={onSend} isLoading={false} />);

    const emailInput = screen.getByPlaceholderText(
      /reset-password.form.placeholders.email/i,
    );
    const submitBtn = screen.getByTestId("reset-btn");

    fireEvent.change(emailInput, {
      target: { value: "<EMAIL>" },
    });

    fireEvent.submit(submitBtn);
    await waitFor(() => {
      expect(onSend).toHaveBeenCalledWith(
        { email: "<EMAIL>" },
        expect.objectContaining({}),
      );
    });
  });
});
