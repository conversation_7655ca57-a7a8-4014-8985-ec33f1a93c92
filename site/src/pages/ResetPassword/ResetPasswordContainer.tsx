import { useNavigate } from "react-router-dom";
import ResetPassword from "./ResetPassword";
import { useMutation } from "@tanstack/react-query";
import { callApi } from "@/utils";
import { RESET_PASSWORD } from "@/queries/resetPassword";
import { useState } from "react";

export type ResetPasswordParam = {
  email: string;
};

export function ResetPasswordContainer() {
  const [email, setEmail] = useState("");
  const navigate = useNavigate();

  const { mutate, error, isPending } = useMutation<
    unknown,
    Error,
    ResetPasswordParam
  >({
    mutationFn: (data: ResetPasswordParam) => callApi(RESET_PASSWORD, data),
    onSuccess: () =>
      navigate("/reset-password-confirm", {
        state: {
          email,
        },
      }),
  });

  const onSend = (data: ResetPasswordParam) => {
    setEmail(data.email);
    mutate(data);
  };

  return <ResetPassword onSend={onSend} isLoading={isPending} error={error!} />;
}

export default ResetPasswordContainer;
