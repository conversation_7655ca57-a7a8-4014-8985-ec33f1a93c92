import { Button, Heading, Input, Text, Image } from "@/components";
import { ArrowRightIcon } from "@heroicons/react/24/outline";
import { ExclamationTriangleIcon } from "@heroicons/react/24/solid";
import { yupResolver } from "@hookform/resolvers/yup";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import * as yup from "yup";
import logo from "@/assets/logo.svg";
import { ResetPasswordParam } from "./ResetPasswordContainer";

export type ResetPasswordProps = {
  onSend: (params: ResetPasswordParam) => void;
  isLoading: boolean;
  error?: Error;
};

export function ResetPassword({
  onSend,
  isLoading,
  error,
}: ResetPasswordProps) {
  const { t } = useTranslation();

  const schema = yup.object().shape({
    email: yup
      .string()
      .required(t("reset-password.form.errors.required"))
      .email(t("reset-password.form.errors.invalid-email")),
  });

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm<ResetPasswordParam>({
    resolver: yupResolver(schema),
    delayError: 1000,
    mode: "onChange",
    shouldFocusError: true,
  });

  useEffect(() => {
    if (error) {
      setError("email", {
        message: error.message,
      });
    }
  }, [error]);

  return (
    <div
      data-testid="reset-password"
      className="flex h-screen place-items-center justify-center px-4 overflow-x-hidden before:content-[''] before:opacity-35 before:blur-3xl before:fixed before:top-[0] before:left-[-20%] before:w-[2000px] before:h-[2000px] before:rounded-full before:block before:z-[-2] before:bg-amber-100 before:border-solid before:border-[200px] before:border-amber-50 before:opacity-80
  after:blur-3xl after:content-[''] after:fixed after:top-[180px] after:right-[-35%] after:w-[2000px] after:h-[2000px] after:rounded-full after:block after:z-[-1] after:bg-slate-300 after:border-solid after:border-[200px] after:border-slate-5 after:opacity-50"
    >
      <div className="flex flex-col items-center text-center w-[30rem] sm:p-20 p-6 rounded-2xl bg-white">
        <Image src={logo} alt="Conteoodo" className="w-[10rem]" />

        <form className="w-full" onSubmit={handleSubmit(onSend)}>
          <Heading size="3" className="mt-[1.5rem]">
            {t("reset-password.title")}
          </Heading>
          <Text className="mt-[1.5rem] leading-4">
            {t("reset-password.description")}
          </Text>
          <label
            className="block text-black-900 text-[0.625rem] text-left font-semibold mb-2 mt-[1.5rem]"
            htmlFor="email"
          >
            {t("reset-password.form.label")}
          </label>
          <Input
            placeholder={t("reset-password.form.placeholders.email")}
            state={errors.email ? "error" : "default"}
            id="email"
            autoComplete="email"
            {...register("email")}
          />
          {errors.email && (
            <div className="flex flex-row gap-1 ml-1 mt-1 items-center">
              <ExclamationTriangleIcon className="h-4 text-red-700" />
              <Text className="text-red-700 text-left">
                {errors.email.message}
              </Text>
            </div>
          )}
          <Button
            className="mt-[1.5rem] flex justify-center items-center text-xs gap-2"
            type="submit"
            disabled={isLoading}
            data-testid="reset-btn"
          >
            {t("reset-password.form.send")}
            <ArrowRightIcon className="w-[1.125rem] stroke-[0.15rem]" />
          </Button>
        </form>
      </div>
    </div>
  );
}

export default ResetPassword;
