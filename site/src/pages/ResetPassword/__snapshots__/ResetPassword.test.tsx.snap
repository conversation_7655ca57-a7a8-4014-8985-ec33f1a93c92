// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<ResetPassword /> should render the page 1`] = `
<div
  class="flex h-screen place-items-center justify-center px-4 overflow-x-hidden before:content-[''] before:opacity-35 before:blur-3xl before:fixed before:top-[0] before:left-[-20%] before:w-[2000px] before:h-[2000px] before:rounded-full before:block before:z-[-2] before:bg-amber-100 before:border-solid before:border-[200px] before:border-amber-50 before:opacity-80 after:blur-3xl after:content-[''] after:fixed after:top-[180px] after:right-[-35%] after:w-[2000px] after:h-[2000px] after:rounded-full after:block after:z-[-1] after:bg-slate-300 after:border-solid after:border-[200px] after:border-slate-5 after:opacity-50"
  data-testid="reset-password"
>
  <div
    class="flex flex-col items-center text-center w-[30rem] sm:p-20 p-6 rounded-2xl bg-white"
  >
    <img
      alt="Conteoodo"
      class="w-[10rem]"
      src=""
    />
    <form
      class="w-full"
    >
      <h1
        class="font-outfit font-[700] text-[1.5rem] mt-[1.5rem]"
      >
        reset-password.title
      </h1>
      <p
        class="font-rubik font-[400] text-[0.75rem] mt-[1.5rem] leading-4"
      >
        reset-password.description
      </p>
      <label
        class="block text-black-900 text-[0.625rem] text-left font-semibold mb-2 mt-[1.5rem]"
        for="email"
      >
        reset-password.form.label
      </label>
      <div
        class="relative flex flex-row items-center w-full"
        data-testid="input"
      >
        <input
          autocomplete="email"
          class="py-[0.9375rem] px-[0.8125rem] text-xs border rounded-lg bg-white w-full h-10 disabled:border-gray-200 focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none border-gray-700"
          id="email"
          name="email"
          placeholder="reset-password.form.placeholders.email"
        />
      </div>
      <button
        class="w-full py-3 px-4 rounded-md font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white mt-[1.5rem] flex justify-center items-center text-xs gap-2"
        data-testid="reset-btn"
        type="submit"
      >
        reset-password.form.send
        <svg
          aria-hidden="true"
          class="w-[1.125rem] stroke-[0.15rem]"
          data-slot="icon"
          fill="none"
          stroke="currentColor"
          stroke-width="1.5"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </form>
  </div>
</div>
`;
