// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Home /> should render the page 1`] = `
<div
  class="w-full font-outfit text-center  before:content-[''] before:opacity-35 before:blur-3xl before:fixed before:top-[0] before:left-[-20%] before:w-[2000px] before:h-[2000px] before:rounded-full before:block before:z-[-2] before:bg-amber-100 before:border-solid before:border-[200px] before:border-amber-50 before:opacity-80 after:blur-3xl after:content-[''] after:fixed after:top-[180px] after:right-[-35%] after:w-[2000px] after:h-[2000px] after:rounded-full after:block after:z-[-1] after:bg-slate-300 after:border-solid after:border-[200px] after:border-slate-5 after:opacity-80 "
  data-testid="home"
>
  <nav
    aria-hidden="true"
    data-floating-ui-inert=""
  >
    <div
      class="relative flex h-14 items-center justify-between px-6"
    >
      <a
        href="/"
      >
        <img
          alt="Conteoodo"
          class="h-7"
          src=""
        />
      </a>
      <div
        class="right-0 flex gap-12 items-center pr-2"
      >
        <a
          href="/sign-in"
        >
          <p
            class="bg-transparent no-underline font-outfit font-medium cursor-pointer"
          >
            home.buttons.login
          </p>
        </a>
        <a
          href="/sign-up"
        >
          <button
            class="w-full py-3 px-4 rounded-md font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-xs whitespace-nowrap"
          >
            home.buttons.sign-up
          </button>
        </a>
      </div>
    </div>
  </nav>
  <div
    aria-hidden="true"
    class="lg:max-w-[1246px] md:max-w-[1000px] mx-auto"
    data-floating-ui-inert=""
  >
    <div
      class="lg:mt-20 sm:mt-14 mt-6 text-center lg:px-8 px-4"
    >
      <div
        class="aos-init aos-animate"
        data-aos="fade-up"
        data-aos-delay="200"
      >
        <h1
          class="font-outfit font-[700] tracking-[-0.0375rem] lg:text-[2.5rem] text-[2rem] leading-10"
        >
          home.title
        </h1>
        <p
          class="font-outfit font-semibold mt-6 text-base max-sm:leading-[1.125rem]"
        >
          home.description
        </p>
      </div>
      <div
        class="flex justify-center lg:mt-14 sm:mt-10 mt-[0.85rem]"
      >
        <div
          class="flex shrink md:gap-4 min-[370px]:gap-2 gap-1 justify-items-center"
        >
          <div
            class="flex items-center justify-center text-center p-2 aos-init aos-animate"
            data-aos="fade-left"
            data-aos-delay="300"
          >
            <img
              alt="Instagram"
              class="sm:h-[2rem] min-[300px]:h-[1.5rem] h-[1.15rem] sm:w-[2rem] min-[300px]:w-[1.5rem] w-[1.15rem] mx-2"
              src=""
            />
          </div>
          <div
            class="flex items-center justify-center text-center p-2 aos-init aos-animate"
            data-aos="fade-left"
            data-aos-delay="350"
          >
            <img
              alt="Twitter"
              class="sm:h-[2rem] min-[300px]:h-[1.5rem] h-[1.15rem] sm:w-[2rem] min-[300px]:w-[1.5rem] w-[1.15rem] mx-1"
              src=""
            />
          </div>
          <div
            class="flex items-center justify-center text-center p-2 aos-init aos-animate"
            data-aos="fade-left"
            data-aos-delay="400"
          >
            <img
              alt="Facebook"
              class="sm:h-[2.4rem] min-[300px]:h-[1.78rem] h-[1.4rem] sm:w-[2.4rem] min-[300px]:w-[1.78rem] w-[1.4rem]"
              src=""
            />
          </div>
          <div
            class="flex items-center justify-center text-center p-2 aos-init aos-animate"
            data-aos="fade-left"
            data-aos-delay="450"
          >
            <img
              alt="TikTok"
              class="sm:h-[2.4rem] min-[300px]:h-[1.78rem] h-[1.4rem] sm:w-[2.4rem] min-[300px]:w-[1.78rem] w-[1.4rem]"
              src=""
            />
          </div>
          <div
            class="flex items-center justify-center text-center p-2 aos-init aos-animate"
            data-aos="fade-left"
            data-aos-delay="500"
          >
            <img
              alt="Linkedin"
              class="sm:h-[2.4rem] min-[300px]:h-[1.78rem] h-[1.4rem] sm:w-[2.4rem] min-[300px]:w-[1.78rem] w-[1.4rem]"
              src=""
            />
          </div>
          <div
            class="flex items-center justify-center text-center p-2 aos-init aos-animate"
            data-aos="fade-left"
            data-aos-delay="550"
          >
            <img
              alt="Youtube"
              class="sm:h-[2.4rem] min-[300px]:h-[1.78rem] h-[1.4rem] sm:w-[2.4rem] min-[300px]:w-[1.78rem] w-[1.4rem]"
              src=""
            />
          </div>
        </div>
      </div>
      <div
        class="bg-white pl-[1.35rem] pr-[1.35rem] pt-4 pb-4 lg:mt-14 mt-8 h-fit lg:w-full sm:w-[80%] mx-auto rounded-lg aos-init aos-animate"
        data-aos="fade"
        data-aos-delay="600"
      >
        <form
          class="flex lg:flex-row flex-col flex-grow lg:gap-6 sm:gap-2 gap-6"
        >
          <div
            class="min-[1250px]:w-full lg:w-[60%] md:w-full flex sm:flex-row flex-col sm:gap-6 gap-6"
          >
            <div
              class="text-left sm:w-3/5 w-full"
            >
              <label
                class="block text-black-900 text-sm font-bold mb-1 whitespace-nowrap"
                for="campaign"
              >
                home.label.area
              </label>
              <div
                class="relative flex flex-row items-center w-full"
                data-testid="input"
              >
                <input
                  class="py-[0.9375rem] px-[0.8125rem] text-xs border rounded-lg w-full h-10 disabled:border-gray-200 focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none border-gray-700 bg-gray-100 border-none"
                  name="area"
                  placeholder="home.placeholders.area"
                  required=""
                  value=""
                />
              </div>
            </div>
            <div
              class="text-left sm:w-3/5 w-full"
            >
              <label
                class="block text-black-900 text-sm font-bold mb-1"
                for="campaign"
              >
                home.label.objective
              </label>
              <div
                class="relative flex flex-row items-center w-full"
                data-testid="input"
              >
                <input
                  class="py-[0.9375rem] px-[0.8125rem] text-xs border rounded-lg w-full h-10 disabled:border-gray-200 focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none border-gray-700 bg-gray-100 border-none"
                  name="campaign"
                  placeholder="home.placeholders.objective"
                  required=""
                  value=""
                />
              </div>
            </div>
          </div>
          <div
            class="flex sm:flex-row flex-col w-full"
          >
            <div
              class="text-left w-full"
            >
              <label
                class="block text-black-900 text-sm font-bold mb-1"
                for="campaign"
              >
                home.label.period
              </label>
              <div
                class="flex sm:flex-row flex-col max-sm:w-full sm:items-center sm:justify-center sm:gap-[0.69rem] gap-1"
              >
                <div
                  class="relative rounded-lg bg-gray-100 border-none"
                >
                  <div
                    class="flex"
                  >
                    <div
                      class="relative w-full"
                    >
                      <div
                        class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"
                      >
                        <svg
                          fill="none"
                          height="16"
                          viewBox="0 0 16 16"
                          width="16"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8.00016 12.6667C8.13202 12.6667 8.26091 12.6276 8.37054 12.5543C8.48018 12.4811 8.56562 12.3769 8.61608 12.2551C8.66654 12.1333 8.67974 11.9993 8.65402 11.8699C8.6283 11.7406 8.5648 11.6218 8.47157 11.5286C8.37833 11.4354 8.25954 11.3719 8.13022 11.3461C8.0009 11.3204 7.86686 11.3336 7.74504 11.3841C7.62322 11.4345 7.5191 11.52 7.44585 11.6296C7.3726 11.7392 7.3335 11.8681 7.3335 12C7.3335 12.1768 7.40373 12.3464 7.52876 12.4714C7.65378 12.5964 7.82335 12.6667 8.00016 12.6667ZM11.3335 12.6667C11.4654 12.6667 11.5942 12.6276 11.7039 12.5543C11.8135 12.4811 11.899 12.3769 11.9494 12.2551C11.9999 12.1333 12.0131 11.9993 11.9874 11.8699C11.9616 11.7406 11.8981 11.6218 11.8049 11.5286C11.7117 11.4354 11.5929 11.3719 11.4636 11.3461C11.3342 11.3204 11.2002 11.3336 11.0784 11.3841C10.9566 11.4345 10.8524 11.52 10.7792 11.6296C10.7059 11.7392 10.6668 11.8681 10.6668 12C10.6668 12.1768 10.7371 12.3464 10.8621 12.4714C10.9871 12.5964 11.1567 12.6667 11.3335 12.6667ZM11.3335 10C11.4654 10 11.5942 9.9609 11.7039 9.88764C11.8135 9.81439 11.899 9.71027 11.9494 9.58845C11.9999 9.46664 12.0131 9.33259 11.9874 9.20327C11.9616 9.07395 11.8981 8.95516 11.8049 8.86193C11.7117 8.76869 11.5929 8.7052 11.4636 8.67947C11.3342 8.65375 11.2002 8.66695 11.0784 8.71741C10.9566 8.76787 10.8524 8.85332 10.7792 8.96295C10.7059 9.07258 10.6668 9.20148 10.6668 9.33333C10.6668 9.51014 10.7371 9.67971 10.8621 9.80473C10.9871 9.92976 11.1567 10 11.3335 10ZM8.00016 10C8.13202 10 8.26091 9.9609 8.37054 9.88764C8.48018 9.81439 8.56562 9.71027 8.61608 9.58845C8.66654 9.46664 8.67974 9.33259 8.65402 9.20327C8.6283 9.07395 8.5648 8.95516 8.47157 8.86193C8.37833 8.76869 8.25954 8.7052 8.13022 8.67947C8.0009 8.65375 7.86686 8.66695 7.74504 8.71741C7.62322 8.76787 7.5191 8.85332 7.44585 8.96295C7.3726 9.07258 7.3335 9.20148 7.3335 9.33333C7.3335 9.51014 7.40373 9.67971 7.52876 9.80473C7.65378 9.92976 7.82335 10 8.00016 10ZM12.6668 2H12.0002V1.33333C12.0002 1.15652 11.9299 0.98695 11.8049 0.861926C11.6799 0.736902 11.5103 0.666664 11.3335 0.666664C11.1567 0.666664 10.9871 0.736902 10.8621 0.861926C10.7371 0.98695 10.6668 1.15652 10.6668 1.33333V2H5.3335V1.33333C5.3335 1.15652 5.26326 0.98695 5.13823 0.861926C5.01321 0.736902 4.84364 0.666664 4.66683 0.666664C4.49002 0.666664 4.32045 0.736902 4.19543 0.861926C4.0704 0.98695 4.00016 1.15652 4.00016 1.33333V2H3.3335C2.80306 2 2.29436 2.21071 1.91928 2.58578C1.54421 2.96086 1.3335 3.46956 1.3335 4V13.3333C1.3335 13.8638 1.54421 14.3725 1.91928 14.7475C2.29436 15.1226 2.80306 15.3333 3.3335 15.3333H12.6668C13.1973 15.3333 13.706 15.1226 14.081 14.7475C14.4561 14.3725 14.6668 13.8638 14.6668 13.3333V4C14.6668 3.46956 14.4561 2.96086 14.081 2.58578C13.706 2.21071 13.1973 2 12.6668 2ZM13.3335 13.3333C13.3335 13.5101 13.2633 13.6797 13.1382 13.8047C13.0132 13.9298 12.8436 14 12.6668 14H3.3335C3.15669 14 2.98712 13.9298 2.86209 13.8047C2.73707 13.6797 2.66683 13.5101 2.66683 13.3333V7.33333H13.3335V13.3333ZM13.3335 6H2.66683V4C2.66683 3.82319 2.73707 3.65362 2.86209 3.52859C2.98712 3.40357 3.15669 3.33333 3.3335 3.33333H4.00016V4C4.00016 4.17681 4.0704 4.34638 4.19543 4.4714C4.32045 4.59643 4.49002 4.66666 4.66683 4.66666C4.84364 4.66666 5.01321 4.59643 5.13823 4.4714C5.26326 4.34638 5.3335 4.17681 5.3335 4V3.33333H10.6668V4C10.6668 4.17681 10.7371 4.34638 10.8621 4.4714C10.9871 4.59643 11.1567 4.66666 11.3335 4.66666C11.5103 4.66666 11.6799 4.59643 11.8049 4.4714C11.9299 4.34638 12.0002 4.17681 12.0002 4V3.33333H12.6668C12.8436 3.33333 13.0132 3.40357 13.1382 3.52859C13.2633 3.65362 13.3335 3.82319 13.3335 4V6ZM4.66683 10C4.79868 10 4.92758 9.9609 5.03721 9.88764C5.14684 9.81439 5.23229 9.71027 5.28275 9.58845C5.33321 9.46664 5.34641 9.33259 5.32069 9.20327C5.29496 9.07395 5.23147 8.95516 5.13823 8.86193C5.045 8.76869 4.92621 8.7052 4.79689 8.67947C4.66757 8.65375 4.53352 8.66695 4.41171 8.71741C4.28989 8.76787 4.18577 8.85332 4.11252 8.96295C4.03926 9.07258 4.00016 9.20148 4.00016 9.33333C4.00016 9.51014 4.0704 9.67971 4.19543 9.80473C4.32045 9.92976 4.49002 10 4.66683 10ZM4.66683 12.6667C4.79868 12.6667 4.92758 12.6276 5.03721 12.5543C5.14684 12.4811 5.23229 12.3769 5.28275 12.2551C5.33321 12.1333 5.34641 11.9993 5.32069 11.8699C5.29496 11.7406 5.23147 11.6218 5.13823 11.5286C5.045 11.4354 4.92621 11.3719 4.79689 11.3461C4.66757 11.3204 4.53352 11.3336 4.41171 11.3841C4.28989 11.4345 4.18577 11.52 4.11252 11.6296C4.03926 11.7392 4.00016 11.8681 4.00016 12C4.00016 12.1768 4.0704 12.3464 4.19543 12.4714C4.32045 12.5964 4.49002 12.6667 4.66683 12.6667Z"
                            fill="#8B8B88"
                          />
                        </svg>
                      </div>
                      <input
                        class="border-none w-full font-rubik tracking-[-0.035rem] focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none bg-gray-100 p-2.5 text-sm pl-10 rounded-lg"
                        data-testid="datepicker"
                        name="startDate"
                        placeholder="home.placeholders.date"
                        readonly=""
                        value=""
                      />
                    </div>
                  </div>
                </div>
                <p
                  class="font-outfit font-semibold text-base"
                >
                  home.label.preposition
                </p>
                <div
                  class="relative rounded-lg bg-gray-100 border-none"
                >
                  <div
                    class="flex"
                  >
                    <div
                      class="relative w-full"
                    >
                      <div
                        class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"
                      >
                        <svg
                          fill="none"
                          height="16"
                          viewBox="0 0 16 16"
                          width="16"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8.00016 12.6667C8.13202 12.6667 8.26091 12.6276 8.37054 12.5543C8.48018 12.4811 8.56562 12.3769 8.61608 12.2551C8.66654 12.1333 8.67974 11.9993 8.65402 11.8699C8.6283 11.7406 8.5648 11.6218 8.47157 11.5286C8.37833 11.4354 8.25954 11.3719 8.13022 11.3461C8.0009 11.3204 7.86686 11.3336 7.74504 11.3841C7.62322 11.4345 7.5191 11.52 7.44585 11.6296C7.3726 11.7392 7.3335 11.8681 7.3335 12C7.3335 12.1768 7.40373 12.3464 7.52876 12.4714C7.65378 12.5964 7.82335 12.6667 8.00016 12.6667ZM11.3335 12.6667C11.4654 12.6667 11.5942 12.6276 11.7039 12.5543C11.8135 12.4811 11.899 12.3769 11.9494 12.2551C11.9999 12.1333 12.0131 11.9993 11.9874 11.8699C11.9616 11.7406 11.8981 11.6218 11.8049 11.5286C11.7117 11.4354 11.5929 11.3719 11.4636 11.3461C11.3342 11.3204 11.2002 11.3336 11.0784 11.3841C10.9566 11.4345 10.8524 11.52 10.7792 11.6296C10.7059 11.7392 10.6668 11.8681 10.6668 12C10.6668 12.1768 10.7371 12.3464 10.8621 12.4714C10.9871 12.5964 11.1567 12.6667 11.3335 12.6667ZM11.3335 10C11.4654 10 11.5942 9.9609 11.7039 9.88764C11.8135 9.81439 11.899 9.71027 11.9494 9.58845C11.9999 9.46664 12.0131 9.33259 11.9874 9.20327C11.9616 9.07395 11.8981 8.95516 11.8049 8.86193C11.7117 8.76869 11.5929 8.7052 11.4636 8.67947C11.3342 8.65375 11.2002 8.66695 11.0784 8.71741C10.9566 8.76787 10.8524 8.85332 10.7792 8.96295C10.7059 9.07258 10.6668 9.20148 10.6668 9.33333C10.6668 9.51014 10.7371 9.67971 10.8621 9.80473C10.9871 9.92976 11.1567 10 11.3335 10ZM8.00016 10C8.13202 10 8.26091 9.9609 8.37054 9.88764C8.48018 9.81439 8.56562 9.71027 8.61608 9.58845C8.66654 9.46664 8.67974 9.33259 8.65402 9.20327C8.6283 9.07395 8.5648 8.95516 8.47157 8.86193C8.37833 8.76869 8.25954 8.7052 8.13022 8.67947C8.0009 8.65375 7.86686 8.66695 7.74504 8.71741C7.62322 8.76787 7.5191 8.85332 7.44585 8.96295C7.3726 9.07258 7.3335 9.20148 7.3335 9.33333C7.3335 9.51014 7.40373 9.67971 7.52876 9.80473C7.65378 9.92976 7.82335 10 8.00016 10ZM12.6668 2H12.0002V1.33333C12.0002 1.15652 11.9299 0.98695 11.8049 0.861926C11.6799 0.736902 11.5103 0.666664 11.3335 0.666664C11.1567 0.666664 10.9871 0.736902 10.8621 0.861926C10.7371 0.98695 10.6668 1.15652 10.6668 1.33333V2H5.3335V1.33333C5.3335 1.15652 5.26326 0.98695 5.13823 0.861926C5.01321 0.736902 4.84364 0.666664 4.66683 0.666664C4.49002 0.666664 4.32045 0.736902 4.19543 0.861926C4.0704 0.98695 4.00016 1.15652 4.00016 1.33333V2H3.3335C2.80306 2 2.29436 2.21071 1.91928 2.58578C1.54421 2.96086 1.3335 3.46956 1.3335 4V13.3333C1.3335 13.8638 1.54421 14.3725 1.91928 14.7475C2.29436 15.1226 2.80306 15.3333 3.3335 15.3333H12.6668C13.1973 15.3333 13.706 15.1226 14.081 14.7475C14.4561 14.3725 14.6668 13.8638 14.6668 13.3333V4C14.6668 3.46956 14.4561 2.96086 14.081 2.58578C13.706 2.21071 13.1973 2 12.6668 2ZM13.3335 13.3333C13.3335 13.5101 13.2633 13.6797 13.1382 13.8047C13.0132 13.9298 12.8436 14 12.6668 14H3.3335C3.15669 14 2.98712 13.9298 2.86209 13.8047C2.73707 13.6797 2.66683 13.5101 2.66683 13.3333V7.33333H13.3335V13.3333ZM13.3335 6H2.66683V4C2.66683 3.82319 2.73707 3.65362 2.86209 3.52859C2.98712 3.40357 3.15669 3.33333 3.3335 3.33333H4.00016V4C4.00016 4.17681 4.0704 4.34638 4.19543 4.4714C4.32045 4.59643 4.49002 4.66666 4.66683 4.66666C4.84364 4.66666 5.01321 4.59643 5.13823 4.4714C5.26326 4.34638 5.3335 4.17681 5.3335 4V3.33333H10.6668V4C10.6668 4.17681 10.7371 4.34638 10.8621 4.4714C10.9871 4.59643 11.1567 4.66666 11.3335 4.66666C11.5103 4.66666 11.6799 4.59643 11.8049 4.4714C11.9299 4.34638 12.0002 4.17681 12.0002 4V3.33333H12.6668C12.8436 3.33333 13.0132 3.40357 13.1382 3.52859C13.2633 3.65362 13.3335 3.82319 13.3335 4V6ZM4.66683 10C4.79868 10 4.92758 9.9609 5.03721 9.88764C5.14684 9.81439 5.23229 9.71027 5.28275 9.58845C5.33321 9.46664 5.34641 9.33259 5.32069 9.20327C5.29496 9.07395 5.23147 8.95516 5.13823 8.86193C5.045 8.76869 4.92621 8.7052 4.79689 8.67947C4.66757 8.65375 4.53352 8.66695 4.41171 8.71741C4.28989 8.76787 4.18577 8.85332 4.11252 8.96295C4.03926 9.07258 4.00016 9.20148 4.00016 9.33333C4.00016 9.51014 4.0704 9.67971 4.19543 9.80473C4.32045 9.92976 4.49002 10 4.66683 10ZM4.66683 12.6667C4.79868 12.6667 4.92758 12.6276 5.03721 12.5543C5.14684 12.4811 5.23229 12.3769 5.28275 12.2551C5.33321 12.1333 5.34641 11.9993 5.32069 11.8699C5.29496 11.7406 5.23147 11.6218 5.13823 11.5286C5.045 11.4354 4.92621 11.3719 4.79689 11.3461C4.66757 11.3204 4.53352 11.3336 4.41171 11.3841C4.28989 11.4345 4.18577 11.52 4.11252 11.6296C4.03926 11.7392 4.00016 11.8681 4.00016 12C4.00016 12.1768 4.0704 12.3464 4.19543 12.4714C4.32045 12.5964 4.49002 12.6667 4.66683 12.6667Z"
                            fill="#8B8B88"
                          />
                        </svg>
                      </div>
                      <input
                        class="border-none w-full font-rubik tracking-[-0.035rem] focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none bg-gray-100 p-2.5 text-sm pl-10 rounded-lg"
                        data-testid="datepicker"
                        name="endDate"
                        placeholder="home.placeholders.date"
                        readonly=""
                        value=""
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="lg:w-1/2 sm:w-3/4 w-full relative sm:ml-4"
            >
              <button
                class="w-full font-outfit font-medium duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-base tracking-[0.0312rem] rounded-lg py-4 flex flex-row sm:absolute sm:bottom-0 items-center justify-center gap-[0.63rem] lg:h-[95%] h-[64%] px-0 max-sm:mt-8 max-sm:mx-auto"
              >
                home.buttons.generate-posts
                <svg
                  aria-hidden="true"
                  class="h-4 stroke-[0.25rem]"
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
  <section
    aria-hidden="true"
    class="bg-black-900 lg:mt-20 mt-14 lg:py-20 py-14 max-lg:px-4"
    data-floating-ui-inert=""
  >
    <h1
      class="font-outfit text-[2.5rem] text-white max-w-3xl mx-auto text-center font-semibold tracking-[-0.0375rem] max-[639px]:text-[1.75rem] leading-[3rem] max-[639px]:leading-8"
    >
      ***
      <strong
        class="text-yellow-800 font-bold"
      >
         ***
      </strong>
    </h1>
    <div
      class="mt-14 flex sm:flex-row flex-col lg:gap-[9.3rem] gap-20 justify-center"
    >
      <div
        class="flex flex-col items-center gap-8"
      >
        <div
          class="lg:h-[36rem] h-[31.25rem] lg:w-72 w-[16.25rem] bg-white rounded-[2rem] flex items-center justify-center aos-init aos-animate"
          data-aos="fade-right"
          data-aos-offset="300"
        >
          <p>
            video
          </p>
        </div>
        <div
          class="aos-init aos-animate"
          data-aos="fade-up"
          data-aos-offset="200"
        >
          <p
            class="font-outfit font-bold text-[2rem] text-yellow-800"
          >
            home.how-works.mini-title-1
          </p>
          <p
            class="font-outfit font-bold text-2xl leading-7 text-white max-w-[15rem] text-center"
          >
            home.how-works.mini-description
          </p>
        </div>
      </div>
      <div
        class="flex flex-col items-center gap-8"
      >
        <div
          class="lg:h-[36rem] h-[31.25rem] lg:w-72 w-[16.25rem] bg-white rounded-[2rem] flex items-center justify-center aos-init aos-animate"
          data-aos="fade-left"
          data-aos-offset="300"
        >
          <p>
            video
          </p>
        </div>
        <div
          class="aos-init aos-animate"
          data-aos="fade-up"
          data-aos-offset="200"
        >
          <p
            class="font-outfit font-bold text-[2rem] text-yellow-800"
          >
            home.how-works.mini-title-2
          </p>
          <p
            class="font-outfit font-bold text-2xl leading-7 text-white max-w-[15rem] text-center"
          >
            home.how-works.mini-description
          </p>
        </div>
      </div>
    </div>
  </section>
  <section
    aria-hidden="true"
    class="lg:py-20 py-14 sm:px-8 px-4"
    data-floating-ui-inert=""
  >
    <div
      class="mx-auto bg-white flex sm:flex-row flex-col justify-center lg:gap-24 sm:gap-8 gap-16 rounded-[2rem] max-w-[100rem]"
    >
      <div
        class="flex flex-col gap-8 lg:max-w-[30%] sm:max-w-[40%] max-sm:px-6 sm:my-auto max-sm:mt-[2.5rem] max-sm:mx-auto"
      >
        <h1
          class="font-outfit font-[700] max-w-lg tracking-[-0.0375rem] text-left lg:font-semibold lg:text-[2.5rem] text-[2rem] leading-10 aos-init aos-animate"
          data-aos="fade-right"
          data-aos-offset="200"
        >
          home.campaigns.title-1
        </h1>
        <p
          class="max-w-sm text-left font-outfit font-semibold text-base leading-[1.31rem] aos-init aos-animate"
          data-aos="fade-right"
          data-aos-offset="100"
        >
          home.campaigns.description-1
        </p>
        <button
          class="w-full font-outfit font-medium duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-base tracking-[0.0312rem] rounded-lg py-4 max-w-fit flex flex-row items-center justify-center gap-[0.63rem] px-3 aos-init aos-animate"
          data-aos="fade-right"
          data-aos-offset="100"
        >
          home.buttons.test-free
          <svg
            aria-hidden="true"
            class="relative h-4 stroke-[0.25rem]"
            data-slot="icon"
            fill="none"
            stroke="currentColor"
            stroke-width="1.5"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
      <img
        class="xl:w-[34.935rem] lg:w-[30rem] sm:w-[20.5rem] w-[18.23rem] sm:mt-20 max-sm:mx-auto aos-init aos-animate"
        data-aos="fade"
        data-aos-delay="500"
        data-aos-duration="700"
        data-aos-offset="100"
        src=""
      />
    </div>
    <div
      class="lg:pt-20 pt-14 px-14 flex flex-col max-w-[100rem] mx-auto"
    >
      <h1
        class="font-outfit text-[2.5rem] font-semibold tracking-[-0.0375rem] lg:whitespace-nowrap lg:text-left text-center max-sm:text-[2rem] sm:leading-[3rem] leading-[2.5rem] max-[300px]:text-2xl max-sm:mx-[-3rem] xl:w-[70rem] xl:mx-auto lg:mb-14 mb-8 aos-init aos-animate"
        data-aos="fade-right"
        data-aos-duration="400"
        data-aos-offset="200"
      >
        home.campaigns.title-2
      </h1>
      <div
        class="flex lg:flex-row flex-col sm:gap-8 gap-6 max-w-[70rem] xl:mx-auto lg:items-center"
      >
        <div
          class="sm:h-[28rem] h-[13.5rem] grow flex justify-center items-center bg-white rounded-xl max-sm:mx-[-4rem] max-sm:min-[505px]:w-[22.5rem] max-[504px]:min-[465px]:w-[20rem] max-[465px]:min-[410px]:w-[17rem] max-sm:min-[410px]:mx-auto aos-init aos-animate"
          data-aos="fade"
          data-aos-duration="500"
          data-aos-offset="300"
        >
          <p>
            video
          </p>
        </div>
        <div
          class="flex flex-col gap-5 lg:w-[30%] max-[1023px]:mx-auto"
        >
          <p
            class="lg:text-[2rem] text-2xl font-outfit font-bold text-black-900 leading-9 lg:text-left max-sm:mx-[-4rem] aos-init aos-animate"
            data-aos="fade-up"
            data-aos-offset="200"
          >
            home.campaigns.description-2
          </p>
          <button
            class="w-full font-outfit font-medium text-black-900 duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-base tracking-[0.0312rem] rounded-lg py-4 max-w-fit flex flex-row items-center justify-center gap-[0.63rem] max-[1023px]:mx-auto px-3 aos-init aos-animate"
            data-aos="fade"
            data-aos-offset="100"
          >
            home.buttons.free-test
            <svg
              aria-hidden="true"
              class="relative h-4 stroke-[0.25rem]"
              data-slot="icon"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </section>
  <section>
    <div
      class="bg-black-900 lg:py-20 py-14"
    >
      <div
        class="max-w-[100rem] lg:pl-16 max-sm:px-4 mx-auto flex lg:flex-row flex-col xl:gap-16 lg:gap-12"
      >
        <div
          aria-hidden="true"
          class="text-white flex flex-col gap-12 sm:text-left lg:max-w-[23rem] sm:max-w-[30rem] max-lg:sm:pl-[15%] max-sm:text-[2rem] max-sm:leading-10 max-xl:lg:min-w-[23rem]"
          data-floating-ui-inert=""
        >
          <h1
            class="font-outfit text-[2.5rem] font-semibold tracking-[-0.0375rem] sm:leading-[3rem] max-sm:text-[2rem] leading-10 aos-init aos-animate"
            data-aos="fade-right"
            data-aos-duration="400"
            data-aos-offset="300"
          >
            ***
            <strong
              class="text-yellow-800"
            >
               *** 
            </strong>
            ***
          </h1>
          <p
            class="font-outfit font-bold sm:text-2xl sm:leading-7 text-[1.31rem] leading-6 aos-init aos-animate"
            data-aos="fade-right"
            data-aos-duration="400"
            data-aos-offset="200"
          >
            home.custom-content.description
          </p>
          <button
            class="w-full font-outfit font-medium text-black-900 duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-base tracking-[0.0312rem] rounded-lg py-4 max-w-fit flex flex-row items-center justify-center gap-[0.63rem] max-lg:hidden px-3 aos-init aos-animate"
            data-aos="fade-right"
            data-aos-duration="400"
            data-aos-offset="100"
          >
            home.buttons.free-test
            <svg
              aria-hidden="true"
              class="relative h-4 stroke-[0.25rem]"
              data-slot="icon"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>
        <div
          class="flex flex-col gap-6 w-full max-lg:mt-12 min-[1700px]:mr-[-10%] max-xl:lg:min-w-[49rem] aos-init aos-animate"
          data-aos="fade-left"
          data-aos-duration="400"
          data-aos-offset="350"
        >
          <div
            aria-label="Slider"
            aria-roledescription="carousel"
            class="slider-container"
            data-testid=":r0:"
            id=":r0:"
            role="group"
            style="position: relative;"
          >
            <div
              aria-atomic="true"
              aria-live="polite"
              style="position: absolute; width: 1px; height: 1px; overflow: hidden; padding: 0px; margin: -1px; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border: 0px;"
              tabindex="-1"
            >
              Slide 1 of 4
            </div>
            <div
              aria-hidden="true"
              class="slider-frame"
              data-floating-ui-inert=""
              data-testid=":r0:-slider-frame"
              id=":r0:-slider-frame"
              style="overflow: hidden; width: 100%; position: relative; outline: none; height: auto; transition: height 300ms ease-in-out; will-change: height; user-select: none;"
              tabindex="-1"
            >
              <div
                class="slider-list"
                style="width: 200%; text-align: left; user-select: auto; display: flex;"
              >
                <div
                  class="slide slide-current slide-visible"
                  id=":r0:-slide-1"
                  role="tabpanel"
                  style="width: 25%; height: auto; padding: 0px 12px; opacity: 1;"
                >
                  <div
                    class="relative rounded-xl border-0 bg-transparent p-0"
                  >
                    <img
                      class="w-full"
                      draggable="false"
                      src=""
                    />
                    <div
                      class="absolute -top-[0.525rem] -right-[0.675rem] shrink h-42 w-32"
                    />
                  </div>
                </div>
                <div
                  class="slide slide-visible"
                  id=":r0:-slide-2"
                  role="tabpanel"
                  style="width: 25%; height: auto; padding: 0px 12px; opacity: 1;"
                >
                  <div
                    class="relative rounded-xl border-0 bg-transparent p-0"
                  >
                    <img
                      class="w-full"
                      draggable="false"
                      src=""
                    />
                    <div
                      class="absolute -top-[0.525rem] -right-[0.675rem] shrink h-42 w-32"
                    />
                  </div>
                </div>
                <div
                  class="slide slide-visible"
                  id=":r0:-slide-3"
                  role="tabpanel"
                  style="width: 25%; height: auto; padding: 0px 12px; opacity: 1;"
                >
                  <div
                    class="relative rounded-xl border-0 bg-transparent p-0"
                  >
                    <img
                      class="w-full"
                      draggable="false"
                      src=""
                    />
                    <div
                      class="absolute -top-[0.525rem] -right-[0.675rem] shrink h-42 w-32"
                    />
                  </div>
                </div>
                <div
                  class="slide slide-visible"
                  id=":r0:-slide-4"
                  role="tabpanel"
                  style="width: 25%; height: auto; padding: 0px 12px; opacity: 1;"
                >
                  <div
                    class="relative rounded-xl border-0 bg-transparent p-0"
                  >
                    <img
                      class="w-full"
                      draggable="false"
                      src=""
                    />
                    <div
                      class="absolute -top-[0.525rem] -right-[0.675rem] shrink h-42 w-32"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            aria-hidden="true"
            class="flex flex-row w-full h-fit justify-start"
            data-floating-ui-inert=""
            data-testid="pagination"
          >
            <div
              class="flex flex-row gap-2 items-center w-fit"
            >
              <div
                class="stroke-[0.25rem] cursor-pointer min-w-[1.75rem]"
              />
              <div
                class="w-fit"
              >
                <button
                  class="py-3 px-4 font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-xs rounded-full text-center flex items-center justify-center w-10 h-10"
                  data-testid="button-1"
                >
                  <p
                    class="select-none tracking-normal"
                  >
                    1
                  </p>
                </button>
              </div>
              <div
                class="w-fit"
              >
                <button
                  class="py-3 px-4 font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-white text-xs rounded-full text-center flex items-center justify-center w-10 h-10"
                  data-testid="button-2"
                >
                  <p
                    class="select-none tracking-normal"
                  >
                    2
                  </p>
                </button>
              </div>
              <div
                class="w-fit"
              >
                <button
                  class="py-3 px-4 font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-white text-xs rounded-full text-center flex items-center justify-center w-10 h-10"
                  data-testid="button-3"
                >
                  <p
                    class="select-none tracking-normal"
                  >
                    3
                  </p>
                </button>
              </div>
              <div
                class="w-fit"
              >
                <button
                  class="py-3 px-4 font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-white text-xs rounded-full text-center flex items-center justify-center w-10 h-10"
                  data-testid="button-4"
                >
                  <p
                    class="select-none tracking-normal"
                  >
                    4
                  </p>
                </button>
              </div>
              <div
                class="stroke-[0.25rem] cursor-pointer min-w-[1.75rem]"
              >
                <svg
                  aria-hidden="true"
                  class="stroke-[0.25rem] cursor-pointer text-white min-w-[1.75rem]"
                  data-slot="icon"
                  data-testid="right-icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m8.25 4.5 7.5 7.5-7.5 7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      aria-hidden="true"
      class="bg-yellow-800 lg:py-20 py-14 px-4"
      data-floating-ui-inert=""
    >
      <div
        class="mx-auto lg:max-w-[76rem] sm:max-w-[51rem] max-w-[33.8rem] lg:px-16"
      >
        <h1
          class="font-outfit text-[2.5rem] tracking-[-0.0375rem] font-semibold sm:leading-[3rem] leading-10 max-sm:text-[2rem] sm:text-left max-sm:text-center xl:w-9/12 lg:7/12 sm:w-[72%] min-[469px]:w-7/12 min-[360px]:w-9/12 max-lg:mx-auto aos-init aos-animate"
          data-aos="fade-up"
          data-aos-offset="350"
        >
          home.custom-content.title-2
        </h1>
        <div
          class="w-full flex flex-row flex-wrap xl:flex-nowrap xl:gap-8  lg:justify-between justify-center mt-14 max-lg:flex-wrap"
        >
          <div
            class="w-full lg:w-1/2 xl:w-full lg:h-72 max-lg:mb-8 lg:p-4 xl:p-0 aos-init aos-animate"
            data-aos="fade-left"
            data-aos-delay="200"
            data-aos-duration="1000"
            data-aos-offset="200"
          >
            <div
              class="relative rounded-xl border-0 bg-white p-3 w-full h-full"
            >
              <div
                class="w-full text-left p-8"
              >
                <svg
                  aria-hidden="true"
                  class="text-yellow-800 h-24 w-24 mb-4"
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M7.864 4.243A7.5 7.5 0 0 1 19.5 10.5c0 2.92-.556 5.709-1.568 8.268M5.742 6.364A7.465 7.465 0 0 0 4.5 10.5a7.464 7.464 0 0 1-1.15 3.993m1.989 3.559A11.209 11.209 0 0 0 8.25 10.5a3.75 3.75 0 1 1 7.5 0c0 .527-.021 1.049-.064 1.565M12 10.5a14.94 14.94 0 0 1-3.6 9.75m6.633-4.596a18.666 18.666 0 0 1-2.485 5.33"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <p
                  class="font-outfit font-bold text-xl leading-5"
                >
                  home.custom-content.cards.title-1
                </p>
                <p
                  class="font-rubik font-[400] text-[0.75rem] mt-3"
                >
                  home.custom-content.cards.description-1
                </p>
              </div>
              <div
                class="absolute -top-[0.525rem] -right-[0.675rem] shrink h-42 w-32"
              />
            </div>
          </div>
          <div
            class="w-full lg:w-1/2 xl:w-full lg:h-72 max-lg:mb-8 lg:p-4 xl:p-0 aos-init aos-animate"
            data-aos="fade-left"
            data-aos-delay="250"
            data-aos-duration="1000"
            data-aos-offset="200"
          >
            <div
              class="relative rounded-xl border-0 bg-white p-3 w-full h-full"
            >
              <div
                class="w-full text-left p-8 lg:pb-12"
              >
                <svg
                  aria-hidden="true"
                  class="text-yellow-800 h-24 w-24 mb-4"
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <p
                  class="font-outfit font-bold text-xl leading-5"
                >
                  home.custom-content.cards.title-2
                </p>
                <p
                  class="font-rubik font-[400] text-[0.75rem] mt-3"
                >
                  home.custom-content.cards.description-2
                </p>
              </div>
              <div
                class="absolute -top-[0.525rem] -right-[0.675rem] shrink h-42 w-32"
              />
            </div>
          </div>
          <div
            class="w-full lg:w-1/2 xl:w-full lg:h-72 max-lg:mb-8 lg:p-4 xl:p-0 aos-init aos-animate"
            data-aos="fade-left"
            data-aos-delay="300"
            data-aos-duration="1000"
            data-aos-offset="200"
          >
            <div
              class="relative rounded-xl border-0 bg-white p-3 w-full h-full"
            >
              <div
                class="w-full text-left p-8"
              >
                <svg
                  aria-hidden="true"
                  class="text-yellow-800 h-24 w-24 mb-4"
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535M10.34 6.66a23.847 23.847 0 0 0 8.835-2.535m0 0A23.74 23.74 0 0 0 18.795 3m.38 1.125a23.91 23.91 0 0 1 1.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 0 0 1.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 0 1 0 3.46"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <p
                  class="font-outfit font-bold text-xl leading-5"
                >
                  home.custom-content.cards.title-3
                </p>
                <p
                  class="font-rubik font-[400] text-[0.75rem] mt-3"
                >
                  home.custom-content.cards.description-3
                </p>
              </div>
              <div
                class="absolute -top-[0.525rem] -right-[0.675rem] shrink h-42 w-32"
              />
            </div>
          </div>
          <div
            class="w-full lg:w-1/2 xl:w-full lg:h-72 max-lg:mb-8 lg:p-4 xl:p-0 aos-init aos-animate"
            data-aos="fade-left"
            data-aos-delay="350"
            data-aos-duration="1000"
            data-aos-offset="200"
          >
            <div
              class="relative rounded-xl border-0 bg-white p-3 w-full h-full"
            >
              <div
                class="w-full text-left p-8"
              >
                <svg
                  aria-hidden="true"
                  class="text-yellow-800 h-24 w-24 mb-4"
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M9.53 16.122a3 3 0 0 0-5.78 1.128 2.25 2.25 0 0 1-2.4 2.245 4.5 4.5 0 0 0 8.4-2.245c0-.399-.078-.78-.22-1.128Zm0 0a15.998 15.998 0 0 0 3.388-1.62m-5.043-.025a15.994 15.994 0 0 1 1.622-3.395m3.42 3.42a15.995 15.995 0 0 0 4.764-4.648l3.876-5.814a1.151 1.151 0 0 0-1.597-1.597L14.146 6.32a15.996 15.996 0 0 0-4.649 4.763m3.42 3.42a6.776 6.776 0 0 0-3.42-3.42"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <p
                  class="font-outfit font-bold text-xl leading-5"
                >
                  home.custom-content.cards.title-4
                </p>
                <p
                  class="font-rubik font-[400] text-[0.75rem] mt-3"
                >
                  home.custom-content.cards.description-4
                </p>
              </div>
              <div
                class="absolute -top-[0.525rem] -right-[0.675rem] shrink h-42 w-32"
              />
            </div>
          </div>
        </div>
        <button
          class="w-full font-outfit font-medium duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-base tracking-[0.0312rem] rounded-lg py-4 max-w-fit flex flex-row items-center justify-center gap-[0.63rem] mt-10 mx-auto px-3 aos-init aos-animate"
          data-aos="fade"
          data-aos-offset="200"
        >
          home.buttons.free-test
          <svg
            aria-hidden="true"
            class="relative h-4 stroke-[0.25rem]"
            data-slot="icon"
            fill="none"
            stroke="currentColor"
            stroke-width="1.5"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
    </div>
  </section>
  <section
    aria-hidden="true"
    class="lg:py-20 py-14 sm:px-8 px-4"
    data-floating-ui-inert=""
  >
    <div
      class="bg-white rounded-[2rem] lg:py-[5.5rem] py-[3.5rem] max-[639px]:px-4 max-w-[100rem] mx-auto"
    >
      <h1
        class="font-outfit text-[2.5rem] lg:w-10/12 sm:w-5/6 min-[455px]:w-11/12 min-[430px]:w-full min-[365px]:w-10/12 max-w-[90rem] text-center mx-auto tracking-[-0.0375rem] font-bold lg:leading-[3rem] sm:leading-[2.5rem] leading-9 max-lg:text-[2rem] max-sm:text-[1.75rem] aos-init aos-animate"
        data-aos="fade-up"
        data-aos-offset="300"
      >
        home.multitool.title
      </h1>
      <p
        class="font-outfit font-semibold lg:text-2xl sm:text-[1.31rem] text-[1.125rem] sm:leading-7 leading-6 lg:w-7/12 sm:w-4/5 max-w-[54rem] text-center mx-auto lg:mt-6 mt-4 aos-init aos-animate"
        data-aos="fade-up"
        data-aos-offset="200"
      >
        home.multitool.description
      </p>
      <div
        class="lg:mt-24 sm:mt-[5.5rem] mt-10 mx-auto flex flex-col lg:gap-36 sm:gap-20 gap-12 max-w-[65rem] lg:w-9/12 sm:w-4/5"
      >
        <div
          class="w-full flex sm:flex-row flex-col gap-4 justify-between items-center"
        >
          <div
            class="bg-gray-100 lg:w-[27rem] lg:h-[22rem] sm:w-[23rem] sm:h-[18.25rem] min-[375px]:w-[18.5rem] min-[375px]:h-[14.75rem] min-[340px]:w-[16.7rem] min-[340px]:h-[12.25rem] w-[14rem] h-[11rem] rounded-lg flex items-center aos-init aos-animate"
            data-aos="fade-up"
            data-aos-offset="350"
          >
            <p
              class="mx-auto"
            >
              Vídeo ou Gif demonstrando
            </p>
          </div>
          <div
            class="lg:max-w-[45%] sm:max-w-[40%] sm:text-left text-center aos-init aos-animate"
            data-aos="fade-left"
            data-aos-offset="300"
          >
            <p
              class="font-outfit font-semibold lg:text-[2rem] text-[1.5rem] text-yellow-900 tracking-[-0.0375rem] lg:leading-9 leading-7"
            >
              home.multitool.items.title-1
            </p>
            <p
              class="font-outfit lg:text-2xl text-[1.125rem] font-medium lg:leading-7 leading-[1.31rem] sm:mt-4 mt-8"
            >
              home.multitool.items.description-1
            </p>
          </div>
        </div>
        <div
          class="w-full flex sm:flex-row flex-col gap-4 justify-between items-center"
        >
          <div
            class="lg:max-w-[45%] sm:max-w-[40%] max-[639px]:hidden sm:text-left text-center aos-init aos-animate"
            data-aos="fade-right"
            data-aos-offset="300"
          >
            <p
              class="font-outfit font-semibold lg:text-[2rem] text-[1.5rem] text-yellow-900 tracking-[-0.0375rem] lg:leading-9 leading-7"
            >
              home.multitool.items.title-2
            </p>
            <p
              class="font-outfit lg:text-2xl text-[1.125rem] font-medium lg:leading-7 leading-[1.31rem] sm:mt-4 mt-8"
            >
              home.multitool.items.description-2
            </p>
          </div>
          <div
            class="bg-gray-100 lg:w-[27rem] lg:h-[22rem] sm:w-[23rem] sm:h-[18.25rem] min-[375px]:w-[18.5rem] min-[375px]:h-[14.75rem] min-[340px]:w-[16.7rem] min-[340px]:h-[12.25rem] w-[14rem] h-[11rem] rounded-lg flex items-center aos-init aos-animate"
            data-aos="fade-up"
            data-aos-offset="350"
          >
            <p
              class="mx-auto"
            >
              Vídeo ou Gif demonstrando
            </p>
          </div>
          <div
            class="lg:max-w-[45%] sm:max-w-[40%] sm:text-left text-center sm:hidden aos-init aos-animate"
            data-aos="fade-left"
            data-aos-offset="300"
          >
            <p
              class="font-outfit font-semibold lg:text-[2rem] text-[1.5rem] text-yellow-900 tracking-[-0.0375rem] lg:leading-9 leading-7"
            >
              home.multitool.items.title-2
            </p>
            <p
              class="font-outfit lg:text-2xl text-[1.125rem] font-medium lg:leading-7 leading-[1.31rem] sm:mt-4 mt-8"
            >
              home.multitool.items.description-2
            </p>
          </div>
        </div>
        <div
          class="w-full flex sm:flex-row flex-col gap-4 justify-between items-center"
        >
          <div
            class="bg-gray-100 lg:w-[27rem] lg:h-[22rem] sm:w-[23rem] sm:h-[18.25rem] min-[375px]:w-[18.5rem] min-[375px]:h-[14.75rem] min-[340px]:w-[16.7rem] min-[340px]:h-[12.25rem] w-[14rem] h-[11rem] rounded-lg flex items-center aos-init aos-animate"
            data-aos="fade-up"
            data-aos-offset="350"
          >
            <p
              class="mx-auto"
            >
              Vídeo ou Gif demonstrando
            </p>
          </div>
          <div
            class="lg:max-w-[45%] sm:max-w-[40%] sm:text-left text-center aos-init aos-animate"
            data-aos="fade-left"
            data-aos-offset="300"
          >
            <p
              class="font-outfit font-semibold lg:text-[2rem] text-[1.5rem] text-yellow-900 tracking-[-0.0375rem] lg:leading-9 leading-7"
            >
              home.multitool.items.title-3
            </p>
            <p
              class="font-outfit lg:text-2xl text-[1.125rem] font-medium lg:leading-7 leading-[1.31rem] sm:mt-4 mt-8"
            >
              home.multitool.items.description-3
            </p>
          </div>
        </div>
        <div
          class="w-full flex sm:flex-row flex-col gap-4 justify-between items-center"
        >
          <div
            class="lg:max-w-[45%] sm:max-w-[40%] max-[639px]:hidden sm:text-left text-center aos-init aos-animate"
            data-aos="fade-right"
            data-aos-offset="300"
          >
            <p
              class="font-outfit font-semibold lg:text-[2rem] text-[1.5rem] text-yellow-900 tracking-[-0.0375rem] lg:leading-9 leading-7"
            >
              home.multitool.items.title-4
            </p>
            <p
              class="font-outfit lg:text-2xl text-[1.125rem] font-medium lg:leading-7 leading-[1.31rem] sm:mt-4 mt-8"
            >
              home.multitool.items.description-4
            </p>
          </div>
          <div
            class="bg-gray-100 lg:w-[27rem] lg:h-[22rem] sm:w-[23rem] sm:h-[18.25rem] min-[375px]:w-[18.5rem] min-[375px]:h-[14.75rem] min-[340px]:w-[16.7rem] min-[340px]:h-[12.25rem] w-[14rem] h-[11rem] rounded-lg flex items-center aos-init aos-animate"
            data-aos="fade-up"
            data-aos-offset="350"
          >
            <p
              class="mx-auto"
            >
              Vídeo ou Gif demonstrando
            </p>
          </div>
          <div
            class="lg:max-w-[45%] sm:max-w-[40%] sm:text-left text-center sm:hidden aos-init aos-animate"
            data-aos="fade-left"
            data-aos-offset="300"
          >
            <p
              class="font-outfit font-semibold lg:text-[2rem] text-[1.5rem] text-yellow-900 tracking-[-0.0375rem] lg:leading-9 leading-7"
            >
              home.multitool.items.title-4
            </p>
            <p
              class="font-outfit lg:text-2xl text-[1.125rem] font-medium lg:leading-7 leading-[1.31rem] sm:mt-4 mt-8"
            >
              home.multitool.items.description-4
            </p>
          </div>
        </div>
      </div>
      <button
        class="w-full font-outfit font-medium text-black-900 duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-base tracking-[0.0312rem] rounded-lg py-4 max-w-fit flex flex-row items-center justify-center gap-[0.63rem] mx-auto mt-[5.5rem] px-3 aos-init aos-animate"
        data-aos="fade"
        data-aos-offset="250"
      >
        home.buttons.test-free
        <svg
          aria-hidden="true"
          class="relative h-4 stroke-[0.25rem]"
          data-slot="icon"
          fill="none"
          stroke="currentColor"
          stroke-width="1.5"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </div>
  </section>
  <section
    aria-hidden="true"
    class="bg-black-900 sm:py-20 py-14"
    data-floating-ui-inert=""
  >
    <h1
      class="font-outfit text-[2.5rem] text-white lg:w-4/6 w-11/12 max-w-3xl font-semibold mx-auto text-center tracking-[-0.0375rem] max-lg:text-[2rem] leading-[3rem] max-lg:leading-10 aos-init aos-animate"
      data-aos="fade-up"
      data-aos-offset="300"
    >
      ***
      <strong
        class="text-yellow-800 font-bold"
      >
         ***
      </strong>
      ***
    </h1>
    <div
      class="w-full mt-14"
    >
      <div
        class="flex gap-3 justify-end"
        id="row-1"
      >
        <img
          class="w-[19rem] h-[28.5rem]"
          src=""
        />
        <img
          class="w-[19rem] h-[28.5rem]"
          src=""
        />
        <img
          class="w-[19rem] h-[28.5rem]"
          src=""
        />
        <img
          class="w-[19rem] h-[28.5rem]"
          src=""
        />
        <img
          class="w-[19rem] h-[28.5rem]"
          src=""
        />
        <img
          class="w-[19rem] h-[28.5rem]"
          src=""
        />
        <img
          class="w-[19rem] h-[28.5rem]"
          src=""
        />
        <img
          class="w-[19rem] h-[28.5rem]"
          src=""
        />
      </div>
      <div
        class="flex gap-3 justify-start mt-4"
        id="row-2"
      >
        <img
          class="w-72 h-[30rem]"
          src=""
        />
        <img
          class="w-72 h-[30rem]"
          src=""
        />
        <img
          class="w-72 h-[30rem]"
          src=""
        />
        <img
          class="w-72 h-[30rem]"
          src=""
        />
        <img
          class="w-72 h-[30rem]"
          src=""
        />
        <img
          class="w-72 h-[30rem]"
          src=""
        />
        <img
          class="w-72 h-[30rem]"
          src=""
        />
        <img
          class="w-72 h-[30rem]"
          src=""
        />
      </div>
    </div>
    <p
      class="font-outfit font-bold text-2xl leading-7 text-white mx-auto mt-[3.60rem] text-center max-md:w-11/12 max-sm:w-9/12 max-[480px]:w-11/12 max-[360px]:w-9/12 aos-init aos-animate"
      data-aos="fade-up"
      data-aos-delay="400"
      data-aos-offset="100"
    >
      home.examples.punchline
    </p>
    <div
      class="inline-block"
    >
      <button
        class="w-full font-outfit font-medium text-black-900 duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-base tracking-[0.0312rem] rounded-lg py-4 flex flex-row items-center justify-center gap-[0.63rem] mt-6 px-3 aos-init aos-animate"
        data-aos="fade-up"
        data-aos-delay="400"
        data-aos-offset="100"
      >
        home.buttons.test-free
        <svg
          aria-hidden="true"
          class="relative h-4 stroke-[0.25rem]"
          data-slot="icon"
          fill="none"
          stroke="currentColor"
          stroke-width="1.5"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </div>
  </section>
  <section
    aria-hidden="true"
    class="lg:py-20 py-14 sm:px-8 px-4"
    data-floating-ui-inert=""
  >
    <div
      class="mx-auto max-w-[100rem] rounded-[2rem] bg-white pt-20 pb-14 lg:px-24"
    >
      <div
        class="w-[90%] flex sm:flex-row flex-col sm:gap-20 gap-12 justify-between mx-auto"
      >
        <div
          class="text-left sm:max-w-[45%] aos-init aos-animate"
          data-aos="fade-right"
          data-aos-offset="300"
        >
          <h1
            class="font-outfit text-[2.5rem] font-semibold lg:leading-[3rem] leading-10 tracking-[-0.0375rem] max-lg:text-[2rem]"
          >
            home.questions.title
          </h1>
          <p
            class="font-outfit font-medium lg:text-2xl text-sm mt-6"
          >
            ***
            <a
              class="hover:underline text-yellow-900 hover:text-black-900"
              href="#"
            >
               *** 
            </a>
            ***
            <a
              class="hover:underline text-yellow-900 hover:text-black-900"
              href="#"
            >
               ***
            </a>
            ***
          </p>
        </div>
        <div
          class="divide-y border-gray-200 dark:divide-gray-700 dark:border-gray-700 rounded-none border-0 bg-transparent divide-black-900 origin-top-left transition-all sm:shrink-0 sm:w-96 min-h-[30rem] aos-init aos-animate"
          data-aos="fade-left"
          data-aos-offset="400"
          data-testid="flowbite-accordion"
        >
          <button
            class="flex w-full items-center justify-between py-[0.88rem] text-left font-outfit focus:ring-gray-200 dark:hover:bg-gray-800 dark:focus:ring-gray-800 text-gray-900 dark:bg-gray-800 dark:text-white border-0 border-t-[0.0625rem] border-black-900 first:rounded-none first:text-black-900 last:rounded-none hover:bg-transparent bg-transparent focus:ring-0 origin-top-left transition-all text-2xl font-semibold"
            type="button"
          >
            <h2
              class=""
              data-testid="flowbite-accordion-heading"
            >
              home.questions.question-1
            </h2>
            <svg
              aria-hidden="true"
              class="h-10 w-10 shrink-0 rotate-180"
              data-testid="flowbite-accordion-arrow"
              fill="currentColor"
              height="1em"
              stroke="currentColor"
              stroke-width="0"
              viewBox="0 0 20 20"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                fill-rule="evenodd"
              />
            </svg>
          </button>
          <div
            class="last:rounded-b-lg dark:bg-gray-900 first:rounded-t-lg p-0 border-none font-rubik overflow-hidden text-base font-normal text-left mb-2"
            data-testid="flowbite-accordion-content"
          >
            <div
              class="[&:not([hidden])]:animate-slideDown origin-top-left transition-all"
            >
              home.questions.answer-1
            </div>
          </div>
          <button
            class="flex w-full items-center justify-between py-[0.88rem] text-left font-outfit focus:ring-gray-200 dark:hover:bg-gray-800 dark:focus:ring-gray-800 border-0 border-t-[0.0625rem] border-black-900 first:rounded-none first:text-black-900 last:rounded-none hover:bg-transparent bg-transparent focus:ring-0 origin-top-left transition-all text-2xl font-semibold"
            type="button"
          >
            <h2
              class=""
              data-testid="flowbite-accordion-heading"
            >
              home.questions.question-2
            </h2>
            <svg
              aria-hidden="true"
              class="h-10 w-10 shrink-0"
              data-testid="flowbite-accordion-arrow"
              fill="currentColor"
              height="1em"
              stroke="currentColor"
              stroke-width="0"
              viewBox="0 0 20 20"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                fill-rule="evenodd"
              />
            </svg>
          </button>
          <div
            class="last:rounded-b-lg dark:bg-gray-900 first:rounded-t-lg p-0 border-none font-rubik overflow-hidden text-base font-normal text-left mb-2"
            data-testid="flowbite-accordion-content"
            hidden=""
          >
            <div
              class="[&:not([hidden])]:animate-slideDown origin-top-left transition-all"
            >
              home.questions.answer-2
            </div>
          </div>
          <button
            class="flex w-full items-center justify-between py-[0.88rem] text-left font-outfit focus:ring-gray-200 dark:hover:bg-gray-800 dark:focus:ring-gray-800 border-0 border-t-[0.0625rem] border-black-900 first:rounded-none first:text-black-900 last:rounded-none hover:bg-transparent bg-transparent focus:ring-0 origin-top-left transition-all text-2xl font-semibold"
            type="button"
          >
            <h2
              class=""
              data-testid="flowbite-accordion-heading"
            >
              home.questions.question-3
            </h2>
            <svg
              aria-hidden="true"
              class="h-10 w-10 shrink-0"
              data-testid="flowbite-accordion-arrow"
              fill="currentColor"
              height="1em"
              stroke="currentColor"
              stroke-width="0"
              viewBox="0 0 20 20"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                fill-rule="evenodd"
              />
            </svg>
          </button>
          <div
            class="last:rounded-b-lg dark:bg-gray-900 first:rounded-t-lg p-0 border-none font-rubik overflow-hidden text-base font-normal text-left mb-2"
            data-testid="flowbite-accordion-content"
            hidden=""
          >
            <div
              class="[&:not([hidden])]:animate-slideDown origin-top-left transition-all"
            >
              home.questions.answer-3
            </div>
          </div>
          <button
            class="flex w-full items-center justify-between py-[0.88rem] text-left font-outfit focus:ring-gray-200 dark:hover:bg-gray-800 dark:focus:ring-gray-800 border-0 border-t-[0.0625rem] border-black-900 first:rounded-none first:text-black-900 last:rounded-none hover:bg-transparent bg-transparent focus:ring-0 origin-top-left transition-all text-2xl font-semibold"
            type="button"
          >
            <h2
              class=""
              data-testid="flowbite-accordion-heading"
            >
              home.questions.question-4
            </h2>
            <svg
              aria-hidden="true"
              class="h-10 w-10 shrink-0"
              data-testid="flowbite-accordion-arrow"
              fill="currentColor"
              height="1em"
              stroke="currentColor"
              stroke-width="0"
              viewBox="0 0 20 20"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                fill-rule="evenodd"
              />
            </svg>
          </button>
          <div
            class="last:rounded-b-lg dark:bg-gray-900 first:rounded-t-lg p-0 border-none font-rubik overflow-hidden text-base font-normal text-left mb-2"
            data-testid="flowbite-accordion-content"
            hidden=""
          >
            <div
              class="[&:not([hidden])]:animate-slideDown origin-top-left transition-all"
            >
              home.questions.answer-4
            </div>
          </div>
          <button
            class="flex w-full items-center justify-between py-[0.88rem] text-left font-outfit focus:ring-gray-200 dark:hover:bg-gray-800 dark:focus:ring-gray-800 border-0 border-t-[0.0625rem] border-black-900 first:rounded-none first:text-black-900 last:rounded-none hover:bg-transparent bg-transparent focus:ring-0 origin-top-left transition-all text-2xl font-semibold"
            type="button"
          >
            <h2
              class=""
              data-testid="flowbite-accordion-heading"
            >
              home.questions.question-5
            </h2>
            <svg
              aria-hidden="true"
              class="h-10 w-10 shrink-0"
              data-testid="flowbite-accordion-arrow"
              fill="currentColor"
              height="1em"
              stroke="currentColor"
              stroke-width="0"
              viewBox="0 0 20 20"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                fill-rule="evenodd"
              />
            </svg>
          </button>
          <div
            class="last:rounded-b-lg dark:bg-gray-900 first:rounded-t-lg p-0 border-none font-rubik overflow-hidden text-base font-normal text-left mb-2"
            data-testid="flowbite-accordion-content"
            hidden=""
          >
            <div
              class="[&:not([hidden])]:animate-slideDown origin-top-left transition-all"
            >
              home.questions.answer-5
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <footer
    aria-hidden="true"
    class="bg-white w-full sm:py-14 py-12"
    data-floating-ui-inert=""
  >
    <div
      class="w-[80%] mx-auto"
    >
      <img
        class="max-[639px]:w-[8rem]"
        src=""
      />
    </div>
  </footer>
</div>
`;
