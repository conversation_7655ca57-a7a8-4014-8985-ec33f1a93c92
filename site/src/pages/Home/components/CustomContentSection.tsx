import { Button, Carrousel, Heading, Image, Text } from "@/components";
import brunaoPersonal from "@/assets/brunaoPersonal.png";
import { Trans, useTranslation } from "react-i18next";
import {
  ArrowRightIcon,
  EnvelopeIcon,
  FingerPrintIcon,
  MegaphoneIcon,
  PaintBrushIcon,
} from "@heroicons/react/24/outline";
import { Card } from "@/components/Card";
import { useState } from "react";
import EarlyAccess from "./EarlyAccess";

export function CustomContentSection() {
  const [showModal, setShowModal] = useState(false);
  const openModal = () => {
    setShowModal(true);
  };
  const { t } = useTranslation();

  const carrouselItems = [
    <Image className="w-full" src={brunaoPersonal} />,
    <Image className="w-full" src={brunaoPersonal} />,
    <Image className="w-full" src={brunaoPersonal} />,
    <Image className="w-full" src={brunaoPersonal} />,
  ];

  const cardsContent = {
    customContent: [
      <div className="w-full text-left p-8">
        <FingerPrintIcon className="text-yellow-800 h-24 w-24 mb-4" />
        <Text className="font-outfit font-bold text-xl leading-5">
          {t("home.custom-content.cards.title-1")}
        </Text>
        <Text className=" mt-3">
          {t("home.custom-content.cards.description-1")}
        </Text>
      </div>,
      <div className="w-full text-left p-8 lg:pb-12">
        <EnvelopeIcon className="text-yellow-800 h-24 w-24 mb-4" />
        <Text className="font-outfit font-bold text-xl leading-5">
          {t("home.custom-content.cards.title-2")}
        </Text>
        <Text className=" mt-3">
          {t("home.custom-content.cards.description-2")}
        </Text>
      </div>,
      <div className="w-full text-left p-8">
        <MegaphoneIcon className="text-yellow-800 h-24 w-24 mb-4" />
        <Text className="font-outfit font-bold text-xl leading-5">
          {t("home.custom-content.cards.title-3")}
        </Text>
        <Text className=" mt-3">
          {t("home.custom-content.cards.description-3")}
        </Text>
      </div>,
      <div className="w-full text-left p-8">
        <PaintBrushIcon className="text-yellow-800 h-24 w-24 mb-4" />
        <Text className="font-outfit font-bold text-xl leading-5">
          {t("home.custom-content.cards.title-4")}
        </Text>
        <Text className=" mt-3">
          {t("home.custom-content.cards.description-4")}
        </Text>
      </div>,
    ],
  };

  return (
    <section>
      <div className="bg-black-900 lg:py-20 py-14">
        <div className="max-w-[100rem] lg:pl-16 max-sm:px-4 mx-auto flex lg:flex-row flex-col xl:gap-16 lg:gap-12">
          <div className="text-white flex flex-col gap-12 sm:text-left lg:max-w-[23rem] sm:max-w-[30rem] max-lg:sm:pl-[15%] max-sm:text-[2rem] max-sm:leading-10 max-xl:lg:min-w-[23rem]">
            <Heading
              data-aos="fade-right"
              data-aos-offset="300"
              data-aos-duration="400"
              className="font-semibold tracking-[-0.0375rem] sm:leading-[3rem] max-sm:text-[2rem] leading-10"
            >
              <Trans i18nKey={"home.custom-content.title-1"}>
                ***<strong className="text-yellow-800"> *** </strong>***
              </Trans>
            </Heading>
            <Text
              data-aos="fade-right"
              data-aos-offset="200"
              data-aos-duration="400"
              className="font-outfit font-bold sm:text-2xl sm:leading-7 text-[1.31rem] leading-6"
            >
              {t("home.custom-content.description")}
            </Text>
            <Button
              data-aos="fade-right"
              onClick={openModal}
              data-aos-offset="100"
              data-aos-duration="400"
              className="max-w-fit flex flex-row items-center justify-center gap-[0.63rem] max-lg:hidden px-3"
              size="xLarge"
            >
              {t("home.buttons.free-test")}
              <ArrowRightIcon className="relative h-4 stroke-[0.25rem]" />
            </Button>
          </div>
          <Carrousel
            data-aos="fade-left"
            data-aos-offset="350"
            data-aos-duration="400"
            className="max-lg:mt-12 min-[1700px]:mr-[-10%] max-xl:lg:min-w-[49rem]"
            variant="secondary"
            position={innerWidth > 639 && innerWidth < 1024 ? "center" : "left"}
            options={{ show: { sm: 1, md: 2, lg: 2 }, items: carrouselItems }}
          />
        </div>
      </div>
      <div className="bg-yellow-800 lg:py-20 py-14 px-4">
        <div className="mx-auto lg:max-w-[76rem] sm:max-w-[51rem] max-w-[33.8rem] lg:px-16">
          <Heading
            data-aos="fade-up"
            data-aos-offset="350"
            className="tracking-[-0.0375rem] font-semibold sm:leading-[3rem] leading-10 max-sm:text-[2rem] sm:text-left max-sm:text-center xl:w-9/12 lg:7/12 sm:w-[72%] min-[469px]:w-7/12 min-[360px]:w-9/12 max-lg:mx-auto"
          >
            {t("home.custom-content.title-2")}
          </Heading>
          <div className="w-full flex flex-row flex-wrap xl:flex-nowrap xl:gap-8  lg:justify-between justify-center mt-14 max-lg:flex-wrap">
            {cardsContent.customContent.map((content, index) => (
              <div
                key={index}
                data-aos="fade-left"
                data-aos-offset="200"
                data-aos-delay={String(200 + index * 50)}
                data-aos-duration="1000"
                className="w-full lg:w-1/2 xl:w-full lg:h-72 max-lg:mb-8 lg:p-4 xl:p-0"
              >
                <Card className="w-full h-full" key={index} color="secondary">
                  {content}
                </Card>
              </div>
            ))}
          </div>
          <Button
            data-aos="fade"
            onClick={openModal}
            data-aos-offset="200"
            className="max-w-fit flex flex-row items-center justify-center gap-[0.63rem] mt-10 mx-auto px-3"
            size="xLarge"
            color="secondary"
          >
            {t("home.buttons.free-test")}
            <ArrowRightIcon className="relative h-4 stroke-[0.25rem]" />
          </Button>
          <EarlyAccess showModal={showModal} setShowModal={setShowModal} />
        </div>
      </div>
    </section>
  );
}

export default CustomContentSection;
