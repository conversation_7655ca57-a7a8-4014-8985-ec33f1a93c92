import { But<PERSON>, Heading, Image, Text } from "@/components";
import { ArrowRightIcon } from "@heroicons/react/24/outline";
import { useTranslation } from "react-i18next";
import phoneGuy from "@/assets/guyWithPhone.png";
import { useState } from "react";
import EarlyAccess from "./EarlyAccess";

export function CampaingnsSection() {
  const [showModal, setShowModal] = useState(false);
  const openModal = () => {
    setShowModal(true);
  };
  const { t } = useTranslation();

  return (
    <section className="lg:py-20 py-14 sm:px-8 px-4">
      <div className="mx-auto bg-white flex sm:flex-row flex-col justify-center lg:gap-24 sm:gap-8 gap-16 rounded-[2rem] max-w-[100rem]">
        <div className="flex flex-col gap-8 lg:max-w-[30%] sm:max-w-[40%] max-sm:px-6 sm:my-auto max-sm:mt-[2.5rem] max-sm:mx-auto">
          <Heading
            data-aos="fade-right"
            data-aos-offset="200"
            className="max-w-lg tracking-[-0.0375rem] text-left lg:font-semibold lg:leading-[3rem] lg:text-[2.5rem] text-[2rem] leading-10"
          >
            {t("home.campaigns.title-1")}
          </Heading>
          <Text
            data-aos="fade-right"
            data-aos-offset="100"
            className="max-w-sm text-left font-outfit font-semibold text-base leading-[1.31rem]"
          >
            {t("home.campaigns.description-1")}
          </Text>
          <Button
            data-aos="fade-right"
            onClick={openModal}
            data-aos-offset="100"
            className="max-w-fit flex flex-row items-center justify-center gap-[0.63rem] px-3"
            color="secondary"
            size="xLarge"
          >
            {t("home.buttons.test-free")}
            <ArrowRightIcon className="relative h-4 stroke-[0.25rem]" />
          </Button>
        </div>
        <Image
          data-aos="fade"
          data-aos-offset="100"
          data-aos-delay="500"
          data-aos-duration="700"
          className="xl:w-[34.935rem] lg:w-[30rem] sm:w-[20.5rem] w-[18.23rem] sm:mt-20 max-sm:mx-auto"
          src={phoneGuy}
        />
      </div>
      <div className="lg:pt-20 pt-14 px-14 flex flex-col max-w-[100rem] mx-auto">
        <Heading
          data-aos="fade-right"
          data-aos-offset="200"
          data-aos-duration="400"
          className="font-semibold tracking-[-0.0375rem] lg:whitespace-nowrap lg:text-left text-center max-sm:text-[2rem] sm:leading-[3rem] leading-[2.5rem] max-[300px]:text-2xl max-sm:mx-[-3rem] xl:w-[70rem] xl:mx-auto lg:mb-14 mb-8"
        >
          {t("home.campaigns.title-2")}
        </Heading>
        <div className="flex lg:flex-row flex-col sm:gap-8 gap-6 max-w-[70rem] xl:mx-auto lg:items-center">
          <div
            data-aos="fade"
            data-aos-offset="300"
            data-aos-duration="500"
            className="sm:h-[28rem] h-[13.5rem] grow flex justify-center items-center bg-white rounded-xl max-sm:mx-[-4rem] max-sm:min-[505px]:w-[22.5rem] max-[504px]:min-[465px]:w-[20rem] max-[465px]:min-[410px]:w-[17rem] max-sm:min-[410px]:mx-auto"
          >
            <p>video</p>
          </div>
          <div className="flex flex-col gap-5 lg:w-[30%] max-[1023px]:mx-auto">
            <Text
              data-aos="fade-up"
              data-aos-offset="200"
              className="lg:text-[2rem] text-2xl font-outfit font-bold text-black-900 leading-9 lg:text-left max-sm:mx-[-4rem]"
            >
              {t("home.campaigns.description-2")}
            </Text>
            <Button
              onClick={openModal}
              data-aos="fade"
              data-aos-offset="100"
              className="max-w-fit flex flex-row items-center justify-center gap-[0.63rem] max-[1023px]:mx-auto px-3"
              size="xLarge"
            >
              {t("home.buttons.free-test")}
              <ArrowRightIcon className="relative h-4 stroke-[0.25rem]" />
            </Button>
            <EarlyAccess showModal={showModal} setShowModal={setShowModal} />
          </div>
        </div>
      </div>
    </section>
  );
}

export default CampaingnsSection;
