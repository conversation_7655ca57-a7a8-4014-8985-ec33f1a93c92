import { use<PERSON><PERSON>, <PERSON>mit<PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import instagram from "@/assets/instagram.svg";
import x from "@/assets/x.svg";
import facebook from "@/assets/facebook.svg";
import tiktok from "@/assets/tiktok.svg";
import linkedin from "@/assets/linkedin.svg";
import youtube from "@/assets/youtube.svg";
import { Button, Heading, Text, Image, DatePicker } from "@/components";
import { useTranslation } from "react-i18next";
import { ArrowRightIcon } from "@heroicons/react/24/outline";
import { Input } from "@/components/Input";
import { format } from "date-fns";

type Inputs = {
  area: string;
  campaign: string;
  startDate: string;
  endDate: string;
};

function Header() {
  const { control, handleSubmit } = useForm<Inputs>();
  const onSubmit: SubmitHandler<Inputs> = (data) => console.log(data);
  const { t } = useTranslation();

  return (
    <div className="lg:mt-20 sm:mt-14 mt-6 text-center lg:px-8 px-4">
      <div data-aos="fade-up" data-aos-delay="200">
        <Heading className="tracking-[-0.0375rem] lg:text-[2.5rem] text-[2rem] leading-10">
          {t("home.title")}
        </Heading>
        <Text className="font-outfit font-semibold mt-6 text-base max-sm:leading-[1.125rem]">
          {t("home.description")}
        </Text>
      </div>
      <div className="flex justify-center lg:mt-14 sm:mt-10 mt-[0.85rem]">
        <div className="flex shrink md:gap-4 min-[370px]:gap-2 gap-1 justify-items-center">
          <div
            data-aos="fade-left"
            data-aos-delay="300"
            className="flex items-center justify-center text-center p-2"
          >
            <Image
              className="sm:h-[2rem] min-[300px]:h-[1.5rem] h-[1.15rem] sm:w-[2rem] min-[300px]:w-[1.5rem] w-[1.15rem] mx-2"
              src={instagram}
              alt="Instagram"
            />
          </div>
          <div
            data-aos="fade-left"
            data-aos-delay="350"
            className="flex items-center justify-center text-center p-2"
          >
            <Image
              className="sm:h-[2rem] min-[300px]:h-[1.5rem] h-[1.15rem] sm:w-[2rem] min-[300px]:w-[1.5rem] w-[1.15rem] mx-1"
              src={x}
              alt="Twitter"
            />
          </div>
          <div
            data-aos="fade-left"
            data-aos-delay="400"
            className="flex items-center justify-center text-center p-2"
          >
            <Image
              className="sm:h-[2.4rem] min-[300px]:h-[1.78rem] h-[1.4rem] sm:w-[2.4rem] min-[300px]:w-[1.78rem] w-[1.4rem]"
              src={facebook}
              alt="Facebook"
            />
          </div>
          <div
            data-aos="fade-left"
            data-aos-delay="450"
            className="flex items-center justify-center text-center p-2"
          >
            <Image
              className="sm:h-[2.4rem] min-[300px]:h-[1.78rem] h-[1.4rem] sm:w-[2.4rem] min-[300px]:w-[1.78rem] w-[1.4rem]"
              src={tiktok}
              alt="TikTok"
            />
          </div>
          <div
            data-aos="fade-left"
            data-aos-delay="500"
            className="flex items-center justify-center text-center p-2"
          >
            <Image
              className="sm:h-[2.4rem] min-[300px]:h-[1.78rem] h-[1.4rem] sm:w-[2.4rem] min-[300px]:w-[1.78rem] w-[1.4rem]"
              src={linkedin}
              alt="Linkedin"
            />
          </div>
          <div
            data-aos="fade-left"
            data-aos-delay="550"
            className="flex items-center justify-center text-center p-2"
          >
            <Image
              className="sm:h-[2.4rem] min-[300px]:h-[1.78rem] h-[1.4rem] sm:w-[2.4rem] min-[300px]:w-[1.78rem] w-[1.4rem]"
              src={youtube}
              alt="Youtube"
            />
          </div>
        </div>
      </div>
      <div
        data-aos="fade"
        data-aos-delay="600"
        className="bg-white pl-[1.35rem] pr-[1.35rem] pt-4 pb-4 lg:mt-14 mt-8 h-fit lg:w-full sm:w-[80%] mx-auto rounded-lg"
      >
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="flex lg:flex-row flex-col flex-grow lg:gap-6 sm:gap-2 gap-6"
        >
          <div className="min-[1250px]:w-full lg:w-[60%] md:w-full flex sm:flex-row flex-col sm:gap-6 gap-6">
            <div className="text-left sm:w-3/5 w-full">
              <label
                className="block text-black-900 text-sm font-bold mb-1 whitespace-nowrap"
                htmlFor="campaign"
              >
                {t("home.label.area")}
              </label>
              <Controller
                control={control}
                name="area"
                render={({
                  field: { onChange, value, name },
                  // fieldState: { invalid, isTouched, isDirty, error },
                  // formState,
                }) => (
                  <Input
                    onChange={onChange}
                    value={value}
                    name={name}
                    className="bg-gray-100 border-none"
                    placeholder={t("home.placeholders.area")}
                    required
                  />
                )}
              />
            </div>
            <div className="text-left sm:w-3/5 w-full">
              <label
                className="block text-black-900 text-sm font-bold mb-1"
                htmlFor="campaign"
              >
                {t("home.label.objective")}
              </label>
              <Controller
                control={control}
                name="campaign"
                render={({
                  field: { onChange, value, name },
                  // fieldState: { invalid, isTouched, isDirty, error },
                  // formState,
                }) => (
                  <Input
                    onChange={onChange}
                    value={value}
                    name={name}
                    className="bg-gray-100 border-none"
                    placeholder={t("home.placeholders.objective")}
                    required
                  />
                )}
              />
            </div>
          </div>
          <div className="flex sm:flex-row flex-col w-full">
            <div className="text-left w-full">
              <label
                className="block text-black-900 text-sm font-bold mb-1"
                htmlFor="campaign"
              >
                {t("home.label.period")}
              </label>
              <div className="flex sm:flex-row flex-col max-sm:w-full sm:items-center sm:justify-center sm:gap-[0.69rem] gap-1">
                <Controller
                  control={control}
                  name="startDate"
                  render={({
                    field: { onChange, name, value },
                    // fieldState: { invalid, isTouched, isDirty, error },
                    // formState,
                  }) => (
                    <DatePicker
                      onSelectedDateChanged={(date) =>
                        onChange(format(date, "dd/MM/yyyy"))
                      }
                      name={name}
                      value={value}
                      variant="secondary"
                      className="bg-gray-100 border-none"
                      placeholder={t("home.placeholders.date")}
                    />
                  )}
                />
                <Text className="font-outfit font-semibold text-base">
                  {t("home.label.preposition")}
                </Text>
                <Controller
                  control={control}
                  name="endDate"
                  render={({
                    field: { onChange, name, value },
                    // fieldState: { invalid, isTouched, isDirty, error },
                    // formState,
                  }) => (
                    <DatePicker
                      onSelectedDateChanged={(date) =>
                        onChange(format(date, "dd/MM/yyyy"))
                      }
                      name={name}
                      value={value}
                      variant="secondary"
                      className="bg-gray-100 border-none"
                      placeholder={t("home.placeholders.date")}
                    />
                  )}
                />
              </div>
            </div>
            <div className="lg:w-1/2 sm:w-3/4 w-full relative sm:ml-4">
              <Button
                color="secondary"
                size="xLarge"
                className="flex flex-row sm:absolute sm:bottom-0 items-center justify-center gap-[0.63rem] lg:h-[95%] h-[64%] px-0 max-sm:mt-8 max-sm:mx-auto"
              >
                {t("home.buttons.generate-posts")}
                <ArrowRightIcon className="h-4 stroke-[0.25rem]" />
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}

export default Header;
