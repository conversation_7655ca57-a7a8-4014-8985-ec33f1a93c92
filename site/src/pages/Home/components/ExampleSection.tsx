import { useEffect, useState, useRef } from "react";
import { <PERSON><PERSON>, Heading, Image, Text } from "@/components";
import { ArrowRightIcon } from "@heroicons/react/24/outline";
import { Trans, useTranslation } from "react-i18next";
import cardBrunaoPersonal from "@/assets/cardBrunaoPersonal.png";
import workOut from "@/assets/workOut.png";
import { EarlyAccess } from "./EarlyAccess";

export function ExampleSection() {
  const [showModal, setShowModal] = useState(false);
  const openModal = () => {
    setShowModal(true);
  };
  const { t } = useTranslation();
  const [onView, setOnView] = useState(false);
  const xPos = useRef<number>(0);
  const scrollPos = useRef<number>(0);
  const animatedRows = useRef<HTMLDivElement | null>(null);

  const scrollDirection = () => {
    const st = document.documentElement.scrollTop;
    let scrollDir;

    if (st > scrollPos.current) {
      scrollDir = "down";
    } else if (st < scrollPos.current) {
      scrollDir = "up";
    }

    scrollPos.current = st <= 0 ? 0 : st;
    return scrollDir;
  };

  const isInViewport = (el: HTMLElement | null) => {
    if (window) {
      const rect = el?.getBoundingClientRect();
      if (rect && rect.top < window.innerHeight && rect.bottom > 0) {
        return true;
      } else {
        return false;
      }
    }
  };

  useEffect(() => {
    const section = animatedRows.current;

    document.addEventListener("scroll", function () {
      const direction = scrollDirection();

      const animateRow = (row: string) => {
        const transformEl = (symbol: string, pos: number) => {
          const element = animatedRows.current?.querySelector(row);
          if (element instanceof HTMLElement) {
            element.style.transform = `translateX(${symbol + pos}px)`;
          }
        };

        if (direction === "down") {
          xPos.current = xPos.current + 1;
          row === "#row-1"
            ? transformEl("+", xPos.current)
            : transformEl("-", xPos.current);
        }

        if (direction === "up" && animatedRows.current) {
          const select = animatedRows.current.querySelector(row);
          if (select) {
            let currPos = select.getBoundingClientRect().x;

            if (row === "#row-1") {
              currPos = currPos - 1;
              transformEl("+", currPos);
            } else {
              currPos = currPos + 1;
              transformEl("+", currPos);
            }
          }
        }
      };

      if (isInViewport(section) === true) {
        animateRow("#row-1");
        animateRow("#row-2");
        onView === true ? "" : setOnView(true);
      } else {
        xPos.current = 0;
        animateRow("#row-1");
        animateRow("#row-2");
        onView === false ? "" : setOnView(false);
      }
    });
  }, [onView]);

  return (
    <section className="bg-black-900 sm:py-20 py-14">
      <Heading
        data-aos="fade-up"
        data-aos-offset="300"
        className="text-white lg:w-4/6 w-11/12 max-w-3xl font-semibold mx-auto text-center tracking-[-0.0375rem] max-lg:text-[2rem] leading-[3rem] max-lg:leading-10"
      >
        <Trans i18nKey="home.examples.title">
          ***
          <strong className="text-yellow-800 font-bold"> ***</strong>
          ***
        </Trans>
      </Heading>
      <div className="w-full mt-14" ref={animatedRows}>
        <div className="flex gap-3 justify-end" id="row-1">
          <Image src={cardBrunaoPersonal} className="w-[19rem] h-[28.5rem]" />
          <Image src={cardBrunaoPersonal} className="w-[19rem] h-[28.5rem]" />
          <Image src={cardBrunaoPersonal} className="w-[19rem] h-[28.5rem]" />
          <Image src={cardBrunaoPersonal} className="w-[19rem] h-[28.5rem]" />
          <Image src={cardBrunaoPersonal} className="w-[19rem] h-[28.5rem]" />
          <Image src={cardBrunaoPersonal} className="w-[19rem] h-[28.5rem]" />
          <Image src={cardBrunaoPersonal} className="w-[19rem] h-[28.5rem]" />
          <Image src={cardBrunaoPersonal} className="w-[19rem] h-[28.5rem]" />
        </div>
        <div className="flex gap-3 justify-start mt-4" id="row-2">
          <Image src={workOut} className="w-72 h-[30rem]" />
          <Image src={workOut} className="w-72 h-[30rem]" />
          <Image src={workOut} className="w-72 h-[30rem]" />
          <Image src={workOut} className="w-72 h-[30rem]" />
          <Image src={workOut} className="w-72 h-[30rem]" />
          <Image src={workOut} className="w-72 h-[30rem]" />
          <Image src={workOut} className="w-72 h-[30rem]" />
          <Image src={workOut} className="w-72 h-[30rem]" />
        </div>
      </div>
      <Text
        data-aos="fade-up"
        data-aos-offset="100"
        data-aos-delay="400"
        className="font-outfit font-bold text-2xl leading-7 text-white mx-auto mt-[3.60rem] text-center max-md:w-11/12 max-sm:w-9/12 max-[480px]:w-11/12 max-[360px]:w-9/12"
      >
        {t("home.examples.punchline")}
      </Text>
      <div className="inline-block">
        <Button
          data-aos="fade-up"
          onClick={openModal}
          data-aos-offset="100"
          data-aos-delay="400"
          size="xLarge"
          className="flex flex-row items-center justify-center gap-[0.63rem] mt-6 px-3"
        >
          {t("home.buttons.test-free")}
          <ArrowRightIcon className="relative h-4 stroke-[0.25rem]" />
        </Button>
        <EarlyAccess showModal={showModal} setShowModal={setShowModal} />
      </div>
    </section>
  );
}

export default ExampleSection;
