import { But<PERSON>, Head<PERSON>, Text } from "@/components";
import { ArrowRightIcon } from "@heroicons/react/24/outline";
import { useTranslation } from "react-i18next";
import EarlyAccess from "./EarlyAccess";
import { useState } from "react";

export function MultitoolSection() {
  const [showModal, setShowModal] = useState(false);
  const openModal = () => {
    setShowModal(true);
  };
  const { t } = useTranslation();

  const toolsContent = [
    {
      gif: "Vídeo ou Gif demonstrando",
      title: t("home.multitool.items.title-1"),
      description: t("home.multitool.items.description-1"),
    },
    {
      gif: "Vídeo ou Gif demonstrando",
      title: t("home.multitool.items.title-2"),
      description: t("home.multitool.items.description-2"),
    },
    {
      gif: "Vídeo ou Gif demonstrando",
      title: t("home.multitool.items.title-3"),
      description: t("home.multitool.items.description-3"),
    },
    {
      gif: "Vídeo ou Gif demonstrando",
      title: t("home.multitool.items.title-4"),
      description: t("home.multitool.items.description-4"),
    },
  ];

  return (
    <section className="lg:py-20 py-14 sm:px-8 px-4">
      <div className="bg-white rounded-[2rem] lg:py-[5.5rem] py-[3.5rem] max-[639px]:px-4 max-w-[100rem] mx-auto">
        <Heading
          data-aos="fade-up"
          data-aos-offset="300"
          className="lg:w-10/12 sm:w-5/6 min-[455px]:w-11/12 min-[430px]:w-full min-[365px]:w-10/12 max-w-[90rem] text-center mx-auto tracking-[-0.0375rem] font-bold lg:leading-[3rem] sm:leading-[2.5rem] leading-9 max-lg:text-[2rem] max-sm:text-[1.75rem]"
        >
          {t("home.multitool.title")}
        </Heading>
        <Text
          data-aos="fade-up"
          data-aos-offset="200"
          className="font-outfit font-semibold lg:text-2xl sm:text-[1.31rem] text-[1.125rem] sm:leading-7 leading-6 lg:w-7/12 sm:w-4/5 max-w-[54rem] text-center mx-auto lg:mt-6 mt-4"
        >
          {t("home.multitool.description")}
        </Text>
        <div className="lg:mt-24 sm:mt-[5.5rem] mt-10 mx-auto flex flex-col lg:gap-36 sm:gap-20 gap-12 max-w-[65rem] lg:w-9/12 sm:w-4/5">
          {toolsContent.map((content, index) => (
            <div
              key={content.title}
              className="w-full flex sm:flex-row flex-col gap-4 justify-between items-center"
            >
              {index % 2 === 0 ? (
                <>
                  <div
                    data-aos="fade-up"
                    data-aos-offset="350"
                    className="bg-gray-100 lg:w-[27rem] lg:h-[22rem] sm:w-[23rem] sm:h-[18.25rem] min-[375px]:w-[18.5rem] min-[375px]:h-[14.75rem] min-[340px]:w-[16.7rem] min-[340px]:h-[12.25rem] w-[14rem] h-[11rem] rounded-lg flex items-center"
                  >
                    <p className="mx-auto">{content.gif}</p>
                  </div>
                  <div
                    data-aos="fade-left"
                    data-aos-offset="300"
                    className="lg:max-w-[45%] sm:max-w-[40%] sm:text-left text-center"
                  >
                    <Text className="font-outfit font-semibold lg:text-[2rem] text-[1.5rem] text-yellow-900 tracking-[-0.0375rem] lg:leading-9 leading-7">
                      {content.title}
                    </Text>
                    <Text className="font-outfit lg:text-2xl text-[1.125rem] font-medium lg:leading-7 leading-[1.31rem] sm:mt-4 mt-8">
                      {content.description}
                    </Text>
                  </div>
                </>
              ) : (
                <>
                  <div
                    data-aos="fade-right"
                    data-aos-offset="300"
                    className="lg:max-w-[45%] sm:max-w-[40%] max-[639px]:hidden sm:text-left text-center"
                  >
                    <Text className="font-outfit font-semibold lg:text-[2rem] text-[1.5rem] text-yellow-900 tracking-[-0.0375rem] lg:leading-9 leading-7">
                      {content.title}
                    </Text>
                    <Text className="font-outfit lg:text-2xl text-[1.125rem] font-medium lg:leading-7 leading-[1.31rem] sm:mt-4 mt-8">
                      {content.description}
                    </Text>
                  </div>
                  <div
                    data-aos="fade-up"
                    data-aos-offset="350"
                    className="bg-gray-100 lg:w-[27rem] lg:h-[22rem] sm:w-[23rem] sm:h-[18.25rem] min-[375px]:w-[18.5rem] min-[375px]:h-[14.75rem] min-[340px]:w-[16.7rem] min-[340px]:h-[12.25rem] w-[14rem] h-[11rem] rounded-lg flex items-center"
                  >
                    <p className="mx-auto">{content.gif}</p>
                  </div>
                  <div
                    data-aos="fade-left"
                    data-aos-offset="300"
                    className="lg:max-w-[45%] sm:max-w-[40%] sm:text-left text-center sm:hidden"
                  >
                    <Text className="font-outfit font-semibold lg:text-[2rem] text-[1.5rem] text-yellow-900 tracking-[-0.0375rem] lg:leading-9 leading-7">
                      {content.title}
                    </Text>
                    <Text className="font-outfit lg:text-2xl text-[1.125rem] font-medium lg:leading-7 leading-[1.31rem] sm:mt-4 mt-8">
                      {content.description}
                    </Text>
                  </div>
                </>
              )}
            </div>
          ))}
        </div>
        <Button
          data-aos="fade"
          data-aos-offset="250"
          onClick={openModal}
          className="max-w-fit flex flex-row items-center justify-center gap-[0.63rem] mx-auto mt-[5.5rem] px-3"
          size="xLarge"
        >
          {t("home.buttons.test-free")}
          <ArrowRightIcon className="relative h-4 stroke-[0.25rem]" />
        </Button>
        <EarlyAccess showModal={showModal} setShowModal={setShowModal} />
      </div>
    </section>
  );
}

export default MultitoolSection;
