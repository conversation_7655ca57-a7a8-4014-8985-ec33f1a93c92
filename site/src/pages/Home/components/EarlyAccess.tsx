import { useState } from "react";
import { Button, Heading, Input, Modal, Text } from "@/components";
import { RoundedIcon } from "@/components/CustomIcons";
import { HandThumbUpIcon } from "@heroicons/react/24/outline";
import { <PERSON>dalHeader, ModalBody } from "flowbite-react";
import { useTranslation } from "react-i18next";

interface EarlyAccessProps {
  showModal: boolean;
  setShowModal: (show: boolean) => void;
}

export function EarlyAccess({ showModal, setShowModal }: EarlyAccessProps) {
  const { t } = useTranslation();
  const [showSecondModal, setShowSecondModal] = useState(false);

  const handleFirstModalClose = () => {
    setShowModal(false);
  };

  const handleSecondModalClose = () => {
    setShowSecondModal(false);
    setShowModal(false);
  };

  const handleCadastrarSeClick = () => {
    setShowModal(false);
    setShowSecondModal(true);
  };

  return (
    <>
      {showModal && (
        <Modal
          size="md"
          className="flex-col inline-flex gap-6"
          show={showModal}
          onClose={handleFirstModalClose}
        >
          <ModalHeader>
            <Heading size="3" className="mt-10">
              {t("createclient.components.modalhome.hadear")}
            </Heading>
            <div className="flex mt-6 items-center justify-center mx-auto">
              <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
                {t("createclient.components.modalhome.hadear2")}
              </Text>
            </div>
          </ModalHeader>
          <ModalBody>
            <form className="flex flex-col">
              <div className="flex gap-6 mb-4">
                <div className="mb-4">
                  <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
                    {t("createclient.components.modalhome.body.text1")}
                  </Text>
                  <Input state="default" className="border-gray-300" />
                </div>
                <div className="mb-4">
                  <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
                    {t("createclient.components.modalhome.body.text2")}
                  </Text>
                  <Input state="default" className="border-gray-300" />
                </div>
              </div>
              <div className="mb-4">
                <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
                  {t("createclient.components.modalhome.body.text3")}
                </Text>
                <Input state="default" className="border-gray-300" />
              </div>
            </form>
            <div className="flex mt-7 gap-4">
              <Button color="secondary" onClick={handleFirstModalClose}>
                {t("createclient.components.modalhome.fotter.button1")}
              </Button>
              <Button onClick={handleCadastrarSeClick}>
                {t("createclient.components.modalhome.fotter.button2")}
              </Button>
            </div>
          </ModalBody>
        </Modal>
      )}
      {showSecondModal && (
        <Modal
          size="md"
          className="flex-col inline-flex gap-6"
          show={showSecondModal}
          onClose={handleSecondModalClose}
        >
          <ModalHeader>
            <div className="flex items-center justify-center mx-auto">
              <RoundedIcon>
                <HandThumbUpIcon width="40px" height="40px" />
              </RoundedIcon>
            </div>
            <Heading size="3" className="mt-10">
              {t("createclient.components.modalhome.hadear3")}
            </Heading>
            <div className="flex mt-6 items-center justify-center mx-auto">
              <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
                {t("createclient.components.modalhome.body.text4")}
              </Text>
            </div>
          </ModalHeader>
          <ModalBody>
            <div className="flex mt-7 gap-4 justify-center">
              <Button onClick={handleSecondModalClose}>
                {t("createclient.components.modalhome.fotter.botton3")}
              </Button>
            </div>
          </ModalBody>
        </Modal>
      )}
    </>
  );
}

export default EarlyAccess;
