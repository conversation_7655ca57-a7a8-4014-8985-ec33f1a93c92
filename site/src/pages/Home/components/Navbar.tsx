import { Link } from "react-router-dom";
import { Button, Image } from "@/components";
import { useTranslation } from "react-i18next";

import logo from "@/assets/logo.svg";

function Navbar() {
  const { t } = useTranslation();

  return (
    <nav>
      <div className="relative flex h-14 items-center justify-between px-6">
        <Link to="/">
          <Image src={logo} alt="Conteoodo" className="h-7" />
        </Link>
        <div className="right-0 flex gap-12 items-center pr-2">
          <Link to="/sign-in">
            <p className="bg-transparent no-underline font-outfit font-medium cursor-pointer">
              {t("home.buttons.login")}
            </p>
          </Link>
          <Link to="/sign-up">
            <Button className="whitespace-nowrap">
              {t("home.buttons.sign-up")}
            </Button>
          </Link>
        </div>
      </div>
    </nav>
  );
}

export default Navbar;
