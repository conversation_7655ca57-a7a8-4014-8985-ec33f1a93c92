import { Heading, Text } from "@/components";
import { Trans, useTranslation } from "react-i18next";

export function HowItWorksSection() {
  const { t } = useTranslation();

  return (
    <section className="bg-black-900 lg:mt-20 mt-14 lg:py-20 py-14 max-lg:px-4">
      <Heading className="text-white max-w-3xl mx-auto text-center font-semibold tracking-[-0.0375rem] max-[639px]:text-[1.75rem] leading-[3rem] max-[639px]:leading-8">
        <Trans i18nKey="home.how-works.title">
          ***
          <strong className="text-yellow-800 font-bold"> ***</strong>
        </Trans>
      </Heading>
      <div className="mt-14 flex sm:flex-row flex-col lg:gap-[9.3rem] gap-20 justify-center">
        <div className="flex flex-col items-center gap-8">
          <div
            data-aos="fade-right"
            data-aos-offset="300"
            className="lg:h-[36rem] h-[31.25rem] lg:w-72 w-[16.25rem] bg-white rounded-[2rem] flex items-center justify-center"
          >
            <p>video</p>
          </div>
          <div data-aos="fade-up" data-aos-offset="200">
            <Text className="font-outfit font-bold text-[2rem] text-yellow-800">
              {t("home.how-works.mini-title-1")}
            </Text>
            <Text className="font-outfit font-bold text-2xl leading-7 text-white max-w-[15rem] text-center">
              {t("home.how-works.mini-description")}
            </Text>
          </div>
        </div>
        <div className="flex flex-col items-center gap-8">
          <div
            data-aos="fade-left"
            data-aos-offset="300"
            className="lg:h-[36rem] h-[31.25rem] lg:w-72 w-[16.25rem] bg-white rounded-[2rem] flex items-center justify-center"
          >
            <p>video</p>
          </div>
          <div data-aos="fade-up" data-aos-offset="200">
            <Text className="font-outfit font-bold text-[2rem] text-yellow-800">
              {t("home.how-works.mini-title-2")}
            </Text>
            <Text className="font-outfit font-bold text-2xl leading-7 text-white max-w-[15rem] text-center">
              {t("home.how-works.mini-description")}
            </Text>
          </div>
        </div>
      </div>
    </section>
  );
}

export default HowItWorksSection;
