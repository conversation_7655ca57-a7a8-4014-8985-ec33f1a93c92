import { useState } from "react";
import Navbar from "./components/Navbar";
import Header from "./components/Header";
import ExampleSection from "./components/ExampleSection";
import { HowItWorksSection } from "./components/HowItWorksSection";
import CampaingnsSection from "./components/CampaignsSection";
import CustomContentSection from "./components/CustomContentSection";
import MultitoolSection from "./components/MultitoolSection";
import FAQSection from "./components/FAQSection";
import Footer from "./components/Footer";
import { EarlyAccess } from "./components/EarlyAccess";
import AOS from "aos";
import "aos/dist/aos.css";

function Home() {
  AOS.init();

  const [showModal, setShowModal] = useState(true);

  return (
    <div
      data-testid="home"
      className="w-full font-outfit text-center 
      before:content-[''] before:opacity-35 before:blur-3xl before:fixed before:top-[0] before:left-[-20%] before:w-[2000px] before:h-[2000px] before:rounded-full before:block before:z-[-2] before:bg-amber-100 before:border-solid before:border-[200px] before:border-amber-50 before:opacity-80
      after:blur-3xl after:content-[''] after:fixed after:top-[180px] after:right-[-35%] after:w-[2000px] after:h-[2000px] after:rounded-full after:block after:z-[-1] after:bg-slate-300 after:border-solid after:border-[200px] after:border-slate-5 after:opacity-80
      "
    >
      <Navbar />
      <div className="lg:max-w-[1246px] md:max-w-[1000px] mx-auto">
        <Header />
      </div>
      <HowItWorksSection />
      <CampaingnsSection />
      <CustomContentSection />
      <MultitoolSection />
      <ExampleSection />
      <FAQSection />
      <Footer />
      <EarlyAccess showModal={showModal} setShowModal={setShowModal} />
    </div>
  );
}

export default Home;
