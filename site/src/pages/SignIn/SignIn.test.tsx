import { fireEvent, render, screen } from "@tests/render";
import SignIn from "./SignIn";

describe("<SignIn />", () => {
  it("should render the page", () => {
    render(<SignIn onEmailSignIn={jest.fn()} onGoogleSignIn={jest.fn()} />);
    expect(screen.getByTestId("sign-in")).toMatchSnapshot();
  });

  it("should click on google button", () => {
    const onGoogleSignIn = jest.fn();

    render(
      <SignIn onEmailSignIn={jest.fn()} onGoogleSignIn={onGoogleSignIn} />,
    );
    const button = screen.getByTestId("google-btn");

    fireEvent.click(button);

    expect(onGoogleSignIn).toHaveBeenCalledTimes(1);
  });

  it("should click on email button", () => {
    const onEmailSignIn = jest.fn();

    render(<SignIn onEmailSignIn={onEmailSignIn} onGoogleSignIn={jest.fn()} />);
    const button = screen.getByTestId("email-btn");

    fireEvent.click(button);

    expect(onEmailSignIn).toHaveBeenCalledTimes(1);
  });
});
