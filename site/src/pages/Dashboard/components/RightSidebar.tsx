import { Card, Text, Image } from "@/components";
import brunaoPersonal from "@/assets/brunaoPersonal.png";
import { Heading } from "@/components";
import {
  BuildingStorefrontIcon,
  ClockIcon,
  MegaphoneIcon,
} from "@heroicons/react/24/outline";
import {
  RoundedFacebookIcon,
  RoundedInstagramIcon,
  RoundedLinkedinIcon,
} from "@/components/CustomIcons";
import { HTMLAttributes } from "react";

interface RightSidebarProps extends HTMLAttributes<HTMLDivElement> {}

export function RightSidebar(props: RightSidebarProps) {
  const recentCardData = {
    title: "Postados Recentemente",
    image: brunaoPersonal,
    additionalInfo: "Hábitos Saudáveis",
    additionalUser: "Fernanda Nutricionista",
    additionalTime: "17:30",
    socialMedia: [
      { name: "Facebook", icon: <RoundedFacebookIcon className="w-4 h-4" /> },
      { name: "Instagram", icon: <RoundedInstagramIcon className="w-4 h-4" /> },
      { name: "LinkedIn", icon: <RoundedLinkedinIcon className="w-4 h-4" /> },
    ],
  };

  return (
    <section className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-1">
      <div className="flex flex-col gap-4" {...props}>
        <Heading size="4" className="font-normal">
          {recentCardData.title}
        </Heading>
        <div className="flex flex-col">
          {[...Array(3)].map((_, cardIndex) => (
            <Card
              key={cardIndex}
              fullWidth
              color="quaternary"
              className="flex flex-row mb-4"
            >
              <Image
                className="w-16 h-16 rounded-xl mr-[0.5rem]"
                src={recentCardData.image}
              />
              <div className="flex flex-col w-full">
                <div className="flex flex-row items-center">
                  <MegaphoneIcon className="w-[0.65513rem] h-[0.62938rem] text-gray-800 mr-[0.16rem]" />
                  <Text className="font-outfit text-[.625rem] text-black-900 font-semibold">
                    {recentCardData.additionalInfo}
                  </Text>
                </div>
                <div className="flex flex-row items-center">
                  <BuildingStorefrontIcon className="w-[0.65513rem] h-[0.62938rem] text-gray-800 mr-[0.16rem]" />
                  <Text className="font-outfit text-[.625rem]">
                    {recentCardData.additionalUser}
                  </Text>
                </div>
                <div className="flex flex-row items-center">
                  <ClockIcon className="w-[0.65513rem] h-[0.62938rem] text-gray-800 mr-[0.16rem]" />
                  <Text className="font-outfit text-[.625rem]">
                    {recentCardData.additionalTime}
                  </Text>
                </div>
                <div className="flex gap-1 w-full">
                  {recentCardData.socialMedia?.map(({ icon: Icon }) => (
                    <>{Icon}</>
                  ))}
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      <div className="flex flex-col gap-4" {...props}>
        <Heading size="4" className="font-normal">
          {recentCardData.title}
        </Heading>
        <div className="flex flex-col">
          {[...Array(3)].map((_, cardIndex) => (
            <Card
              key={cardIndex}
              fullWidth
              color="quaternary"
              className="flex flex-row mb-4"
            >
              <Image
                className="w-16 h-16 rounded-xl mr-[0.5rem]"
                src={recentCardData.image}
              />
              <div className="flex flex-col w-full">
                <div className="flex flex-row items-center">
                  <MegaphoneIcon className="w-[0.65513rem] h-[0.62938rem] text-gray-800 mr-[0.16rem]" />
                  <Text className="font-outfit text-[.625rem] text-black-900 font-semibold">
                    {recentCardData.additionalInfo}
                  </Text>
                </div>
                <div className="flex flex-row items-center">
                  <BuildingStorefrontIcon className="w-[0.65513rem] h-[0.62938rem] text-gray-800 mr-[0.16rem]" />
                  <Text className="font-outfit text-[.625rem]">
                    {recentCardData.additionalUser}
                  </Text>
                </div>
                <div className="flex flex-row items-center">
                  <ClockIcon className="w-[0.65513rem] h-[0.62938rem] text-gray-800 mr-[0.16rem]" />
                  <Text className="font-outfit text-[.625rem]">
                    {recentCardData.additionalTime}
                  </Text>
                </div>
                <div className="flex gap-1 w-full">
                  {recentCardData.socialMedia?.map(({ icon: Icon }) => (
                    <>{Icon}</>
                  ))}
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      <div className="flex flex-col gap-4" {...props}>
        <Heading size="4" className="font-normal">
          {recentCardData.title}
        </Heading>
        <div className="flex flex-col">
          {[...Array(3)].map((_, cardIndex) => (
            <Card
              key={cardIndex}
              fullWidth
              color="quaternary"
              className="flex flex-row mb-4"
            >
              <Image
                className="w-16 h-16 rounded-xl mr-[0.5rem]"
                src={recentCardData.image}
              />
              <div className="flex flex-col w-full">
                <div className="flex flex-row items-center">
                  <MegaphoneIcon className="w-[0.65513rem] h-[0.62938rem] text-gray-800 mr-[0.16rem]" />
                  <Text className="font-outfit text-[.625rem] text-black-900 font-semibold">
                    {recentCardData.additionalInfo}
                  </Text>
                </div>
                <div className="flex flex-row items-center">
                  <BuildingStorefrontIcon className="w-[0.65513rem] h-[0.62938rem] text-gray-800 mr-[0.16rem]" />
                  <Text className="font-outfit text-[.625rem]">
                    {recentCardData.additionalUser}
                  </Text>
                </div>
                <div className="flex flex-row items-center">
                  <ClockIcon className="w-[0.65513rem] h-[0.62938rem] text-gray-800 mr-[0.16rem]" />
                  <Text className="font-outfit text-[.625rem]">
                    {recentCardData.additionalTime}
                  </Text>
                </div>
                <div className="flex gap-1 w-full">
                  {recentCardData.socialMedia?.map(({ icon: Icon }) => (
                    <>{Icon}</>
                  ))}
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
