import { Card, Heading, Text, Image, Button } from "@/components";
import brunaoPersonal from "@/assets/brunaoPersonal.png";
import {
  BuildingStorefrontIcon,
  CalendarDaysIcon,
  CheckIcon,
  SignalIcon,
  MegaphoneIcon,
  ShareIcon,
} from "@heroicons/react/24/outline";
import {
  FacebookIcon,
  InstagramIcon,
  LinkedInIcon,
  RoundedFacebookIcon,
  RoundedInstagramIcon,
  RoundedLinkedinIcon,
} from "@/components/CustomIcons";
import { HTMLAttributes } from "react";

interface MainContentProps extends HTMLAttributes<HTMLDivElement> {}

export function MainContent(props: MainContentProps) {
  const cardsData = [
    {
      title: "Agendados para hoje",
      icon: CalendarDaysIcon,
      count: 1300,
      buttonText: "Ver Todos",
      image: brunaoPersonal,
      additionalInfo: "Hábitos Saudáveis Co...",
      additionalUser: "Fernanda Nutricionista",
      additionalCampaign: [{ name: "<PERSON> andament<PERSON>" }, { name: "Finalizado" }],
      datatPublic: "12/12/2015 a 20/02/2022",
      socialMedia: [
        { name: "Facebook", icon: FacebookIcon },
        { name: "Instagram", icon: InstagramIcon },
        { name: "LinkedIn", icon: LinkedInIcon },
      ],
    },
    {
      title: "Campanhas Ativas",
      icon: MegaphoneIcon,
      count: 1000,
      buttonText: "Ver Todos",
      image: brunaoPersonal,
      additionalInfo: "Hábitos Saudáveis",
      additionalUser: "Fernanda Nutricionista",
      additionalCampaign: [{ name: "Em andamento" }, { name: "Finalizado" }],
      datatPublic: "12/12/2015 a 20/02/2022",
      socialMedia: [
        { name: "Facebook", icon: FacebookIcon },
        { name: "Instagram", icon: InstagramIcon },
        { name: "LinkedIn", icon: LinkedInIcon },
      ],
    },
    {
      title: "Contas de Clientes",
      icon: MegaphoneIcon,
      count: 1500,
      buttonText: "Ver Todos",
      image: brunaoPersonal,
      additionalUser: "Fernanda Nutricionista",
    },
  ];

  return (
    <div className="container m-auto" {...props}>
      <Heading size="3">Olá, Bruno! Bora criar conteúdo?</Heading>

      {cardsData.map((card, cardIndex) => (
        <section key={cardIndex} className="flex flex-col gap-4 mt-8">
          <Text className="flex items-center font-outfit text-[1.5rem] font-semibold">
            <card.icon className="w-6 h-6 mr-1" />
            {card.title}
          </Text>

          <div className="grid gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-5">
            {[...Array(10)].map((_, index) => (
              <Card key={index} color="quinternary">
                <Image
                  className="rounded-[0.75rem] mb-2 w-full"
                  src={card.image}
                />

                <div className="flex flex-col gap-1">
                  <div className="flex flex-row items-center gap-1">
                    <MegaphoneIcon className="w-4 h-4 text-gray-800" />
                    <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.3125rem]">
                      {card.additionalInfo}
                    </Text>
                  </div>
                  <div className="flex flex-row items-center gap-1">
                    <BuildingStorefrontIcon className="w-4 h-4 text-gray-800" />
                    <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                      {card.additionalUser}
                    </Text>
                  </div>
                  {card.additionalCampaign?.map((campaign, idx) => (
                    <div key={idx} className="flex flex-row items-center gap-1">
                      {idx === 0 && campaign.name === "Em andamento" && (
                        <>
                          <SignalIcon className="w-4 h-4 text-gray-800" />
                          <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                            {campaign.name}
                          </Text>
                        </>
                      )}
                      {idx === 1 && campaign.name === "Finalizado" && (
                        <div style={{ display: "none" }}>
                          <CheckIcon className="w-4 h-4  text-gray-800" />
                          <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                            {campaign.name}
                          </Text>
                        </div>
                      )}
                      {idx === 2 && campaign.name === "Em andamento" && (
                        <>
                          <SignalIcon className="w-[0.65513rem] h-[0.62938rem] text-gray-800" />
                          <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                            {campaign.name}
                          </Text>
                        </>
                      )}
                      {idx !== 0 && idx !== 1 && idx !== 2 && (
                        <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                          {campaign.name}
                        </Text>
                      )}
                    </div>
                  ))}
                  <div className="flex flex-row items-center gap-1">
                    <CalendarDaysIcon className="w-4 h-4 text-gray-800" />
                    <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                      {card.datatPublic}
                    </Text>
                  </div>
                  <div className="flex flex-row items-center gap-1">
                    {card.socialMedia?.map((social, index) => (
                      <div key={index} className="flex gap-1">
                        {index === 0 && (
                          <ShareIcon className="w-4 h-4 mt-1 mr-1 text-gray-800" />
                        )}
                        {social.name === "Facebook" && (
                          <RoundedFacebookIcon className="w-5 h-5" />
                        )}
                        {social.name === "Instagram" && (
                          <RoundedInstagramIcon className="w-5 h-5" />
                        )}
                        {social.name === "LinkedIn" && (
                          <RoundedLinkedinIcon className="w-5 h-5" />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </Card>
            ))}
          </div>

          <div className="w-full text-right">
            <Button color="secondary" className="w-auto">
              Ver Todos agendados
            </Button>
          </div>
        </section>
      ))}
    </div>
  );
}
