import { render, fireEvent, screen } from "@tests/render";
import CreateClient from "./CreateClient";

describe("CreateLogo component", () => {
  it("should render the page", () => {
    render(<CreateClient />);
    expect(screen.getByTestId("ClientCreate")).toMatchSnapshot();
  });

  it("should open modal when clicking on the plus button", () => {
    render(<CreateClient />);
    expect(screen.queryByTestId("modalcreatelogo")).toBeNull();

    fireEvent.click(screen.getByTestId("plus-button"));
    const modals = screen.getAllByTestId("modalcreatelogo");
    console.log("Número de modals:", modals.length);
    expect(modals).toHaveLength(2);
  });
});
