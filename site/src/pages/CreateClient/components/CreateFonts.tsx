import { useState } from "react";
import { <PERSON>, Heading, But<PERSON>, Text, Dropdown } from "@/components";
import { PlusIcon } from "@heroicons/react/24/outline";

import { HTMLAttributes } from "react";
import { Modal, ModalBody, ModalHeader } from "@/components/Modal";
import { RoundedIcon, TIcon } from "@/components/CustomIcons";
import { useTranslation } from "react-i18next";

interface CreateFontsProps extends HTMLAttributes<HTMLDivElement> {}

export function CreateFonts(props: CreateFontsProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { t } = useTranslation();

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const handleDropdownChange = (
    selectedOption: { value: string; label: string } | null,
  ) => {
    console.log(selectedOption);
  };

  return (
    <section className="flex gap-8" {...props}>
      <div className="grid gap-4 lg:grid-cols-3 grid-cols-1">
        <div className="w-full drop-zone border-2 mt-1 flex flex-col justify-center items-center gap-2 p-2 py-4 rounded-[0.75rem] border-dashed border-gray-300 active:border-yellow-900 bg-[#FCFCFC] hover:bg-[#FFCC3333] hover:text-yellow-900 text-gray-300">
          <Button className="bg-gray-300 w-fit p-3" onClick={openModal}>
            <PlusIcon className="w-5 h-5 justify-normal" />
          </Button>
          <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
            {t("createclient.components.modalcreatefonts.hadear")}
          </Text>
        </div>
        {[...Array(6)].map((_, index) => (
          <Card key={index} color="quinternary">
            <div className="grid grid-cols-1 gap-4">
              <div className="grid grid-cols-1 gap-2">
                <Text className="font-outfit text-xs text-yellow-900 font-semibold">
                  Fonte Display
                </Text>
                <Text className="text-sm font-normal">
                  Playfair Display Bold
                </Text>
                <Text className="font-outfit font-bold text-lg">
                  The quick brown fox jumps over the lazy dog
                </Text>
              </div>
              <div className="grid grid-cols-1 gap-2">
                <Text className="font-outfit text-xs text-yellow-900 font-semibold">
                  Fonte Corpo
                </Text>
                <Text className="text-sm font-normal">Open Sans Regular</Text>
                <Text className="font-outfit font-normal text-base">
                  The quick brown fox jumps over the lazy dog
                </Text>
              </div>
            </div>
            <div className="inline-grid grid-cols-2 mt-4 gap-2">
              <Button className="py-[0.375rem]" color="quintenary">
                {t("createclient.components.card.botton1")}
              </Button>
              <Button className="py-[0.375rem]" color="secondary">
                {t("createclient.components.card.botton2")}
              </Button>
            </div>
          </Card>
        ))}
      </div>
      {isModalOpen && (
        <Modal
          size="md"
          className="flex-col inline-flex gap-6"
          show={isModalOpen}
          onClose={closeModal}
        >
          <ModalHeader>
            <div className="flex items-center justify-center mx-auto">
              <RoundedIcon>
                <TIcon width="40px" height="40px" />
              </RoundedIcon>
            </div>
            <Heading size="3" className="mt-6">
              {t("createclient.components.modalcreatefonts.hadear")}
            </Heading>
          </ModalHeader>
          <ModalBody>
            <form className="flex flex-col gap-4">
              <div className="flex gap-8 mb-4">
                <div className="w-full">
                  <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
                    {t("createclient.components.modalcreatefonts.body.text1")}
                  </Text>
                  <Dropdown
                    onChange={handleDropdownChange}
                    loadOptions={() => {}}
                  />
                </div>
                <div className="w-3/5">
                  <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
                    {t("createclient.components.modalcreatefonts.body.text2")}
                  </Text>
                  <Dropdown
                    onChange={handleDropdownChange}
                    loadOptions={() => {}}
                  />
                </div>
              </div>
              <div className="flex gap-8 mb-4">
                <div className="w-full">
                  <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
                    {t("createclient.components.modalcreatefonts.body.text3")}
                  </Text>
                  <Dropdown
                    onChange={handleDropdownChange}
                    loadOptions={() => {}}
                  />
                </div>
                <div className="w-3/5">
                  <div className="mb-4">
                    <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
                      {t("createclient.components.modalcreatefonts.body.text4")}
                    </Text>
                    <Dropdown
                      onChange={handleDropdownChange}
                      loadOptions={() => {}}
                    />
                  </div>
                </div>
              </div>
            </form>
            <div className="flex gap-4">
              <Button color="secondary" onClick={closeModal}>
                {t("createclient.components.modalcreatefonts.fotter.button1")}
              </Button>
              <Button>
                {t("createclient.components.modalcreatefonts.fotter.button2")}
              </Button>
            </div>
          </ModalBody>
        </Modal>
      )}
    </section>
  );
}
