import React from "react";
import { Tag } from "@/components";
import {
  FingerPrintIcon,
  PaintBrushIcon,
  DocumentTextIcon,
  PhotoIcon,
} from "@heroicons/react/24/outline";
import { useTranslation } from "react-i18next";

interface MenuTagsProps {
  setMenu: (newMenu: string) => void;
  menu: string;
}

const MenuTags: React.FC<MenuTagsProps> = ({ setMenu, menu }) => {
  const { t } = useTranslation();

  return (
    <div className="flex flex-row gap-3">
      <Tag
        className="cursor-pointer"
        variant={menu === "logo" ? "quaternary" : "primary"}
        onClick={() => setMenu("logo")}
      >
        <FingerPrintIcon className="w-3 h-3 mr-0" />
        {t("createclient.components.menutags.tag1")}
      </Tag>
      <Tag
        className="cursor-pointer"
        variant={menu === "color" ? "quaternary" : "primary"}
        onClick={() => setMenu("color")}
      >
        <PaintBrushIcon className="w-3 h-3 mr-0" />
        {t("createclient.components.menutags.tag2")}
      </Tag>
      <Tag
        className="cursor-pointer"
        variant={menu === "font" ? "quaternary" : "primary"}
        onClick={() => setMenu("font")}
      >
        <DocumentTextIcon className="w-3 h-3 mr-0" />
        {t("createclient.components.menutags.tag3")}
      </Tag>
      <Tag
        className="cursor-pointer"
        variant={menu === "photo" ? "quaternary" : "primary"}
        onClick={() => setMenu("photo")}
      >
        <PhotoIcon className="w-3 h-3 mr-0" />
        {t("createclient.components.menutags.tag4")}
      </Tag>
    </div>
  );
};

export default MenuTags;
