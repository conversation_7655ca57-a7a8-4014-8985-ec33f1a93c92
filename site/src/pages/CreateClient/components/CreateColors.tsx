import { useState } from "react";
import { <PERSON>, Heading, Button, ColorPicker, Input, Text } from "@/components";
import { PaintBrushIcon, PlusIcon } from "@heroicons/react/24/outline";

import { HTMLAttributes } from "react";
import { Modal, ModalBody, ModalHeader } from "@/components/Modal";
import { RoundedIcon } from "@/components/CustomIcons";
import { useTranslation } from "react-i18next";

interface CreateColorsProps extends HTMLAttributes<HTMLDivElement> {}

export function CreateColors(props: CreateColorsProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { t } = useTranslation();

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  return (
    <section className="flex gap-8" {...props}>
      <div className="grid gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 grid-cols-1">
        <div className="w-full drop-zone border-2 mt-1 flex flex-col justify-center items-center gap-2 p-2 py-4 rounded-[0.75rem] border-dashed border-gray-300 active:border-yellow-900 bg-[#FCFCFC] hover:bg-[#FFCC3333] hover:text-yellow-900 text-gray-300">
          <Button className="bg-gray-300 w-fit p-3" onClick={openModal}>
            <PlusIcon className="w-6 h-6" />
          </Button>
          <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
            {t("createclient.components.modalcreatecolors.hadear")}
          </Text>
        </div>
        {[...Array(20)].map((_, index) => (
          <Card key={index} color="quinternary">
            <Text className="font-outfit text-sm text-yellow-900 font-semibold leading-[1.079rem] mb-2">
              Campanha Halloween
            </Text>
            <div className="flex flex-row gap-2">
              <span className="bg-blue-500 w-10 h-10 rounded-lg" />
              <span className="bg-red-500 w-10 h-10 rounded-lg" />
              <span className="bg-green-500 w-10 h-10 rounded-lg" />
              <span className="bg-black-900 w-10 h-10 rounded-lg" />
              <span className="bg-yellow-900 w-10 h-10 rounded-lg" />
            </div>
            <div className="inline-grid grid-cols-2 mt-4 gap-2">
              <Button className="py-[0.375rem]" color="quintenary">
                {t("createclient.components.card.botton1")}
              </Button>
              <Button className="py-[0.375rem]" color="secondary">
                {t("createclient.components.card.botton2")}
              </Button>
            </div>
          </Card>
        ))}
      </div>
      {isModalOpen && (
        <Modal
          size="md"
          className="flex-col inline-flex gap-6"
          show={isModalOpen}
          onClose={closeModal}
        >
          <ModalHeader>
            <div className="flex items-center justify-center mx-auto">
              <RoundedIcon>
                <PaintBrushIcon width="40px" height="40px" />
              </RoundedIcon>
            </div>
            <Heading size="3" className="mt-6">
              {t("createclient.components.modalcreatecolors.hadear")}
            </Heading>
          </ModalHeader>
          <ModalBody>
            <form className="flex flex-col gap-4">
              <div className="mb-4">
                <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
                  {t("createclient.components.modalcreatecolors.body.text1")}
                </Text>
                <Input state="default" className="border-gray-300" />
              </div>
              <div className="flex gap-8">
                <div>
                  <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
                    {t("createclient.components.modalcreatecolors.body.text2")}
                  </Text>
                  <ColorPicker />
                </div>
                <div>
                  <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
                    {t("createclient.components.modalcreatecolors.body.text3")}
                  </Text>
                  <ColorPicker />
                </div>
              </div>
              <div className="flex gap-8">
                <div>
                  <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
                    {t("createclient.components.modalcreatecolors.body.text4")}
                  </Text>
                  <ColorPicker />
                </div>
                <div>
                  <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
                    {t("createclient.components.modalcreatecolors.body.text5")}
                  </Text>
                  <ColorPicker />
                </div>
              </div>
              <div>
                <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
                  {t("createclient.components.modalcreatecolors.body.text6")}
                </Text>
                <ColorPicker />
              </div>
            </form>
            <div className="flex mt-7 gap-4">
              <Button color="secondary" onClick={closeModal}>
                {t("createclient.components.modalcreatecolors.fotter.button1")}
              </Button>
              <Button>
                {t("createclient.components.modalcreatecolors.fotter.button2")}
              </Button>
            </div>
          </ModalBody>
        </Modal>
      )}
    </section>
  );
}
