import { useState } from "react";
import {
  Card,
  Heading,
  Text,
  Image,
  Button,
  Modal,
  Dropdown,
} from "@/components";
import { PhotoIcon, PlusIcon } from "@heroicons/react/24/outline";
import brunaoPersonal from "@/assets/brunaoPersonal.png";

import { HTMLAttributes } from "react";
import { RoundedIcon } from "@/components/CustomIcons";
import { ModalBody, ModalHeader } from "@/components/Modal";
import { DragAndDrop } from "@/components/DragAndDrop";
import { useTranslation } from "react-i18next";

interface CreatePhotoProps extends HTMLAttributes<HTMLDivElement> {}

export function CreatePhoto(props: CreatePhotoProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { t } = useTranslation();

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const handleDropdownChange = (
    selectedOption: { value: string; label: string } | null,
  ) => {
    console.log(selectedOption);
  };

  return (
    <section className="flex gap-8" {...props}>
      <div className="grid gap-4 lg:grid-cols-2 grid-cols-1">
        <div className="w-full drop-zone border-2 mt-1 flex flex-col justify-center items-center gap-2 p-2 py-4 rounded-[0.75rem] border-dashed border-gray-300 active:border-yellow-900 bg-[#FCFCFC] hover:bg-[#FFCC3333] hover:text-yellow-900 text-gray-300">
          <Button className="bg-gray-300 w-fit p-3" onClick={openModal}>
            <PlusIcon className="w-5 h-5 justify-normal" />
          </Button>
          <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
            {t("createclient.components.modalcreatephoto.hadear")}
          </Text>
        </div>
        {[...Array(6)].map((_, index) => (
          <Card key={index} color="quinternary">
            <Text className="font-outfit text-xs mb-2 mr-1 text-yellow-900 font-semibold leading-[1.079rem]">
              Geral
            </Text>
            <div className="grid grid-cols-3 gap-2">
              <Image
                className="h-20 mb-2 w-full"
                src="https://via.placeholder.com/300"
                alt="Exemplo 1"
              />
              <Image
                className="mb-2 w-full"
                src="https://via.placeholder.com/300"
                alt="Exemplo 1"
              />
              <Image
                className="mb-2 w-full h-14"
                src="https://via.placeholder.com/300"
                alt="Exemplo 2"
              />
              <Image className="mb-2 w-full h-14" src={brunaoPersonal} />
              <Image
                className="rounded-[0.75rem] mb-2 w-full"
                src={brunaoPersonal}
              />
              <Image
                className="rounded-[0.75rem] mb-2 w-full"
                src={brunaoPersonal}
              />
            </div>
            <div className="inline-grid grid-cols-2 mt-4 gap-2">
              <Button className="py-[0.375rem]" color="quintenary">
                {t("createclient.components.card.botton1")}
              </Button>
              <Button className="py-[0.375rem]" color="secondary">
                {t("createclient.components.card.botton2")}
              </Button>
            </div>
          </Card>
        ))}
      </div>
      {isModalOpen && (
        <Modal
          size="md"
          className="flex-col inline-flex gap-6"
          show={isModalOpen}
          onClose={closeModal}
        >
          <ModalHeader>
            <div className="flex items-center justify-center mx-auto">
              <RoundedIcon className="flex items-center justify-center mx-auto">
                <PhotoIcon width="40px" height="40px" />
              </RoundedIcon>
            </div>
            <Heading size="3" className="mt-6">
              {t("createclient.components.modalcreatephoto.hadear")}
            </Heading>
          </ModalHeader>
          <ModalBody>
            <form>
              <div className="mb-4 w-full">
                <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
                  {t("createclient.components.modalcreatephoto.body.text1")}
                </Text>
                <Dropdown
                  onChange={handleDropdownChange}
                  loadOptions={() => {}}
                />
              </div>
              <div className="mb-4">
                <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
                  {t("createclient.components.modalcreatephoto.body.text2")}
                </Text>
                <DragAndDrop onFilesDrop={() => {}} />
              </div>
            </form>
            <div className="flex mt-7 gap-4">
              <Button color="secondary" onClick={closeModal}>
                {t("createclient.components.modalcreatephoto.fotter.button1")}
              </Button>
              <Button>
                {t("createclient.components.modalcreatephoto.fotter.button2")}
              </Button>
            </div>
          </ModalBody>
        </Modal>
      )}
    </section>
  );
}
