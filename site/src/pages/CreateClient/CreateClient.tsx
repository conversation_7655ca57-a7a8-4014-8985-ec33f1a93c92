import { HTMLAttributes, useState } from "react";
import { MainLayout } from "@/layout/MainLayout";
import { Heading } from "@/components";
import { BuildingStorefrontIcon } from "@heroicons/react/24/outline";
import MenuTags from "./components/MenuTags";
import { CreateColors } from "./components/CreateColors";
import { CreateFonts } from "./components/CreateFonts";
import { CreatePhoto } from "./components/CreatePhoto";
import { CreateLogo } from "./components/CreateLogo";

interface CreateClientProps extends HTMLAttributes<HTMLDivElement> {}

export function CreateClient(props: CreateClientProps) {
  const [menu, setMenu] = useState("logo");

  return (
    <MainLayout data-testid="ClientCreate">
      <div className="container m-auto" {...props}>
        <section className="flex flex-col gap-8 mt-8 mr-32">
          <div className="flex justify-between">
            <Heading size="3" className="flex items-center">
              <BuildingStorefrontIcon className="w-6 h-6 mr-1" />
              Crossfit Master
            </Heading>
          </div>
          <div className="flex flex-row gap-3">
            <MenuTags setMenu={setMenu} menu={menu} />
          </div>
          {menu === "logo" && <CreateLogo />}
          {menu === "color" && <CreateColors />}
          {menu === "font" && <CreateFonts />}
          {menu === "photo" && <CreatePhoto />}
        </section>
      </div>
    </MainLayout>
  );
}

export default CreateClient;
