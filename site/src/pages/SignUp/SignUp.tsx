import logo from "@/assets/logo.svg";
import { But<PERSON>, Heading, Image, Text } from "@/components";
import { useTranslation } from "react-i18next";
import { EnvelopeIcon } from "@heroicons/react/24/outline";

export type SignUpProps = {
  onGoogleSignUp: () => void;
  onEmailSignUp: () => void;
};

function SignUp({ onGoogleSignUp, onEmailSignUp }: SignUpProps) {
  const { t } = useTranslation();

  return (
    <div
      data-testid="sign-up"
      className="flex h-screen place-items-center justify-center before:content-[''] before:opacity-35 before:blur-3xl before:fixed before:top-[0] before:left-[-20%] before:w-[2000px] before:h-[2000px] before:rounded-full before:block before:z-[-2] before:bg-amber-100 before:border-solid before:border-[200px] before:border-amber-50 before:opacity-80
      after:blur-3xl after:content-[''] after:fixed after:top-[180px] after:right-[-35%] after:w-[2000px] after:h-[2000px] after:rounded-full after:block after:z-[-1] after:bg-slate-300 after:border-solid after:border-[200px] after:border-slate-5 after:opacity-80"
    >
      <div className="flex flex-col items-center text-center w-[30rem] p-20 rounded-2xl bg-white">
        <Image src={logo} alt="Conteoodo" className="w-[10rem]" />
        <Heading size="3" className="mt-[1.5rem]">
          {t("sign-up.title")}
        </Heading>
        <Text className="mt-[1.5rem] leading-4">
          {t("sign-up.description")}
        </Text>
        <Heading size="5" className="mt-[1.5rem]">
          {t("sign-up.label")}
        </Heading>
        <Button
          data-testid="google-btn"
          size="normal"
          className="mt-[1.5rem] flex justify-center items-center text-xs gap-2"
          onClick={onGoogleSignUp}
        >
          {t("sign-up.google")}
          <p className="text-[1.75rem] font-black leading-[1.25rem]">G</p>
        </Button>
        <Button
          data-testid="email-btn"
          size="normal"
          className="mt-[1.5rem] flex justify-center items-center text-xs gap-2"
          onClick={onEmailSignUp}
        >
          {t("sign-up.email")}
          <EnvelopeIcon className="lg:w-6 w-4 lg:my-[-0.125rem] stroke-[0.15rem]" />
        </Button>
      </div>
    </div>
  );
}

export default SignUp;
