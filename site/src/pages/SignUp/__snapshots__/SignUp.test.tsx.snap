// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<SignUp /> should render the page 1`] = `
<div
  class="flex h-screen place-items-center justify-center before:content-[''] before:opacity-35 before:blur-3xl before:fixed before:top-[0] before:left-[-20%] before:w-[2000px] before:h-[2000px] before:rounded-full before:block before:z-[-2] before:bg-amber-100 before:border-solid before:border-[200px] before:border-amber-50 before:opacity-80 after:blur-3xl after:content-[''] after:fixed after:top-[180px] after:right-[-35%] after:w-[2000px] after:h-[2000px] after:rounded-full after:block after:z-[-1] after:bg-slate-300 after:border-solid after:border-[200px] after:border-slate-5 after:opacity-80"
  data-testid="sign-up"
>
  <div
    class="flex flex-col items-center text-center w-[30rem] p-20 rounded-2xl bg-white"
  >
    <img
      alt="Conteoodo"
      class="w-[10rem]"
      src=""
    />
    <h1
      class="font-outfit font-[700] text-[1.5rem] mt-[1.5rem]"
    >
      sign-up.title
    </h1>
    <p
      class="font-rubik font-[400] text-[0.75rem] mt-[1.5rem] leading-4"
    >
      sign-up.description
    </p>
    <h1
      class="font-outfit font-[700] text-[0.75rem] mt-[1.5rem]"
    >
      sign-up.label
    </h1>
    <button
      class="w-full py-3 px-4 rounded-md font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white mt-[1.5rem] flex justify-center items-center text-xs gap-2"
      data-testid="google-btn"
    >
      sign-up.google
      <p
        class="text-[1.75rem] font-black leading-[1.25rem]"
      >
        G
      </p>
    </button>
    <button
      class="w-full py-3 px-4 rounded-md font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white mt-[1.5rem] flex justify-center items-center text-xs gap-2"
      data-testid="email-btn"
    >
      sign-up.email
      <svg
        aria-hidden="true"
        class="lg:w-6 w-4 lg:my-[-0.125rem] stroke-[0.15rem]"
        data-slot="icon"
        fill="none"
        stroke="currentColor"
        stroke-width="1.5"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </button>
  </div>
</div>
`;
