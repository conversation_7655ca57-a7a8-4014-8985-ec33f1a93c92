import { render, screen, fireEvent } from "@tests/render";
import SignUp from "./SignUp";

describe("<SignUp />", () => {
  it("should render the page", () => {
    render(<SignUp onEmailSignUp={jest.fn()} onGoogleSignUp={jest.fn()} />);
    expect(screen.getByTestId("sign-up")).toMatchSnapshot();
  });

  it("should click on google button", () => {
    const onGoogleSignUp = jest.fn();

    render(
      <SignUp onEmailSignUp={jest.fn()} onGoogleSignUp={onGoogleSignUp} />,
    );
    const button = screen.getByTestId("google-btn");

    fireEvent.click(button);

    expect(onGoogleSignUp).toHaveBeenCalledTimes(1);
  });

  it("should click on email button", () => {
    const onEmailSignUp = jest.fn();

    render(<SignUp onEmailSignUp={onEmailSignUp} onGoogleSignUp={jest.fn()} />);
    const button = screen.getByTestId("email-btn");

    fireEvent.click(button);

    expect(onEmailSignUp).toHaveBeenCalledTimes(1);
  });
});
