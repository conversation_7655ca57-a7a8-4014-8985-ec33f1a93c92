import { useForm } from "react-hook-form";
import { But<PERSON>, Heading, Input, PasswordStrength, Text } from "@/components";
import { ArrowRightIcon } from "@heroicons/react/24/outline";
import { Trans, useTranslation } from "react-i18next";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { ExclamationTriangleIcon } from "@heroicons/react/24/solid";
import { CalculateStrength } from "@/components/PasswordStrength/CalculateStrength";
import { SignUpWithEmail2Param } from "./SignUpWithEmail2Container";
import { useEffect } from "react";

type SignUpWithEmail2Props = {
  onSignUp: (params: SignUpWithEmail2Param) => void;
  isLoading: boolean;
  error?: Error;
};

function SignUpWithEmail2({
  onSignUp,
  isLoading,
  error,
}: SignUpWithEmail2Props) {
  const { t } = useTranslation();
  const schema = yup.object().shape({
    password: yup
      .string()
      .required(t("sign-up-with-email.form.errors.required"))
      .min(6, t("sign-up-with-email.form.errors.password-short"))
      .test(
        "password-strength",
        t("sign-up-with-email.form.errors.invalid-password"),
        (value) => {
          return CalculateStrength(value) > 2;
        },
      ),
    confirmPassword: yup
      .string()
      .required(t("sign-up-with-email.form.errors.required"))
      .oneOf(
        [yup.ref("password")],
        t("sign-up-with-email.form.errors.password-not-match"),
      ),
  });
  const {
    handleSubmit,
    watch,
    register,
    formState: { errors },
    setError,
  } = useForm<SignUpWithEmail2Param>({
    resolver: yupResolver(schema),
    delayError: 1000,
    mode: "onChange",
    shouldFocusError: true,
  });
  const watchPassword = watch("password", "");

  useEffect(() => {
    if (error) {
      setError("password", {
        type: "server",
        message: error.message,
      });
    }
  }, [error]);

  return (
    <div
      data-testid="sign-up-with-email2"
      className="flex h-screen place-items-center justify-center before:content-[''] before:opacity-35 before:blur-3xl before:fixed before:top-[0] before:left-[-20%] before:w-[2000px] before:h-[2000px] before:rounded-full before:block before:z-[-2] before:bg-amber-100 before:border-solid before:border-[200px] before:border-amber-50 before:opacity-80
      after:blur-3xl after:content-[''] after:fixed after:top-[180px] after:right-[-35%] after:w-[2000px] after:h-[2000px] after:rounded-full after:block after:z-[-1] after:bg-slate-300 after:border-solid after:border-[200px] after:border-slate-5 after:opacity-80"
    >
      <div className="flex flex-col items-center text-center w-[30rem] p-20 rounded-2xl bg-white">
        <form onSubmit={handleSubmit(onSignUp)}>
          <Heading size="3">{t("sign-up-with-email.password-title")}</Heading>
          <Text className="font-outfit font-semibold text-[0.75rem] leading-4 mt-[1.5rem]">
            {t("sign-up-with-email.password-hint")}
          </Text>

          <label
            className="block text-black-900 text-[0.625rem] text-left font-semibold mb-2 mt-[1.5rem]"
            htmlFor="password"
          >
            {t("sign-up-with-email.form.labels.password")}
          </label>
          <Input
            state={errors.password ? "error" : "default"}
            autoComplete="off"
            type="password"
            autoFocus
            data-testid="password-input"
            {...register("password")}
          />
          {errors.password && (
            <div className="flex flex-row gap-1 ml-1 mt-1 items-center">
              <ExclamationTriangleIcon className="h-4 text-red-700" />
              <Text className="text-red-700 text-left">
                {errors.password.message}
              </Text>
            </div>
          )}

          <label
            className="block text-black-900 text-[0.625rem] text-left font-semibold mb-2 mt-6"
            htmlFor="confirmPassword"
          >
            {t("sign-up-with-email.form.labels.confirm-password")}
          </label>
          <Input
            state={errors.confirmPassword ? "error" : "default"}
            autoComplete="off"
            type="password"
            data-testid="repeat-password-input"
            {...register("confirmPassword")}
          />
          {errors.confirmPassword && (
            <div className="flex flex-row gap-1 ml-1 mt-1 items-center">
              <ExclamationTriangleIcon className="h-4 text-red-700" />
              <Text className="text-red-700 text-left">
                {errors.confirmPassword.message}
              </Text>
            </div>
          )}
          {watchPassword.length != 0 && (
            <PasswordStrength value={watchPassword} className="mt-6" />
          )}
          {CalculateStrength(watchPassword) <= 2 &&
            CalculateStrength(watchPassword) > 0 && (
              <Text className="text-left mt-2 text-[0.625rem] leading-[0.875rem]">
                <Trans i18nKey={"sign-up-with-email.form.weak-password-text"} />
              </Text>
            )}
          <Button
            className="mt-[1.5rem] flex justify-center items-center text-xs gap-2"
            type="submit"
            disabled={isLoading}
            data-testid="sign-up-btn"
          >
            {t("sign-up-with-email.continue")}
            <ArrowRightIcon className="w-[1.125rem] stroke-[0.15rem]" />
          </Button>
        </form>
      </div>
    </div>
  );
}

export default SignUpWithEmail2;
