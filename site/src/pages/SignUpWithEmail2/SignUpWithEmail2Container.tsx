import { useMutation } from "@tanstack/react-query";
import { callApi } from "@/utils";
import { SIGN_UP } from "@/queries/signUp";
import { useLocation, useNavigate } from "react-router-dom";
import SignUpWithEmail2 from "./SignUpWithEmail2";

export type SignUpWithEmail2Param = {
  password: string;
  confirmPassword: string;
};

function SignUpWithEmail2Container() {
  const navigate = useNavigate();
  const location = useLocation();

  const { isPending, mutate, error } = useMutation<
    unknown,
    Error,
    SignUpWithEmail2Param
  >({
    mutationFn: (data: SignUpWithEmail2Param) =>
      callApi(SIGN_UP, {
        fullName: location.state.fullName,
        email: location.state.email,
        password: data.password,
      }),
    onSuccess: async () => {
      navigate("/confirm-account", {
        state: {
          email: location.state.email,
        },
      });
    },
  });

  return (
    <SignUpWithEmail2 onSignUp={mutate} isLoading={isPending} error={error!} />
  );
}

export default SignUpWithEmail2Container;
