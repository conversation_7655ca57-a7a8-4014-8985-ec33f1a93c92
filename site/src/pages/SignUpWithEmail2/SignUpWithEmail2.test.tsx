import { fireEvent, render, screen, waitFor } from "@tests/render";
import SignUpWith<PERSON>mail2 from "./SignUpWithEmail2";

describe("<SignUpWithEmail2>", () => {
  it("should render the page", () => {
    render(<SignUpWithEmail2 onSignUp={jest.fn()} isLoading={false} />);
    expect(screen.getByTestId("sign-up-with-email2")).toMatchSnapshot();
  });

  it("should validate the form fields", async () => {
    const onSignUp = jest.fn();
    render(<SignUpWithEmail2 onSignUp={onSignUp} isLoading={false} />);

    const submitBtn = screen.getByTestId("sign-up-btn");

    fireEvent.submit(submitBtn);

    await waitFor(() => {
      expect(
        screen.getAllByText("sign-up-with-email.form.errors.required"),
      ).toBeTruthy();
      expect(onSignUp).toHaveBeenCalledTimes(0);
    });
  });

  it("should validate minimum character for password form field", async () => {
    const onSignUp = jest.fn();
    render(<SignUpWithEmail2 onSignUp={onSignUp} isLoading={false} />);

    const passwordInput = screen.getByTestId("password-input");
    const submitBtn = screen.getByTestId("sign-up-btn");

    fireEvent.change(passwordInput, {
      target: { value: "132" },
    });
    fireEvent.submit(submitBtn);
    await waitFor(() => {
      expect(
        screen.getByText("sign-up-with-email.form.errors.password-short"),
      ).toBeTruthy();
      expect(onSignUp).toHaveBeenCalledTimes(0);
    });
  });

  it("should validate different password for repeat password form field", async () => {
    const onSignUp = jest.fn();
    render(<SignUpWithEmail2 onSignUp={onSignUp} isLoading={false} />);

    const passwordInput = screen.getByTestId("password-input");
    const repeatPasswordInput = screen.getByTestId("repeat-password-input");
    const submitBtn = screen.getByTestId("sign-up-btn");

    fireEvent.change(passwordInput, {
      target: { value: "qew132" },
    });
    fireEvent.change(repeatPasswordInput, {
      target: { value: "123asd" },
    });
    fireEvent.submit(submitBtn);
    await waitFor(() => {
      expect(
        screen.getByText("sign-up-with-email.form.errors.password-not-match"),
      ).toBeTruthy();
      expect(onSignUp).toHaveBeenCalledTimes(0);
    });
  });

  it("should show the error", async () => {
    const onSignUp = jest.fn();
    const error = new Error("Invalid");
    render(
      <SignUpWithEmail2 onSignUp={onSignUp} isLoading={false} error={error} />,
    );

    expect(screen.getByText("Invalid")).toBeTruthy();
  });

  it("should call onSignUp with correct data when form is submitted with valid data", async () => {
    const onSignUp = jest.fn();
    render(<SignUpWithEmail2 onSignUp={onSignUp} isLoading={false} />);

    const passwordInput = screen.getByTestId("password-input");
    const repeatPasswordInput = screen.getByTestId("repeat-password-input");
    const submitBtn = screen.getByTestId("sign-up-btn");

    fireEvent.change(passwordInput, {
      target: { value: "qwe123" },
    });
    fireEvent.change(repeatPasswordInput, {
      target: { value: "qwe123" },
    });
    fireEvent.submit(submitBtn);
    await waitFor(() => {
      expect(onSignUp).toHaveBeenCalledWith(
        {
          confirmPassword: "qwe123",
          password: "qwe123",
        },
        expect.objectContaining({}),
      );
    });
  });
});
