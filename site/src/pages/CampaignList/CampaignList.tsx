import { MainLayout } from "@/layout/MainLayout";
import {
  <PERSON>ton,
  Card,
  Dropdown,
  Heading,
  Image,
  Input,
  Text,
} from "@/components";
import {
  BuildingStorefrontIcon,
  CalendarDaysIcon,
  CheckCircleIcon,
  MegaphoneIcon,
  PlusIcon,
  ShareIcon,
} from "@heroicons/react/24/outline";
import brunaoPersonal from "@/assets/brunaoPersonal.png";
import {
  RoundedFacebookIcon,
  RoundedInstagramIcon,
  RoundedLinkedinIcon,
} from "@/components/CustomIcons";
import { useTranslation } from "react-i18next";

export function CampaignList() {
  const { t } = useTranslation();

  const statusOptions = [
    {
      value: "finished",
      label: t("campaign-list.cards.status.finished"),
    },
  ];

  const statusFilter = (inputValue: string) => {
    return statusOptions.filter((i) =>
      i.label.toLowerCase().includes(inputValue.toLowerCase()),
    );
  };

  const statusLoadOptions = (
    inputValue: string,
    callback: (options: { value: string; label: string }[]) => void,
  ) => {
    setTimeout(() => {
      callback(statusFilter(inputValue));
    }, 1000);
  };

  const socialOptions = [
    {
      value: "facebook",
      label: "Facebook",
    },
    {
      value: "instagram",
      label: "Instagram",
    },
    {
      value: "linkedIn",
      label: "LinkedIn",
    },
  ];

  const socialFilter = (inputValue: string) => {
    return socialOptions.filter((i) =>
      i.label.toLowerCase().includes(inputValue.toLowerCase()),
    );
  };

  const socialLoadOptions = (
    inputValue: string,
    callback: (options: { value: string; label: string }[]) => void,
  ) => {
    setTimeout(() => {
      callback(socialFilter(inputValue));
    }, 1000);
  };
  return (
    <MainLayout data-testid="Campaign-List">
      <div className="container m-auto">
        <div className="flex justify-between mt-14">
          <Heading size="3" className="flex gap-1 items-center">
            <MegaphoneIcon width="1.5rem" className="stroke-[0.1rem]" />{" "}
            {t("campaign-list.title")}
          </Heading>
          <Button className="flex items-center w-fit">
            {t("campaign-list.new-campaign")}
            <PlusIcon className="w-8 h-5" />
          </Button>
        </div>
        <div className="flex max-[1023px]:flex-col items-end gap-4 bg-white border border-gray-100 rounded-lg mt-8 mb-12 p-4">
          <div className="flex max-[639px]:flex-col gap-4 lg:grow max-[1023px]:w-full">
            <div className="lg:w-[55%] w-full">
              <Text className="font-outfit text-xs font-semibold mb-1">
                {t("campaign-list.filter.placeholders.campaign")}
              </Text>
              <Input
                placeholder={t("campaign-list.filter.placeholders.campaign")}
              />
            </div>
            <div className="lg:w-[45%] w-full">
              <Text className="font-outfit text-xs font-semibold mb-1">
                {t("campaign-list.filter.placeholders.client")}
              </Text>
              <Input
                placeholder={t("campaign-list.filter.placeholders.client")}
              />
            </div>
          </div>
          <div className="flex max-[639px]:flex-col gap-4 lg:w-2/5 w-full">
            <div className="w-full">
              <Text className="font-outfit text-xs font-semibold mb-1">
                {t("campaign-list.filter.placeholders.status")}
              </Text>
              <Dropdown
                loadOptions={statusLoadOptions}
                onChange={(value) => {
                  console.log(value);
                }}
              />
            </div>
            <div className="w-full">
              <Text className="font-outfit text-xs font-semibold mb-1">
                {t("campaign-list.filter.placeholders.social-network")}
              </Text>
              <Dropdown
                loadOptions={socialLoadOptions}
                onChange={(value) => {
                  console.log(value);
                }}
              />
            </div>
          </div>
          <Button color="secondary" className="lg:w-fit w-full h-fit">
            {t("campaign-list.filter.button")}
          </Button>
        </div>
        <div className="grid sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-4 2xl:grid-cols-5 gap-4 pb-16">
          <Card color="quinternary">
            <Image
              className="rounded-[0.75rem] mb-2 w-full"
              src={brunaoPersonal}
            />

            <div className="flex flex-col gap-1">
              <div className="flex flex-row items-center gap-1">
                <MegaphoneIcon className="w-4 h-4 text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.3125rem]">
                  Quer ficar grandao?
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <BuildingStorefrontIcon className="w-4 h-4 text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                  Eric Personal
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <CheckCircleIcon className="w-4 h-4  text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                  {t("campaign-list.cards.status.finished")}
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <CalendarDaysIcon className="w-4 h-4 text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                  10/12/2023 {t("campaign-list.cards.preposition")} 10/02/2024
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <div className="flex gap-1">
                  <ShareIcon className="w-4 h-4 mt-1 mr-1 text-gray-800" />
                  <RoundedFacebookIcon className="w-5 h-5" />
                  <RoundedInstagramIcon className="w-5 h-5" />
                  <RoundedLinkedinIcon className="w-5 h-5" />
                </div>
              </div>
            </div>
          </Card>
          <Card color="quinternary">
            <Image
              className="rounded-[0.75rem] mb-2 w-full"
              src={brunaoPersonal}
            />

            <div className="flex flex-col gap-1">
              <div className="flex flex-row items-center gap-1">
                <MegaphoneIcon className="w-4 h-4 text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.3125rem]">
                  Quer ficar grandao?
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <BuildingStorefrontIcon className="w-4 h-4 text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                  Eric Personal
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <CheckCircleIcon className="w-4 h-4  text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                  Finalizada
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <CalendarDaysIcon className="w-4 h-4 text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                  10/12/2023 {t("campaign-list.cards.preposition")} 10/02/2024
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <div className="flex gap-1">
                  <ShareIcon className="w-4 h-4 mt-1 mr-1 text-gray-800" />
                  <RoundedFacebookIcon className="w-5 h-5" />
                  <RoundedInstagramIcon className="w-5 h-5" />
                  <RoundedLinkedinIcon className="w-5 h-5" />
                </div>
              </div>
            </div>
          </Card>
          <Card color="quinternary">
            <Image
              className="rounded-[0.75rem] mb-2 w-full"
              src={brunaoPersonal}
            />

            <div className="flex flex-col gap-1">
              <div className="flex flex-row items-center gap-1">
                <MegaphoneIcon className="w-4 h-4 text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.3125rem]">
                  Quer ficar grandao?
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <BuildingStorefrontIcon className="w-4 h-4 text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                  Eric Personal
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <CheckCircleIcon className="w-4 h-4  text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                  Finalizada
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <CalendarDaysIcon className="w-4 h-4 text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                  10/12/2023 {t("campaign-list.cards.preposition")} 10/02/2024
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <div className="flex gap-1">
                  <ShareIcon className="w-4 h-4 mt-1 mr-1 text-gray-800" />
                  <RoundedFacebookIcon className="w-5 h-5" />
                  <RoundedInstagramIcon className="w-5 h-5" />
                  <RoundedLinkedinIcon className="w-5 h-5" />
                </div>
              </div>
            </div>
          </Card>
          <Card color="quinternary">
            <Image
              className="rounded-[0.75rem] mb-2 w-full"
              src={brunaoPersonal}
            />

            <div className="flex flex-col gap-1">
              <div className="flex flex-row items-center gap-1">
                <MegaphoneIcon className="w-4 h-4 text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.3125rem]">
                  Quer ficar grandao?
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <BuildingStorefrontIcon className="w-4 h-4 text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                  Eric Personal
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <CheckCircleIcon className="w-4 h-4  text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                  Finalizada
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <CalendarDaysIcon className="w-4 h-4 text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                  10/12/2023 {t("campaign-list.cards.preposition")} 10/02/2024
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <div className="flex gap-1">
                  <ShareIcon className="w-4 h-4 mt-1 mr-1 text-gray-800" />
                  <RoundedFacebookIcon className="w-5 h-5" />
                  <RoundedInstagramIcon className="w-5 h-5" />
                  <RoundedLinkedinIcon className="w-5 h-5" />
                </div>
              </div>
            </div>
          </Card>
          <Card color="quinternary">
            <Image
              className="rounded-[0.75rem] mb-2 w-full"
              src={brunaoPersonal}
            />

            <div className="flex flex-col gap-1">
              <div className="flex flex-row items-center gap-1">
                <MegaphoneIcon className="w-4 h-4 text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.3125rem]">
                  Quer ficar grandao?
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <BuildingStorefrontIcon className="w-4 h-4 text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                  Eric Personal
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <CheckCircleIcon className="w-4 h-4  text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                  Finalizada
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <CalendarDaysIcon className="w-4 h-4 text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                  10/12/2023 {t("campaign-list.cards.preposition")} 10/02/2024
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <div className="flex gap-1">
                  <ShareIcon className="w-4 h-4 mt-1 mr-1 text-gray-800" />
                  <RoundedFacebookIcon className="w-5 h-5" />
                  <RoundedInstagramIcon className="w-5 h-5" />
                  <RoundedLinkedinIcon className="w-5 h-5" />
                </div>
              </div>
            </div>
          </Card>
          <Card color="quinternary">
            <Image
              className="rounded-[0.75rem] mb-2 w-full"
              src={brunaoPersonal}
            />

            <div className="flex flex-col gap-1">
              <div className="flex flex-row items-center gap-1">
                <MegaphoneIcon className="w-4 h-4 text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.3125rem]">
                  Quer ficar grandao?
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <BuildingStorefrontIcon className="w-4 h-4 text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                  Eric Personal
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <CheckCircleIcon className="w-4 h-4  text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                  Finalizada
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <CalendarDaysIcon className="w-4 h-4 text-gray-800" />
                <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                  10/12/2023 {t("campaign-list.cards.preposition")} 10/02/2024
                </Text>
              </div>
              <div className="flex flex-row items-center gap-1">
                <div className="flex gap-1">
                  <ShareIcon className="w-4 h-4 mt-1 mr-1 text-gray-800" />
                  <RoundedFacebookIcon className="w-5 h-5" />
                  <RoundedInstagramIcon className="w-5 h-5" />
                  <RoundedLinkedinIcon className="w-5 h-5" />
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
}

export default CampaignList;
