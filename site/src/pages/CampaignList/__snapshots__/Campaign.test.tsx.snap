// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<CampaignList /> should render the page 1`] = `
<div
  class="h-full h-screen w-full overflow-hidden bg-gray-100"
  data-testid="Campaign-List"
>
  <div
    class="xl:relative flex h-screen w-full overflow-hidden"
  >
    <aside
      class="hidden lg:flex z-20 h-full flex-shrink-0 flex-grow-0 flex-col transition-all"
    >
      <div
        class="relative"
        data-testid="sidebar"
      >
        <nav
          class="flex flex-col flex-auto items-start gap-4 p-6 h-full w-64 flex-shrink-0 min-h-screen antialiased bg-white border-r border-gray-100"
          data-testid="opened-sidebar"
        >
          <div
            class="flex flex-col gap-4 justify-center items-center"
          >
            <a
              class="h-7 mb-2"
              href="/"
            >
              <img
                alt="Logo"
                class="block h-full w-auto"
                src=""
              />
            </a>
            <div
              class="block lg:hidden flex w-full gap-2 items-center"
            >
              <img
                class="rounded-full w-8 h-8"
                loading="lazy"
                src="https://tecdn.b-cdn.net/img/new/avatars/2.jpg"
                style="max-width: none;"
              />
              <p
                class="font-outfit  text-xs capitalize text-black-900"
              >
                Bruno Sousa
              </p>
            </div>
          </div>
          <span
            class="w-full border-b border-gray-100"
          />
          <button
            class="w-full py-3 px-4 rounded-md font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-xs flex justify-center items-center"
          >
            components.Sidebar.text
            <svg
              aria-hidden="true"
              class="w-8 h-5"
              data-slot="icon"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 4.5v15m7.5-7.5h-15"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
          <span
            class="w-full border-b border-gray-100"
          />
          <div
            class="w-full"
          >
            <nav
              class="flex flex-col gap-2 "
            >
              <a
                class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                href="/"
              >
                <svg
                  aria-hidden="true"
                  class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class=" text-xs tracking-wide truncate text-black-900"
                >
                  components.Sidebar.labels.home
                </span>
              </a>
              <a
                class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                href="/"
              >
                <svg
                  aria-hidden="true"
                  class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535M10.34 6.66a23.847 23.847 0 0 0 8.835-2.535m0 0A23.74 23.74 0 0 0 18.795 3m.38 1.125a23.91 23.91 0 0 1 1.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 0 0 1.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 0 1 0 3.46"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class=" text-xs tracking-wide truncate text-black-900"
                >
                  components.Sidebar.labels.megaphone
                </span>
              </a>
              <a
                class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                href="/"
              >
                <svg
                  aria-hidden="true"
                  class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class=" text-xs tracking-wide truncate text-black-900"
                >
                  components.Sidebar.labels.calendarDays
                </span>
              </a>
              <a
                class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                href="/"
              >
                <svg
                  aria-hidden="true"
                  class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M13.5 21v-7.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349M3.75 21V9.349m0 0a3.001 3.001 0 0 0 3.75-.615A2.993 2.993 0 0 0 9.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 0 0 2.25 1.016c.896 0 1.7-.393 2.25-1.015a3.001 3.001 0 0 0 3.75.614m-16.5 0a3.004 3.004 0 0 1-.621-4.72l1.189-1.19A1.5 1.5 0 0 1 5.378 3h13.243a1.5 1.5 0 0 1 1.06.44l1.19 1.189a3 3 0 0 1-.621 4.72M6.75 18h3.75a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75H6.75a.75.75 0 0 0-.75.75v3.75c0 .414.336.75.75.75Z"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class=" text-xs tracking-wide truncate text-black-900"
                >
                  components.Sidebar.labels.buildingStorefront
                </span>
              </a>
              <a
                class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                href="/"
              >
                <svg
                  aria-hidden="true"
                  class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class=" text-xs tracking-wide truncate text-black-900"
                >
                  components.Sidebar.labels.users
                </span>
              </a>
              <a
                class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                href="/"
              >
                <svg
                  aria-hidden="true"
                  class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M3.75 3v11.25A2.25 2.25 0 0 0 6 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0 1 18 16.5h-2.25m-7.5 0h7.5m-7.5 0-1 3m8.5-3 1 3m0 0 .5 1.5m-.5-1.5h-9.5m0 0-.5 1.5M9 11.25v1.5M12 9v3.75m3-6v6"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class=" text-xs tracking-wide truncate text-black-900"
                >
                  components.Sidebar.labels.presentationChartBar
                </span>
              </a>
              <span
                class="flex items-center justify-center w-[14.5rem] h-[0.0625rem] border-b border-b-neutral-50"
              />
              <a
                class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300"
                href="/"
              >
                <svg
                  aria-hidden="true"
                  class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5 "
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class=" text-xs tracking-wide truncate text-black-900"
                >
                  components.Sidebar.labels.user
                </span>
              </a>
              <a
                class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300"
                href="/"
              >
                <svg
                  aria-hidden="true"
                  class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5 "
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class=" text-xs tracking-wide truncate text-black-900"
                >
                  components.Sidebar.labels.lockClose
                </span>
              </a>
              <a
                class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300"
                href="/"
              >
                <svg
                  aria-hidden="true"
                  class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5 "
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class=" text-xs tracking-wide truncate text-black-900"
                >
                  components.Sidebar.labels.creditCar
                </span>
              </a>
              <a
                class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300"
                href="/"
              >
                <svg
                  aria-hidden="true"
                  class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5 "
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class=" text-xs tracking-wide truncate text-black-900"
                >
                  components.Sidebar.labels.cog6Tooth
                </span>
              </a>
            </nav>
          </div>
        </nav>
        <button
          class="absolute bottom-4 right-4 flex justify-end w-full"
          data-testid="left-arrow"
        >
          <svg
            aria-hidden="true"
            class="w-4 h-4 transition ease-in-out delay-150 duration-300 hover:-translate-x-1 hover:text-black-900 text-yellow-900 cursor-pointer"
            data-slot="icon"
            fill="none"
            stroke="currentColor"
            stroke-width="1.5"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
    </aside>
    <main
      class="relative flex xl:h-full w-full flex-col xl:overflow-hidden"
    >
      <header
        class="z-[15]"
      >
        <nav
          class="flex justify-between items-center bg-white w-full h-14 py-2 px-4 border-b border-gray-100 z-10 grid-area-header"
          data-testid="loggedlnNavbar"
        >
          <div
            class="fixed w-full h-full top-0 left-0 z-20 bg-black-900 opacity-0 block pointer-events-none animate-delayedDisplayNone transition-all duration-300"
          />
          <div
            class="flex justify-between items-center gap-8"
          >
            <a
              class="h-12"
              href="/"
            >
              <img
                class="block lg:hidden"
                src=""
              />
            </a>
            <div
              class="hidden lg:block"
            >
              breadcrumbs here
            </div>
          </div>
          <div
            class="hidden lg:block"
          >
            search here
          </div>
          <div
            class="flex justify-between items-center gap-8"
          >
            <a
              class="w-auto h-10 py-3 px-4 rounded-md font-outfit text-xs text-black-900 tracking-[0.0625rem] bg-blue-100 hover:bg-yellow-900 hover:text-white duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed hidden sm:flex items-center truncate"
              href="/"
            >
              <svg
                aria-hidden="true"
                class="mr-2 w-6 h-6"
                data-slot="icon"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M16.5 6v.75m0 3v.75m0 3v.75m0 3V18m-9-5.25h5.25M7.5 15h3M3.375 5.25c-.621 0-1.125.504-1.125 1.125v3.026a2.999 2.999 0 0 1 0 5.198v3.026c0 .621.504 1.125 1.125 1.125h17.25c.621 0 1.125-.504 1.125-1.125v-3.026a2.999 2.999 0 0 1 0-5.198V6.375c0-.621-.504-1.125-1.125-1.125H3.375Z"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              <span
                class="hidden lg:inline"
              >
                10 Créditos
              </span>
              <span
                class="lg:hidden"
              >
                10
              </span>
            </a>
            <button
              class="lg:hidden p-4"
              role="button"
            >
              <svg
                aria-hidden="true"
                class="w-6 h-6"
                data-slot="icon"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
            <button
              class="w-10 h-10 flex justify-center items-center"
              id="dropdownMenuButton1"
              role="button"
            >
              <svg
                aria-hidden="true"
                class="w-6 h-6"
                data-slot="icon"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              <span
                class="absolute -mt-9 ml-3.5 rounded-full bg-danger px-[0.45em] py-[0.15em] text-[0.6rem] font-bold leading-none text-white"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 8 8"
                  width="8"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle
                    cx="4"
                    cy="4"
                    fill="#D63649"
                    r="4"
                  />
                </svg>
              </span>
            </button>
            <button
              class="hidden lg:block"
              role="button"
            >
              <img
                class="rounded-full w-8 h-8"
                src="https://tecdn.b-cdn.net/img/new/avatars/2.jpg"
              />
            </button>
            <button
              class="flex w-10 h-10 justify-center items-center lg:hidden"
            >
              <svg
                aria-hidden="true"
                class="w-6 h-6"
                data-slot="icon"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
          </div>
          <div
            class="block fixed top-0 left-0 w-[20.5rem] h-full bg-white borber-r border-gray-100 lg:hidden z-50 duration-200 transition-all ease-out -translate-x-full"
          >
            <section
              class="relative flex flex-col"
            >
              <button
                class="flex w-10 h-10 justify-center items-center lg:hidden absolute right-0 top-5 z-20 hover:rotate-90 transition-all duration-900"
              >
                <svg
                  aria-hidden="true"
                  class="w-6 h-6"
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6 18 18 6M6 6l12 12"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
              <div
                class="relative"
                data-testid="sidebar"
              >
                <nav
                  class="flex flex-col flex-auto items-start gap-4 p-6 h-full w-64 flex-shrink-0 min-h-screen antialiased bg-white border-r border-gray-100"
                  data-testid="opened-sidebar"
                >
                  <div
                    class="flex flex-col gap-4 justify-center items-center"
                  >
                    <a
                      class="h-7 mb-2"
                      href="/"
                    >
                      <img
                        alt="Logo"
                        class="block h-full w-auto"
                        src=""
                      />
                    </a>
                    <div
                      class="block lg:hidden flex w-full gap-2 items-center"
                    >
                      <img
                        class="rounded-full w-8 h-8"
                        loading="lazy"
                        src="https://tecdn.b-cdn.net/img/new/avatars/2.jpg"
                        style="max-width: none;"
                      />
                      <p
                        class="font-outfit  text-xs capitalize text-black-900"
                      >
                        Bruno Sousa
                      </p>
                    </div>
                  </div>
                  <span
                    class="w-full border-b border-gray-100"
                  />
                  <button
                    class="w-full py-3 px-4 rounded-md font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-xs flex justify-center items-center"
                  >
                    components.Sidebar.text
                    <svg
                      aria-hidden="true"
                      class="w-8 h-5"
                      data-slot="icon"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="1.5"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 4.5v15m7.5-7.5h-15"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </button>
                  <span
                    class="w-full border-b border-gray-100"
                  />
                  <div
                    class="w-full"
                  >
                    <nav
                      class="flex flex-col gap-2 "
                    >
                      <a
                        class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                        href="/"
                      >
                        <svg
                          aria-hidden="true"
                          class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <span
                          class=" text-xs tracking-wide truncate text-black-900"
                        >
                          components.Sidebar.labels.home
                        </span>
                      </a>
                      <a
                        class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                        href="/"
                      >
                        <svg
                          aria-hidden="true"
                          class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535M10.34 6.66a23.847 23.847 0 0 0 8.835-2.535m0 0A23.74 23.74 0 0 0 18.795 3m.38 1.125a23.91 23.91 0 0 1 1.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 0 0 1.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 0 1 0 3.46"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <span
                          class=" text-xs tracking-wide truncate text-black-900"
                        >
                          components.Sidebar.labels.megaphone
                        </span>
                      </a>
                      <a
                        class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                        href="/"
                      >
                        <svg
                          aria-hidden="true"
                          class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <span
                          class=" text-xs tracking-wide truncate text-black-900"
                        >
                          components.Sidebar.labels.calendarDays
                        </span>
                      </a>
                      <a
                        class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                        href="/"
                      >
                        <svg
                          aria-hidden="true"
                          class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M13.5 21v-7.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349M3.75 21V9.349m0 0a3.001 3.001 0 0 0 3.75-.615A2.993 2.993 0 0 0 9.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 0 0 2.25 1.016c.896 0 1.7-.393 2.25-1.015a3.001 3.001 0 0 0 3.75.614m-16.5 0a3.004 3.004 0 0 1-.621-4.72l1.189-1.19A1.5 1.5 0 0 1 5.378 3h13.243a1.5 1.5 0 0 1 1.06.44l1.19 1.189a3 3 0 0 1-.621 4.72M6.75 18h3.75a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75H6.75a.75.75 0 0 0-.75.75v3.75c0 .414.336.75.75.75Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <span
                          class=" text-xs tracking-wide truncate text-black-900"
                        >
                          components.Sidebar.labels.buildingStorefront
                        </span>
                      </a>
                      <a
                        class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                        href="/"
                      >
                        <svg
                          aria-hidden="true"
                          class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <span
                          class=" text-xs tracking-wide truncate text-black-900"
                        >
                          components.Sidebar.labels.users
                        </span>
                      </a>
                      <a
                        class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                        href="/"
                      >
                        <svg
                          aria-hidden="true"
                          class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M3.75 3v11.25A2.25 2.25 0 0 0 6 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0 1 18 16.5h-2.25m-7.5 0h7.5m-7.5 0-1 3m8.5-3 1 3m0 0 .5 1.5m-.5-1.5h-9.5m0 0-.5 1.5M9 11.25v1.5M12 9v3.75m3-6v6"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <span
                          class=" text-xs tracking-wide truncate text-black-900"
                        >
                          components.Sidebar.labels.presentationChartBar
                        </span>
                      </a>
                      <span
                        class="flex items-center justify-center w-[14.5rem] h-[0.0625rem] border-b border-b-neutral-50"
                      />
                      <a
                        class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300"
                        href="/"
                      >
                        <svg
                          aria-hidden="true"
                          class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5 "
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <span
                          class=" text-xs tracking-wide truncate text-black-900"
                        >
                          components.Sidebar.labels.user
                        </span>
                      </a>
                      <a
                        class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300"
                        href="/"
                      >
                        <svg
                          aria-hidden="true"
                          class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5 "
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <span
                          class=" text-xs tracking-wide truncate text-black-900"
                        >
                          components.Sidebar.labels.lockClose
                        </span>
                      </a>
                      <a
                        class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300"
                        href="/"
                      >
                        <svg
                          aria-hidden="true"
                          class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5 "
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <span
                          class=" text-xs tracking-wide truncate text-black-900"
                        >
                          components.Sidebar.labels.creditCar
                        </span>
                      </a>
                      <a
                        class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300"
                        href="/"
                      >
                        <svg
                          aria-hidden="true"
                          class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5 "
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                          <path
                            d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <span
                          class=" text-xs tracking-wide truncate text-black-900"
                        >
                          components.Sidebar.labels.cog6Tooth
                        </span>
                      </a>
                    </nav>
                  </div>
                </nav>
                <button
                  class="absolute bottom-4 right-4 flex justify-end w-full"
                  data-testid="left-arrow"
                >
                  <svg
                    aria-hidden="true"
                    class="w-4 h-4 transition ease-in-out delay-150 duration-300 hover:-translate-x-1 hover:text-black-900 text-yellow-900 cursor-pointer"
                    data-slot="icon"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="1.5"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </button>
              </div>
            </section>
          </div>
        </nav>
      </header>
      <section
        class="h-full w-full overflow-hidden transition-all"
      >
        <div
          class="relative h-full w-full overflow-x-hidden overflow-y-scroll"
        >
          <div
            class="flex flex-col xl:flex-row h-full w-full overflow-hidden"
          >
            <div
              class="w-full xh-full overflow-y-auto scrollbar-hide scrollbar-hide p-5"
            >
              <div
                class="container m-auto"
              >
                <div
                  class="flex justify-between mt-14"
                >
                  <h1
                    class="font-outfit font-[700] text-[1.5rem] flex gap-1 items-center"
                  >
                    <svg
                      aria-hidden="true"
                      class="stroke-[0.1rem]"
                      data-slot="icon"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="1.5"
                      viewBox="0 0 24 24"
                      width="1.5rem"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535M10.34 6.66a23.847 23.847 0 0 0 8.835-2.535m0 0A23.74 23.74 0 0 0 18.795 3m.38 1.125a23.91 23.91 0 0 1 1.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 0 0 1.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 0 1 0 3.46"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                     
                    campaign-list.title
                  </h1>
                  <button
                    class="py-3 px-4 rounded-md font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-xs flex items-center w-fit"
                  >
                    campaign-list.new-campaign
                    <svg
                      aria-hidden="true"
                      class="w-8 h-5"
                      data-slot="icon"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="1.5"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 4.5v15m7.5-7.5h-15"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </button>
                </div>
                <div
                  class="flex max-[1023px]:flex-col items-end gap-4 bg-white border border-gray-100 rounded-lg mt-8 mb-12 p-4"
                >
                  <div
                    class="flex max-[639px]:flex-col gap-4 lg:grow max-[1023px]:w-full"
                  >
                    <div
                      class="lg:w-[55%] w-full"
                    >
                      <p
                        class="font-outfit text-xs font-semibold mb-1"
                      >
                        campaign-list.filter.placeholders.campaign
                      </p>
                      <div
                        class="relative flex flex-row items-center w-full"
                        data-testid="input"
                      >
                        <input
                          class="py-[0.9375rem] px-[0.8125rem] text-xs border rounded-lg bg-white w-full h-10 disabled:border-gray-200 focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none border-gray-700"
                          placeholder="campaign-list.filter.placeholders.campaign"
                        />
                      </div>
                    </div>
                    <div
                      class="lg:w-[45%] w-full"
                    >
                      <p
                        class="font-outfit text-xs font-semibold mb-1"
                      >
                        campaign-list.filter.placeholders.client
                      </p>
                      <div
                        class="relative flex flex-row items-center w-full"
                        data-testid="input"
                      >
                        <input
                          class="py-[0.9375rem] px-[0.8125rem] text-xs border rounded-lg bg-white w-full h-10 disabled:border-gray-200 focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none border-gray-700"
                          placeholder="campaign-list.filter.placeholders.client"
                        />
                      </div>
                    </div>
                  </div>
                  <div
                    class="flex max-[639px]:flex-col gap-4 lg:w-2/5 w-full"
                  >
                    <div
                      class="w-full"
                    >
                      <p
                        class="font-outfit text-xs font-semibold mb-1"
                      >
                        campaign-list.filter.placeholders.status
                      </p>
                      <div
                        class=" css-b62m3t-container"
                      >
                        <span
                          class="css-1f43avz-a11yText-A11yText"
                          id="react-select-2-live-region"
                        />
                        <span
                          aria-atomic="false"
                          aria-live="polite"
                          aria-relevant="additions text"
                          class="css-1f43avz-a11yText-A11yText"
                          role="log"
                        />
                        <div
                          class=" css-g5lwu7-control"
                        >
                          <div
                            class=" css-1fdsijx-ValueContainer"
                          >
                            <div
                              class=" css-1jqq78o-placeholder"
                              id="react-select-2-placeholder"
                            >
                              components.DropDown.placeholder
                            </div>
                            <div
                              class=" css-qbdosj-Input"
                              data-value=""
                            >
                              <input
                                aria-activedescendant=""
                                aria-autocomplete="list"
                                aria-describedby="react-select-2-placeholder"
                                aria-expanded="false"
                                aria-haspopup="true"
                                autocapitalize="none"
                                autocomplete="off"
                                autocorrect="off"
                                class=""
                                id="react-select-2-input"
                                role="combobox"
                                spellcheck="false"
                                style="opacity: 1; width: 100%; grid-area: 1 / 2; min-width: 2px; border: 0px; margin: 0px; outline: 0; padding: 0px;"
                                tabindex="0"
                                type="text"
                                value=""
                              />
                            </div>
                          </div>
                          <div
                            class=" css-1hb7zxy-IndicatorsContainer"
                          >
                            <span
                              class=" css-1uei4ir-indicatorSeparator"
                            />
                            <div
                              aria-hidden="true"
                              class=" css-1xc3v61-indicatorContainer"
                            >
                              <svg
                                aria-hidden="true"
                                class="css-tj5bde-Svg"
                                focusable="false"
                                height="20"
                                viewBox="0 0 20 20"
                                width="20"
                              >
                                <path
                                  d="M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"
                                />
                              </svg>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      class="w-full"
                    >
                      <p
                        class="font-outfit text-xs font-semibold mb-1"
                      >
                        campaign-list.filter.placeholders.social-network
                      </p>
                      <div
                        class=" css-b62m3t-container"
                      >
                        <span
                          class="css-1f43avz-a11yText-A11yText"
                          id="react-select-3-live-region"
                        />
                        <span
                          aria-atomic="false"
                          aria-live="polite"
                          aria-relevant="additions text"
                          class="css-1f43avz-a11yText-A11yText"
                          role="log"
                        />
                        <div
                          class=" css-g5lwu7-control"
                        >
                          <div
                            class=" css-1fdsijx-ValueContainer"
                          >
                            <div
                              class=" css-1jqq78o-placeholder"
                              id="react-select-3-placeholder"
                            >
                              components.DropDown.placeholder
                            </div>
                            <div
                              class=" css-qbdosj-Input"
                              data-value=""
                            >
                              <input
                                aria-activedescendant=""
                                aria-autocomplete="list"
                                aria-describedby="react-select-3-placeholder"
                                aria-expanded="false"
                                aria-haspopup="true"
                                autocapitalize="none"
                                autocomplete="off"
                                autocorrect="off"
                                class=""
                                id="react-select-3-input"
                                role="combobox"
                                spellcheck="false"
                                style="opacity: 1; width: 100%; grid-area: 1 / 2; min-width: 2px; border: 0px; margin: 0px; outline: 0; padding: 0px;"
                                tabindex="0"
                                type="text"
                                value=""
                              />
                            </div>
                          </div>
                          <div
                            class=" css-1hb7zxy-IndicatorsContainer"
                          >
                            <span
                              class=" css-1uei4ir-indicatorSeparator"
                            />
                            <div
                              aria-hidden="true"
                              class=" css-1xc3v61-indicatorContainer"
                            >
                              <svg
                                aria-hidden="true"
                                class="css-tj5bde-Svg"
                                focusable="false"
                                height="20"
                                viewBox="0 0 20 20"
                                width="20"
                              >
                                <path
                                  d="M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"
                                />
                              </svg>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    class="py-3 px-4 rounded-md font-outfit font-medium tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-xs lg:w-fit w-full h-fit"
                  >
                    campaign-list.filter.button
                  </button>
                </div>
                <div
                  class="grid sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-4 2xl:grid-cols-5 gap-4 pb-16"
                >
                  <div
                    class="relative rounded-xl border-0 bg-white border-gray-100 p-3"
                  >
                    <img
                      class="rounded-[0.75rem] mb-2 w-full"
                      src=""
                    />
                    <div
                      class="flex flex-col gap-1"
                    >
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4 text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535M10.34 6.66a23.847 23.847 0 0 0 8.835-2.535m0 0A23.74 23.74 0 0 0 18.795 3m.38 1.125a23.91 23.91 0 0 1 1.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 0 0 1.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 0 1 0 3.46"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.3125rem]"
                        >
                          Quer ficar grandao?
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4 text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M13.5 21v-7.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349M3.75 21V9.349m0 0a3.001 3.001 0 0 0 3.75-.615A2.993 2.993 0 0 0 9.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 0 0 2.25 1.016c.896 0 1.7-.393 2.25-1.015a3.001 3.001 0 0 0 3.75.614m-16.5 0a3.004 3.004 0 0 1-.621-4.72l1.189-1.19A1.5 1.5 0 0 1 5.378 3h13.243a1.5 1.5 0 0 1 1.06.44l1.19 1.189a3 3 0 0 1-.621 4.72M6.75 18h3.75a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75H6.75a.75.75 0 0 0-.75.75v3.75c0 .414.336.75.75.75Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]"
                        >
                          Eric Personal
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4  text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]"
                        >
                          campaign-list.cards.status.finished
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4 text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]"
                        >
                          10/12/2023 
                          campaign-list.cards.preposition
                           10/02/2024
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <div
                          class="flex gap-1"
                        >
                          <svg
                            aria-hidden="true"
                            class="w-4 h-4 mt-1 mr-1 text-gray-800"
                            data-slot="icon"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="1.5"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M7.217 10.907a2.25 2.25 0 1 0 0 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186 9.566-5.314m-9.566 7.5 9.566 5.314m0 0a2.25 2.25 0 1 0 3.935 2.186 2.25 2.25 0 0 0-3.935-2.186Zm0-12.814a2.25 2.25 0 1 0 3.933-2.185 2.25 2.25 0 0 0-3.933 2.185Z"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                          <svg
                            class="w-5 h-5"
                            height="16"
                            viewBox="0 0 16 16"
                            width="16"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <circle
                              cx="8"
                              cy="8"
                              fill="#2476F1"
                              r="6"
                            />
                            <svg
                              class="text-white"
                              viewBox="-1 -1 10 8"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                class="fill-current"
                                d="M4.75958 1.33001H5.22958V0.535007C5.00202 0.511344 4.77337 0.499661 4.54458 0.500007C3.86458 0.500007 3.39958 0.915007 3.39958 1.67501V2.33001H2.63208V3.22001H3.39958V5.50001H4.31958V3.22001H5.08458L5.19958 2.33001H4.31958V1.76251C4.31958 1.50001 4.38958 1.33001 4.75958 1.33001Z"
                              />
                            </svg>
                          </svg>
                          <svg
                            class="w-5 h-5"
                            height="16"
                            viewBox="0 0 16 16"
                            width="16"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <defs>
                              <lineargradient
                                id="b"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="100%"
                              >
                                <stop
                                  offset="0"
                                  stop-color="#3771c8"
                                />
                                <stop
                                  offset=".128"
                                  stop-color="#3771c8"
                                />
                                <stop
                                  offset="1"
                                  stop-color="#60f"
                                  stop-opacity="0"
                                />
                              </lineargradient>
                              <lineargradient
                                id="a"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="100%"
                              >
                                <stop
                                  offset="0"
                                  stop-color="#fd5"
                                />
                                <stop
                                  offset=".1"
                                  stop-color="#fd5"
                                />
                                <stop
                                  offset=".5"
                                  stop-color="#ff543e"
                                />
                                <stop
                                  offset="1"
                                  stop-color="#c837ab"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="8"
                              cy="8"
                              fill="url(#b)"
                              r="6"
                            />
                            <circle
                              cx="8"
                              cy="8"
                              fill="url(#a)"
                              r="6"
                            />
                            <svg
                              class="text-white"
                              height="13"
                              viewBox="-3 -2 10 8"
                              width="13"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <defs>
                                <lineargradient
                                  id="grad1"
                                  x1="0%"
                                  x2="100%"
                                  y1="0%"
                                  y2="0%"
                                >
                                  <stop
                                    offset="0%"
                                  />
                                  <stop
                                    offset="100%"
                                  />
                                </lineargradient>
                              </defs>
                              <g
                                clip-path="url(#clip0_3774_843)"
                              >
                                <path
                                  class="fill-current"
                                  d="M4.335 1.365C4.27567 1.365 4.21766 1.38259 4.16833 1.41556C4.11899 1.44852 4.08054 1.49538 4.05784 1.55019C4.03513 1.60501 4.02919 1.66533 4.04076 1.72353C4.05234 1.78172 4.08091 1.83518 4.12287 1.87713C4.16482 1.91909 4.21828 1.94766 4.27647 1.95924C4.33467 1.97081 4.39499 1.96487 4.44981 1.94216C4.50462 1.91946 4.55148 1.88101 4.58444 1.83167C4.6174 1.78234 4.635 1.72433 4.635 1.665C4.635 1.58544 4.60339 1.50913 4.54713 1.45287C4.49087 1.39661 4.41457 1.365 4.335 1.365ZM5.485 1.97C5.48014 1.76257 5.44129 1.55735 5.37 1.3625C5.30643 1.19578 5.2075 1.04482 5.08 0.92C4.95621 0.791856 4.80489 0.693544 4.6375 0.6325C4.44316 0.55904 4.23771 0.519302 4.03 0.515C3.765 0.5 3.68 0.5 3 0.5C2.32 0.5 2.235 0.5 1.97 0.515C1.76229 0.519302 1.55684 0.55904 1.3625 0.6325C1.19542 0.694162 1.04423 0.79239 0.92 0.92C0.791856 1.04379 0.693544 1.19511 0.6325 1.3625C0.55904 1.55684 0.519302 1.76229 0.515 1.97C0.5 2.235 0.5 2.32 0.5 3C0.5 3.68 0.5 3.765 0.515 4.03C0.519302 4.23771 0.55904 4.44316 0.6325 4.6375C0.693544 4.80489 0.791856 4.95621 0.92 5.08C1.04423 5.20761 1.19542 5.30584 1.3625 5.3675C1.55684 5.44096 1.76229 5.4807 1.97 5.485C2.235 5.5 2.32 5.5 3 5.5C3.68 5.5 3.765 5.5 4.03 5.485C4.23771 5.4807 4.44316 5.44096 4.6375 5.3675C4.80489 5.30646 4.95621 5.20814 5.08 5.08C5.20806 4.95565 5.30708 4.80455 5.37 4.6375C5.44129 4.44265 5.48014 4.23743 5.485 4.03C5.485 3.765 5.5 3.68 5.5 3C5.5 2.32 5.5 2.235 5.485 1.97ZM5.035 4C5.03318 4.15869 5.00444 4.31593 4.95 4.465C4.91008 4.5738 4.84597 4.67211 4.7625 4.7525C4.68141 4.83513 4.58331 4.89911 4.475 4.94C4.32593 4.99444 4.16869 5.02318 4.01 5.025C3.76 5.0375 3.6675 5.04 3.01 5.04C2.3525 5.04 2.26 5.04 2.01 5.025C1.84522 5.02809 1.68115 5.00271 1.525 4.95C1.42145 4.90702 1.32784 4.8432 1.25 4.7625C1.16702 4.68219 1.10371 4.5838 1.065 4.475C1.00396 4.32379 0.970111 4.16299 0.965 4C0.965 3.75 0.95 3.6575 0.95 3C0.95 2.3425 0.95 2.25 0.965 2C0.966121 1.83776 0.995738 1.67699 1.0525 1.525C1.09651 1.41948 1.16407 1.32541 1.25 1.25C1.32595 1.16404 1.41982 1.09577 1.525 1.05C1.67739 0.995011 1.838 0.96627 2 0.965C2.25 0.965 2.3425 0.95 3 0.95C3.6575 0.95 3.75 0.95 4 0.965C4.15869 0.96682 4.31593 0.995562 4.465 1.05C4.57861 1.09216 4.68057 1.16071 4.7625 1.25C4.84443 1.32679 4.90844 1.42068 4.95 1.525C5.00556 1.67723 5.03432 1.83794 5.035 2C5.0475 2.25 5.05 2.3425 5.05 3C5.05 3.6575 5.0475 3.75 5.035 4ZM3 1.7175C2.74645 1.71799 2.49874 1.79363 2.28816 1.93485C2.07759 2.07608 1.91359 2.27655 1.81691 2.51094C1.72022 2.74533 1.69518 3.00312 1.74495 3.25173C1.79472 3.50035 1.91706 3.72863 2.09652 3.90774C2.27598 4.08686 2.5045 4.20875 2.75321 4.25804C3.00193 4.30732 3.25967 4.28178 3.49387 4.18463C3.72807 4.08749 3.92822 3.92311 4.06903 3.71226C4.20984 3.50141 4.285 3.25355 4.285 3C4.28533 2.83128 4.2523 2.66415 4.18781 2.50824C4.12332 2.35233 4.02864 2.2107 3.90922 2.09151C3.7898 1.97232 3.64799 1.87792 3.49195 1.81373C3.33591 1.74954 3.16872 1.71684 3 1.7175ZM3 3.8325C2.83535 3.8325 2.67439 3.78367 2.53749 3.6922C2.40058 3.60072 2.29388 3.4707 2.23087 3.31858C2.16786 3.16646 2.15137 2.99908 2.1835 2.83759C2.21562 2.6761 2.29491 2.52776 2.41133 2.41133C2.52776 2.29491 2.6761 2.21562 2.83759 2.1835C2.99908 2.15137 3.16646 2.16786 3.31858 2.23087C3.4707 2.29388 3.60072 2.40058 3.6922 2.53749C3.78367 2.67439 3.8325 2.83535 3.8325 3C3.8325 3.10933 3.81097 3.21758 3.76913 3.31858C3.72729 3.41959 3.66597 3.51136 3.58867 3.58867C3.51136 3.66597 3.41959 3.72729 3.31858 3.76913C3.21758 3.81097 3.10933 3.8325 3 3.8325Z"
                                  fill="url(#grad1)"
                                />
                              </g>
                              <defs>
                                <clippath
                                  id="clip0_3774_843"
                                >
                                  <rect
                                    fill="white"
                                    height="6"
                                    width="6"
                                  />
                                </clippath>
                              </defs>
                            </svg>
                          </svg>
                          <svg
                            class="w-5 h-5"
                            height="16"
                            viewBox="0 0 16 16"
                            width="16"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <circle
                              cx="8"
                              cy="8"
                              fill="#2476F1"
                              r="6"
                            />
                            <svg
                              fill="none"
                              height="13"
                              viewBox="-2 -3 8 10"
                              width="13"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <g
                                clip-path="url(#clip0_3748_803)"
                              >
                                <path
                                  d="M5.11739 0.499974H0.882393C0.834787 0.499313 0.787518 0.508036 0.743284 0.525645C0.69905 0.543254 0.658718 0.569404 0.624592 0.602602C0.590466 0.6358 0.563213 0.675395 0.544391 0.719126C0.525568 0.762858 0.515545 0.809868 0.514893 0.857474V5.14247C0.515545 5.19008 0.525568 5.23709 0.544391 5.28082C0.563213 5.32455 0.590466 5.36415 0.624592 5.39735C0.658718 5.43054 0.69905 5.45669 0.743284 5.4743C0.787518 5.49191 0.834787 5.50064 0.882393 5.49997H5.11739C5.165 5.50064 5.21227 5.49191 5.2565 5.4743C5.30073 5.45669 5.34107 5.43054 5.37519 5.39735C5.40932 5.36415 5.43657 5.32455 5.45539 5.28082C5.47422 5.23709 5.48424 5.19008 5.48489 5.14247V0.857474C5.48424 0.809868 5.47422 0.762858 5.45539 0.719126C5.43657 0.675395 5.40932 0.6358 5.37519 0.602602C5.34107 0.569404 5.30073 0.543254 5.2565 0.525645C5.21227 0.508036 5.165 0.499313 5.11739 0.499974ZM2.02239 4.68497H1.27239V2.43497H2.02239V4.68497ZM1.64739 2.11997C1.54396 2.11997 1.44476 2.07888 1.37162 2.00575C1.29848 1.93261 1.25739 1.83341 1.25739 1.72997C1.25739 1.62654 1.29848 1.52734 1.37162 1.4542C1.44476 1.38106 1.54396 1.33997 1.64739 1.33997C1.70232 1.33375 1.75794 1.33919 1.81061 1.35594C1.86329 1.3727 1.91183 1.4004 1.95306 1.43721C1.99429 1.47403 2.02727 1.51914 2.04986 1.56959C2.07245 1.62005 2.08412 1.6747 2.08412 1.72997C2.08412 1.78525 2.07245 1.8399 2.04986 1.89035C2.02727 1.9408 1.99429 1.98592 1.95306 2.02273C1.91183 2.05955 1.86329 2.08725 1.81061 2.104C1.75794 2.12076 1.70232 2.1262 1.64739 2.11997ZM4.72739 4.68497H3.97739V3.47747C3.97739 3.17497 3.86989 2.97747 3.59739 2.97747C3.51306 2.97809 3.43094 3.00454 3.3621 3.05327C3.29326 3.10199 3.24101 3.17064 3.21239 3.24997C3.19283 3.30873 3.18435 3.37062 3.18739 3.43247V4.68247H2.43739V2.43247H3.18739V2.74997C3.25553 2.63175 3.35462 2.53435 3.474 2.46827C3.59339 2.4022 3.72853 2.36994 3.86489 2.37497C4.36489 2.37497 4.72739 2.69747 4.72739 3.38997V4.68497Z"
                                  fill="white"
                                />
                              </g>
                              <defs>
                                <clippath
                                  id="clip0_3748_803"
                                >
                                  <rect
                                    class=" fill-current text-white"
                                    height="6"
                                    width="6"
                                  />
                                </clippath>
                              </defs>
                            </svg>
                          </svg>
                        </div>
                      </div>
                    </div>
                    <div
                      class="absolute -top-[0.525rem] -right-[0.675rem] shrink h-42 w-32"
                    />
                  </div>
                  <div
                    class="relative rounded-xl border-0 bg-white border-gray-100 p-3"
                  >
                    <img
                      class="rounded-[0.75rem] mb-2 w-full"
                      src=""
                    />
                    <div
                      class="flex flex-col gap-1"
                    >
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4 text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535M10.34 6.66a23.847 23.847 0 0 0 8.835-2.535m0 0A23.74 23.74 0 0 0 18.795 3m.38 1.125a23.91 23.91 0 0 1 1.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 0 0 1.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 0 1 0 3.46"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.3125rem]"
                        >
                          Quer ficar grandao?
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4 text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M13.5 21v-7.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349M3.75 21V9.349m0 0a3.001 3.001 0 0 0 3.75-.615A2.993 2.993 0 0 0 9.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 0 0 2.25 1.016c.896 0 1.7-.393 2.25-1.015a3.001 3.001 0 0 0 3.75.614m-16.5 0a3.004 3.004 0 0 1-.621-4.72l1.189-1.19A1.5 1.5 0 0 1 5.378 3h13.243a1.5 1.5 0 0 1 1.06.44l1.19 1.189a3 3 0 0 1-.621 4.72M6.75 18h3.75a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75H6.75a.75.75 0 0 0-.75.75v3.75c0 .414.336.75.75.75Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]"
                        >
                          Eric Personal
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4  text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]"
                        >
                          Finalizada
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4 text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]"
                        >
                          10/12/2023 
                          campaign-list.cards.preposition
                           10/02/2024
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <div
                          class="flex gap-1"
                        >
                          <svg
                            aria-hidden="true"
                            class="w-4 h-4 mt-1 mr-1 text-gray-800"
                            data-slot="icon"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="1.5"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M7.217 10.907a2.25 2.25 0 1 0 0 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186 9.566-5.314m-9.566 7.5 9.566 5.314m0 0a2.25 2.25 0 1 0 3.935 2.186 2.25 2.25 0 0 0-3.935-2.186Zm0-12.814a2.25 2.25 0 1 0 3.933-2.185 2.25 2.25 0 0 0-3.933 2.185Z"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                          <svg
                            class="w-5 h-5"
                            height="16"
                            viewBox="0 0 16 16"
                            width="16"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <circle
                              cx="8"
                              cy="8"
                              fill="#2476F1"
                              r="6"
                            />
                            <svg
                              class="text-white"
                              viewBox="-1 -1 10 8"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                class="fill-current"
                                d="M4.75958 1.33001H5.22958V0.535007C5.00202 0.511344 4.77337 0.499661 4.54458 0.500007C3.86458 0.500007 3.39958 0.915007 3.39958 1.67501V2.33001H2.63208V3.22001H3.39958V5.50001H4.31958V3.22001H5.08458L5.19958 2.33001H4.31958V1.76251C4.31958 1.50001 4.38958 1.33001 4.75958 1.33001Z"
                              />
                            </svg>
                          </svg>
                          <svg
                            class="w-5 h-5"
                            height="16"
                            viewBox="0 0 16 16"
                            width="16"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <defs>
                              <lineargradient
                                id="b"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="100%"
                              >
                                <stop
                                  offset="0"
                                  stop-color="#3771c8"
                                />
                                <stop
                                  offset=".128"
                                  stop-color="#3771c8"
                                />
                                <stop
                                  offset="1"
                                  stop-color="#60f"
                                  stop-opacity="0"
                                />
                              </lineargradient>
                              <lineargradient
                                id="a"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="100%"
                              >
                                <stop
                                  offset="0"
                                  stop-color="#fd5"
                                />
                                <stop
                                  offset=".1"
                                  stop-color="#fd5"
                                />
                                <stop
                                  offset=".5"
                                  stop-color="#ff543e"
                                />
                                <stop
                                  offset="1"
                                  stop-color="#c837ab"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="8"
                              cy="8"
                              fill="url(#b)"
                              r="6"
                            />
                            <circle
                              cx="8"
                              cy="8"
                              fill="url(#a)"
                              r="6"
                            />
                            <svg
                              class="text-white"
                              height="13"
                              viewBox="-3 -2 10 8"
                              width="13"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <defs>
                                <lineargradient
                                  id="grad1"
                                  x1="0%"
                                  x2="100%"
                                  y1="0%"
                                  y2="0%"
                                >
                                  <stop
                                    offset="0%"
                                  />
                                  <stop
                                    offset="100%"
                                  />
                                </lineargradient>
                              </defs>
                              <g
                                clip-path="url(#clip0_3774_843)"
                              >
                                <path
                                  class="fill-current"
                                  d="M4.335 1.365C4.27567 1.365 4.21766 1.38259 4.16833 1.41556C4.11899 1.44852 4.08054 1.49538 4.05784 1.55019C4.03513 1.60501 4.02919 1.66533 4.04076 1.72353C4.05234 1.78172 4.08091 1.83518 4.12287 1.87713C4.16482 1.91909 4.21828 1.94766 4.27647 1.95924C4.33467 1.97081 4.39499 1.96487 4.44981 1.94216C4.50462 1.91946 4.55148 1.88101 4.58444 1.83167C4.6174 1.78234 4.635 1.72433 4.635 1.665C4.635 1.58544 4.60339 1.50913 4.54713 1.45287C4.49087 1.39661 4.41457 1.365 4.335 1.365ZM5.485 1.97C5.48014 1.76257 5.44129 1.55735 5.37 1.3625C5.30643 1.19578 5.2075 1.04482 5.08 0.92C4.95621 0.791856 4.80489 0.693544 4.6375 0.6325C4.44316 0.55904 4.23771 0.519302 4.03 0.515C3.765 0.5 3.68 0.5 3 0.5C2.32 0.5 2.235 0.5 1.97 0.515C1.76229 0.519302 1.55684 0.55904 1.3625 0.6325C1.19542 0.694162 1.04423 0.79239 0.92 0.92C0.791856 1.04379 0.693544 1.19511 0.6325 1.3625C0.55904 1.55684 0.519302 1.76229 0.515 1.97C0.5 2.235 0.5 2.32 0.5 3C0.5 3.68 0.5 3.765 0.515 4.03C0.519302 4.23771 0.55904 4.44316 0.6325 4.6375C0.693544 4.80489 0.791856 4.95621 0.92 5.08C1.04423 5.20761 1.19542 5.30584 1.3625 5.3675C1.55684 5.44096 1.76229 5.4807 1.97 5.485C2.235 5.5 2.32 5.5 3 5.5C3.68 5.5 3.765 5.5 4.03 5.485C4.23771 5.4807 4.44316 5.44096 4.6375 5.3675C4.80489 5.30646 4.95621 5.20814 5.08 5.08C5.20806 4.95565 5.30708 4.80455 5.37 4.6375C5.44129 4.44265 5.48014 4.23743 5.485 4.03C5.485 3.765 5.5 3.68 5.5 3C5.5 2.32 5.5 2.235 5.485 1.97ZM5.035 4C5.03318 4.15869 5.00444 4.31593 4.95 4.465C4.91008 4.5738 4.84597 4.67211 4.7625 4.7525C4.68141 4.83513 4.58331 4.89911 4.475 4.94C4.32593 4.99444 4.16869 5.02318 4.01 5.025C3.76 5.0375 3.6675 5.04 3.01 5.04C2.3525 5.04 2.26 5.04 2.01 5.025C1.84522 5.02809 1.68115 5.00271 1.525 4.95C1.42145 4.90702 1.32784 4.8432 1.25 4.7625C1.16702 4.68219 1.10371 4.5838 1.065 4.475C1.00396 4.32379 0.970111 4.16299 0.965 4C0.965 3.75 0.95 3.6575 0.95 3C0.95 2.3425 0.95 2.25 0.965 2C0.966121 1.83776 0.995738 1.67699 1.0525 1.525C1.09651 1.41948 1.16407 1.32541 1.25 1.25C1.32595 1.16404 1.41982 1.09577 1.525 1.05C1.67739 0.995011 1.838 0.96627 2 0.965C2.25 0.965 2.3425 0.95 3 0.95C3.6575 0.95 3.75 0.95 4 0.965C4.15869 0.96682 4.31593 0.995562 4.465 1.05C4.57861 1.09216 4.68057 1.16071 4.7625 1.25C4.84443 1.32679 4.90844 1.42068 4.95 1.525C5.00556 1.67723 5.03432 1.83794 5.035 2C5.0475 2.25 5.05 2.3425 5.05 3C5.05 3.6575 5.0475 3.75 5.035 4ZM3 1.7175C2.74645 1.71799 2.49874 1.79363 2.28816 1.93485C2.07759 2.07608 1.91359 2.27655 1.81691 2.51094C1.72022 2.74533 1.69518 3.00312 1.74495 3.25173C1.79472 3.50035 1.91706 3.72863 2.09652 3.90774C2.27598 4.08686 2.5045 4.20875 2.75321 4.25804C3.00193 4.30732 3.25967 4.28178 3.49387 4.18463C3.72807 4.08749 3.92822 3.92311 4.06903 3.71226C4.20984 3.50141 4.285 3.25355 4.285 3C4.28533 2.83128 4.2523 2.66415 4.18781 2.50824C4.12332 2.35233 4.02864 2.2107 3.90922 2.09151C3.7898 1.97232 3.64799 1.87792 3.49195 1.81373C3.33591 1.74954 3.16872 1.71684 3 1.7175ZM3 3.8325C2.83535 3.8325 2.67439 3.78367 2.53749 3.6922C2.40058 3.60072 2.29388 3.4707 2.23087 3.31858C2.16786 3.16646 2.15137 2.99908 2.1835 2.83759C2.21562 2.6761 2.29491 2.52776 2.41133 2.41133C2.52776 2.29491 2.6761 2.21562 2.83759 2.1835C2.99908 2.15137 3.16646 2.16786 3.31858 2.23087C3.4707 2.29388 3.60072 2.40058 3.6922 2.53749C3.78367 2.67439 3.8325 2.83535 3.8325 3C3.8325 3.10933 3.81097 3.21758 3.76913 3.31858C3.72729 3.41959 3.66597 3.51136 3.58867 3.58867C3.51136 3.66597 3.41959 3.72729 3.31858 3.76913C3.21758 3.81097 3.10933 3.8325 3 3.8325Z"
                                  fill="url(#grad1)"
                                />
                              </g>
                              <defs>
                                <clippath
                                  id="clip0_3774_843"
                                >
                                  <rect
                                    fill="white"
                                    height="6"
                                    width="6"
                                  />
                                </clippath>
                              </defs>
                            </svg>
                          </svg>
                          <svg
                            class="w-5 h-5"
                            height="16"
                            viewBox="0 0 16 16"
                            width="16"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <circle
                              cx="8"
                              cy="8"
                              fill="#2476F1"
                              r="6"
                            />
                            <svg
                              fill="none"
                              height="13"
                              viewBox="-2 -3 8 10"
                              width="13"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <g
                                clip-path="url(#clip0_3748_803)"
                              >
                                <path
                                  d="M5.11739 0.499974H0.882393C0.834787 0.499313 0.787518 0.508036 0.743284 0.525645C0.69905 0.543254 0.658718 0.569404 0.624592 0.602602C0.590466 0.6358 0.563213 0.675395 0.544391 0.719126C0.525568 0.762858 0.515545 0.809868 0.514893 0.857474V5.14247C0.515545 5.19008 0.525568 5.23709 0.544391 5.28082C0.563213 5.32455 0.590466 5.36415 0.624592 5.39735C0.658718 5.43054 0.69905 5.45669 0.743284 5.4743C0.787518 5.49191 0.834787 5.50064 0.882393 5.49997H5.11739C5.165 5.50064 5.21227 5.49191 5.2565 5.4743C5.30073 5.45669 5.34107 5.43054 5.37519 5.39735C5.40932 5.36415 5.43657 5.32455 5.45539 5.28082C5.47422 5.23709 5.48424 5.19008 5.48489 5.14247V0.857474C5.48424 0.809868 5.47422 0.762858 5.45539 0.719126C5.43657 0.675395 5.40932 0.6358 5.37519 0.602602C5.34107 0.569404 5.30073 0.543254 5.2565 0.525645C5.21227 0.508036 5.165 0.499313 5.11739 0.499974ZM2.02239 4.68497H1.27239V2.43497H2.02239V4.68497ZM1.64739 2.11997C1.54396 2.11997 1.44476 2.07888 1.37162 2.00575C1.29848 1.93261 1.25739 1.83341 1.25739 1.72997C1.25739 1.62654 1.29848 1.52734 1.37162 1.4542C1.44476 1.38106 1.54396 1.33997 1.64739 1.33997C1.70232 1.33375 1.75794 1.33919 1.81061 1.35594C1.86329 1.3727 1.91183 1.4004 1.95306 1.43721C1.99429 1.47403 2.02727 1.51914 2.04986 1.56959C2.07245 1.62005 2.08412 1.6747 2.08412 1.72997C2.08412 1.78525 2.07245 1.8399 2.04986 1.89035C2.02727 1.9408 1.99429 1.98592 1.95306 2.02273C1.91183 2.05955 1.86329 2.08725 1.81061 2.104C1.75794 2.12076 1.70232 2.1262 1.64739 2.11997ZM4.72739 4.68497H3.97739V3.47747C3.97739 3.17497 3.86989 2.97747 3.59739 2.97747C3.51306 2.97809 3.43094 3.00454 3.3621 3.05327C3.29326 3.10199 3.24101 3.17064 3.21239 3.24997C3.19283 3.30873 3.18435 3.37062 3.18739 3.43247V4.68247H2.43739V2.43247H3.18739V2.74997C3.25553 2.63175 3.35462 2.53435 3.474 2.46827C3.59339 2.4022 3.72853 2.36994 3.86489 2.37497C4.36489 2.37497 4.72739 2.69747 4.72739 3.38997V4.68497Z"
                                  fill="white"
                                />
                              </g>
                              <defs>
                                <clippath
                                  id="clip0_3748_803"
                                >
                                  <rect
                                    class=" fill-current text-white"
                                    height="6"
                                    width="6"
                                  />
                                </clippath>
                              </defs>
                            </svg>
                          </svg>
                        </div>
                      </div>
                    </div>
                    <div
                      class="absolute -top-[0.525rem] -right-[0.675rem] shrink h-42 w-32"
                    />
                  </div>
                  <div
                    class="relative rounded-xl border-0 bg-white border-gray-100 p-3"
                  >
                    <img
                      class="rounded-[0.75rem] mb-2 w-full"
                      src=""
                    />
                    <div
                      class="flex flex-col gap-1"
                    >
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4 text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535M10.34 6.66a23.847 23.847 0 0 0 8.835-2.535m0 0A23.74 23.74 0 0 0 18.795 3m.38 1.125a23.91 23.91 0 0 1 1.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 0 0 1.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 0 1 0 3.46"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.3125rem]"
                        >
                          Quer ficar grandao?
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4 text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M13.5 21v-7.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349M3.75 21V9.349m0 0a3.001 3.001 0 0 0 3.75-.615A2.993 2.993 0 0 0 9.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 0 0 2.25 1.016c.896 0 1.7-.393 2.25-1.015a3.001 3.001 0 0 0 3.75.614m-16.5 0a3.004 3.004 0 0 1-.621-4.72l1.189-1.19A1.5 1.5 0 0 1 5.378 3h13.243a1.5 1.5 0 0 1 1.06.44l1.19 1.189a3 3 0 0 1-.621 4.72M6.75 18h3.75a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75H6.75a.75.75 0 0 0-.75.75v3.75c0 .414.336.75.75.75Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]"
                        >
                          Eric Personal
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4  text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]"
                        >
                          Finalizada
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4 text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]"
                        >
                          10/12/2023 
                          campaign-list.cards.preposition
                           10/02/2024
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <div
                          class="flex gap-1"
                        >
                          <svg
                            aria-hidden="true"
                            class="w-4 h-4 mt-1 mr-1 text-gray-800"
                            data-slot="icon"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="1.5"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M7.217 10.907a2.25 2.25 0 1 0 0 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186 9.566-5.314m-9.566 7.5 9.566 5.314m0 0a2.25 2.25 0 1 0 3.935 2.186 2.25 2.25 0 0 0-3.935-2.186Zm0-12.814a2.25 2.25 0 1 0 3.933-2.185 2.25 2.25 0 0 0-3.933 2.185Z"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                          <svg
                            class="w-5 h-5"
                            height="16"
                            viewBox="0 0 16 16"
                            width="16"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <circle
                              cx="8"
                              cy="8"
                              fill="#2476F1"
                              r="6"
                            />
                            <svg
                              class="text-white"
                              viewBox="-1 -1 10 8"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                class="fill-current"
                                d="M4.75958 1.33001H5.22958V0.535007C5.00202 0.511344 4.77337 0.499661 4.54458 0.500007C3.86458 0.500007 3.39958 0.915007 3.39958 1.67501V2.33001H2.63208V3.22001H3.39958V5.50001H4.31958V3.22001H5.08458L5.19958 2.33001H4.31958V1.76251C4.31958 1.50001 4.38958 1.33001 4.75958 1.33001Z"
                              />
                            </svg>
                          </svg>
                          <svg
                            class="w-5 h-5"
                            height="16"
                            viewBox="0 0 16 16"
                            width="16"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <defs>
                              <lineargradient
                                id="b"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="100%"
                              >
                                <stop
                                  offset="0"
                                  stop-color="#3771c8"
                                />
                                <stop
                                  offset=".128"
                                  stop-color="#3771c8"
                                />
                                <stop
                                  offset="1"
                                  stop-color="#60f"
                                  stop-opacity="0"
                                />
                              </lineargradient>
                              <lineargradient
                                id="a"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="100%"
                              >
                                <stop
                                  offset="0"
                                  stop-color="#fd5"
                                />
                                <stop
                                  offset=".1"
                                  stop-color="#fd5"
                                />
                                <stop
                                  offset=".5"
                                  stop-color="#ff543e"
                                />
                                <stop
                                  offset="1"
                                  stop-color="#c837ab"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="8"
                              cy="8"
                              fill="url(#b)"
                              r="6"
                            />
                            <circle
                              cx="8"
                              cy="8"
                              fill="url(#a)"
                              r="6"
                            />
                            <svg
                              class="text-white"
                              height="13"
                              viewBox="-3 -2 10 8"
                              width="13"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <defs>
                                <lineargradient
                                  id="grad1"
                                  x1="0%"
                                  x2="100%"
                                  y1="0%"
                                  y2="0%"
                                >
                                  <stop
                                    offset="0%"
                                  />
                                  <stop
                                    offset="100%"
                                  />
                                </lineargradient>
                              </defs>
                              <g
                                clip-path="url(#clip0_3774_843)"
                              >
                                <path
                                  class="fill-current"
                                  d="M4.335 1.365C4.27567 1.365 4.21766 1.38259 4.16833 1.41556C4.11899 1.44852 4.08054 1.49538 4.05784 1.55019C4.03513 1.60501 4.02919 1.66533 4.04076 1.72353C4.05234 1.78172 4.08091 1.83518 4.12287 1.87713C4.16482 1.91909 4.21828 1.94766 4.27647 1.95924C4.33467 1.97081 4.39499 1.96487 4.44981 1.94216C4.50462 1.91946 4.55148 1.88101 4.58444 1.83167C4.6174 1.78234 4.635 1.72433 4.635 1.665C4.635 1.58544 4.60339 1.50913 4.54713 1.45287C4.49087 1.39661 4.41457 1.365 4.335 1.365ZM5.485 1.97C5.48014 1.76257 5.44129 1.55735 5.37 1.3625C5.30643 1.19578 5.2075 1.04482 5.08 0.92C4.95621 0.791856 4.80489 0.693544 4.6375 0.6325C4.44316 0.55904 4.23771 0.519302 4.03 0.515C3.765 0.5 3.68 0.5 3 0.5C2.32 0.5 2.235 0.5 1.97 0.515C1.76229 0.519302 1.55684 0.55904 1.3625 0.6325C1.19542 0.694162 1.04423 0.79239 0.92 0.92C0.791856 1.04379 0.693544 1.19511 0.6325 1.3625C0.55904 1.55684 0.519302 1.76229 0.515 1.97C0.5 2.235 0.5 2.32 0.5 3C0.5 3.68 0.5 3.765 0.515 4.03C0.519302 4.23771 0.55904 4.44316 0.6325 4.6375C0.693544 4.80489 0.791856 4.95621 0.92 5.08C1.04423 5.20761 1.19542 5.30584 1.3625 5.3675C1.55684 5.44096 1.76229 5.4807 1.97 5.485C2.235 5.5 2.32 5.5 3 5.5C3.68 5.5 3.765 5.5 4.03 5.485C4.23771 5.4807 4.44316 5.44096 4.6375 5.3675C4.80489 5.30646 4.95621 5.20814 5.08 5.08C5.20806 4.95565 5.30708 4.80455 5.37 4.6375C5.44129 4.44265 5.48014 4.23743 5.485 4.03C5.485 3.765 5.5 3.68 5.5 3C5.5 2.32 5.5 2.235 5.485 1.97ZM5.035 4C5.03318 4.15869 5.00444 4.31593 4.95 4.465C4.91008 4.5738 4.84597 4.67211 4.7625 4.7525C4.68141 4.83513 4.58331 4.89911 4.475 4.94C4.32593 4.99444 4.16869 5.02318 4.01 5.025C3.76 5.0375 3.6675 5.04 3.01 5.04C2.3525 5.04 2.26 5.04 2.01 5.025C1.84522 5.02809 1.68115 5.00271 1.525 4.95C1.42145 4.90702 1.32784 4.8432 1.25 4.7625C1.16702 4.68219 1.10371 4.5838 1.065 4.475C1.00396 4.32379 0.970111 4.16299 0.965 4C0.965 3.75 0.95 3.6575 0.95 3C0.95 2.3425 0.95 2.25 0.965 2C0.966121 1.83776 0.995738 1.67699 1.0525 1.525C1.09651 1.41948 1.16407 1.32541 1.25 1.25C1.32595 1.16404 1.41982 1.09577 1.525 1.05C1.67739 0.995011 1.838 0.96627 2 0.965C2.25 0.965 2.3425 0.95 3 0.95C3.6575 0.95 3.75 0.95 4 0.965C4.15869 0.96682 4.31593 0.995562 4.465 1.05C4.57861 1.09216 4.68057 1.16071 4.7625 1.25C4.84443 1.32679 4.90844 1.42068 4.95 1.525C5.00556 1.67723 5.03432 1.83794 5.035 2C5.0475 2.25 5.05 2.3425 5.05 3C5.05 3.6575 5.0475 3.75 5.035 4ZM3 1.7175C2.74645 1.71799 2.49874 1.79363 2.28816 1.93485C2.07759 2.07608 1.91359 2.27655 1.81691 2.51094C1.72022 2.74533 1.69518 3.00312 1.74495 3.25173C1.79472 3.50035 1.91706 3.72863 2.09652 3.90774C2.27598 4.08686 2.5045 4.20875 2.75321 4.25804C3.00193 4.30732 3.25967 4.28178 3.49387 4.18463C3.72807 4.08749 3.92822 3.92311 4.06903 3.71226C4.20984 3.50141 4.285 3.25355 4.285 3C4.28533 2.83128 4.2523 2.66415 4.18781 2.50824C4.12332 2.35233 4.02864 2.2107 3.90922 2.09151C3.7898 1.97232 3.64799 1.87792 3.49195 1.81373C3.33591 1.74954 3.16872 1.71684 3 1.7175ZM3 3.8325C2.83535 3.8325 2.67439 3.78367 2.53749 3.6922C2.40058 3.60072 2.29388 3.4707 2.23087 3.31858C2.16786 3.16646 2.15137 2.99908 2.1835 2.83759C2.21562 2.6761 2.29491 2.52776 2.41133 2.41133C2.52776 2.29491 2.6761 2.21562 2.83759 2.1835C2.99908 2.15137 3.16646 2.16786 3.31858 2.23087C3.4707 2.29388 3.60072 2.40058 3.6922 2.53749C3.78367 2.67439 3.8325 2.83535 3.8325 3C3.8325 3.10933 3.81097 3.21758 3.76913 3.31858C3.72729 3.41959 3.66597 3.51136 3.58867 3.58867C3.51136 3.66597 3.41959 3.72729 3.31858 3.76913C3.21758 3.81097 3.10933 3.8325 3 3.8325Z"
                                  fill="url(#grad1)"
                                />
                              </g>
                              <defs>
                                <clippath
                                  id="clip0_3774_843"
                                >
                                  <rect
                                    fill="white"
                                    height="6"
                                    width="6"
                                  />
                                </clippath>
                              </defs>
                            </svg>
                          </svg>
                          <svg
                            class="w-5 h-5"
                            height="16"
                            viewBox="0 0 16 16"
                            width="16"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <circle
                              cx="8"
                              cy="8"
                              fill="#2476F1"
                              r="6"
                            />
                            <svg
                              fill="none"
                              height="13"
                              viewBox="-2 -3 8 10"
                              width="13"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <g
                                clip-path="url(#clip0_3748_803)"
                              >
                                <path
                                  d="M5.11739 0.499974H0.882393C0.834787 0.499313 0.787518 0.508036 0.743284 0.525645C0.69905 0.543254 0.658718 0.569404 0.624592 0.602602C0.590466 0.6358 0.563213 0.675395 0.544391 0.719126C0.525568 0.762858 0.515545 0.809868 0.514893 0.857474V5.14247C0.515545 5.19008 0.525568 5.23709 0.544391 5.28082C0.563213 5.32455 0.590466 5.36415 0.624592 5.39735C0.658718 5.43054 0.69905 5.45669 0.743284 5.4743C0.787518 5.49191 0.834787 5.50064 0.882393 5.49997H5.11739C5.165 5.50064 5.21227 5.49191 5.2565 5.4743C5.30073 5.45669 5.34107 5.43054 5.37519 5.39735C5.40932 5.36415 5.43657 5.32455 5.45539 5.28082C5.47422 5.23709 5.48424 5.19008 5.48489 5.14247V0.857474C5.48424 0.809868 5.47422 0.762858 5.45539 0.719126C5.43657 0.675395 5.40932 0.6358 5.37519 0.602602C5.34107 0.569404 5.30073 0.543254 5.2565 0.525645C5.21227 0.508036 5.165 0.499313 5.11739 0.499974ZM2.02239 4.68497H1.27239V2.43497H2.02239V4.68497ZM1.64739 2.11997C1.54396 2.11997 1.44476 2.07888 1.37162 2.00575C1.29848 1.93261 1.25739 1.83341 1.25739 1.72997C1.25739 1.62654 1.29848 1.52734 1.37162 1.4542C1.44476 1.38106 1.54396 1.33997 1.64739 1.33997C1.70232 1.33375 1.75794 1.33919 1.81061 1.35594C1.86329 1.3727 1.91183 1.4004 1.95306 1.43721C1.99429 1.47403 2.02727 1.51914 2.04986 1.56959C2.07245 1.62005 2.08412 1.6747 2.08412 1.72997C2.08412 1.78525 2.07245 1.8399 2.04986 1.89035C2.02727 1.9408 1.99429 1.98592 1.95306 2.02273C1.91183 2.05955 1.86329 2.08725 1.81061 2.104C1.75794 2.12076 1.70232 2.1262 1.64739 2.11997ZM4.72739 4.68497H3.97739V3.47747C3.97739 3.17497 3.86989 2.97747 3.59739 2.97747C3.51306 2.97809 3.43094 3.00454 3.3621 3.05327C3.29326 3.10199 3.24101 3.17064 3.21239 3.24997C3.19283 3.30873 3.18435 3.37062 3.18739 3.43247V4.68247H2.43739V2.43247H3.18739V2.74997C3.25553 2.63175 3.35462 2.53435 3.474 2.46827C3.59339 2.4022 3.72853 2.36994 3.86489 2.37497C4.36489 2.37497 4.72739 2.69747 4.72739 3.38997V4.68497Z"
                                  fill="white"
                                />
                              </g>
                              <defs>
                                <clippath
                                  id="clip0_3748_803"
                                >
                                  <rect
                                    class=" fill-current text-white"
                                    height="6"
                                    width="6"
                                  />
                                </clippath>
                              </defs>
                            </svg>
                          </svg>
                        </div>
                      </div>
                    </div>
                    <div
                      class="absolute -top-[0.525rem] -right-[0.675rem] shrink h-42 w-32"
                    />
                  </div>
                  <div
                    class="relative rounded-xl border-0 bg-white border-gray-100 p-3"
                  >
                    <img
                      class="rounded-[0.75rem] mb-2 w-full"
                      src=""
                    />
                    <div
                      class="flex flex-col gap-1"
                    >
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4 text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535M10.34 6.66a23.847 23.847 0 0 0 8.835-2.535m0 0A23.74 23.74 0 0 0 18.795 3m.38 1.125a23.91 23.91 0 0 1 1.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 0 0 1.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 0 1 0 3.46"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.3125rem]"
                        >
                          Quer ficar grandao?
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4 text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M13.5 21v-7.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349M3.75 21V9.349m0 0a3.001 3.001 0 0 0 3.75-.615A2.993 2.993 0 0 0 9.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 0 0 2.25 1.016c.896 0 1.7-.393 2.25-1.015a3.001 3.001 0 0 0 3.75.614m-16.5 0a3.004 3.004 0 0 1-.621-4.72l1.189-1.19A1.5 1.5 0 0 1 5.378 3h13.243a1.5 1.5 0 0 1 1.06.44l1.19 1.189a3 3 0 0 1-.621 4.72M6.75 18h3.75a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75H6.75a.75.75 0 0 0-.75.75v3.75c0 .414.336.75.75.75Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]"
                        >
                          Eric Personal
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4  text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]"
                        >
                          Finalizada
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4 text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]"
                        >
                          10/12/2023 
                          campaign-list.cards.preposition
                           10/02/2024
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <div
                          class="flex gap-1"
                        >
                          <svg
                            aria-hidden="true"
                            class="w-4 h-4 mt-1 mr-1 text-gray-800"
                            data-slot="icon"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="1.5"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M7.217 10.907a2.25 2.25 0 1 0 0 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186 9.566-5.314m-9.566 7.5 9.566 5.314m0 0a2.25 2.25 0 1 0 3.935 2.186 2.25 2.25 0 0 0-3.935-2.186Zm0-12.814a2.25 2.25 0 1 0 3.933-2.185 2.25 2.25 0 0 0-3.933 2.185Z"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                          <svg
                            class="w-5 h-5"
                            height="16"
                            viewBox="0 0 16 16"
                            width="16"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <circle
                              cx="8"
                              cy="8"
                              fill="#2476F1"
                              r="6"
                            />
                            <svg
                              class="text-white"
                              viewBox="-1 -1 10 8"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                class="fill-current"
                                d="M4.75958 1.33001H5.22958V0.535007C5.00202 0.511344 4.77337 0.499661 4.54458 0.500007C3.86458 0.500007 3.39958 0.915007 3.39958 1.67501V2.33001H2.63208V3.22001H3.39958V5.50001H4.31958V3.22001H5.08458L5.19958 2.33001H4.31958V1.76251C4.31958 1.50001 4.38958 1.33001 4.75958 1.33001Z"
                              />
                            </svg>
                          </svg>
                          <svg
                            class="w-5 h-5"
                            height="16"
                            viewBox="0 0 16 16"
                            width="16"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <defs>
                              <lineargradient
                                id="b"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="100%"
                              >
                                <stop
                                  offset="0"
                                  stop-color="#3771c8"
                                />
                                <stop
                                  offset=".128"
                                  stop-color="#3771c8"
                                />
                                <stop
                                  offset="1"
                                  stop-color="#60f"
                                  stop-opacity="0"
                                />
                              </lineargradient>
                              <lineargradient
                                id="a"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="100%"
                              >
                                <stop
                                  offset="0"
                                  stop-color="#fd5"
                                />
                                <stop
                                  offset=".1"
                                  stop-color="#fd5"
                                />
                                <stop
                                  offset=".5"
                                  stop-color="#ff543e"
                                />
                                <stop
                                  offset="1"
                                  stop-color="#c837ab"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="8"
                              cy="8"
                              fill="url(#b)"
                              r="6"
                            />
                            <circle
                              cx="8"
                              cy="8"
                              fill="url(#a)"
                              r="6"
                            />
                            <svg
                              class="text-white"
                              height="13"
                              viewBox="-3 -2 10 8"
                              width="13"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <defs>
                                <lineargradient
                                  id="grad1"
                                  x1="0%"
                                  x2="100%"
                                  y1="0%"
                                  y2="0%"
                                >
                                  <stop
                                    offset="0%"
                                  />
                                  <stop
                                    offset="100%"
                                  />
                                </lineargradient>
                              </defs>
                              <g
                                clip-path="url(#clip0_3774_843)"
                              >
                                <path
                                  class="fill-current"
                                  d="M4.335 1.365C4.27567 1.365 4.21766 1.38259 4.16833 1.41556C4.11899 1.44852 4.08054 1.49538 4.05784 1.55019C4.03513 1.60501 4.02919 1.66533 4.04076 1.72353C4.05234 1.78172 4.08091 1.83518 4.12287 1.87713C4.16482 1.91909 4.21828 1.94766 4.27647 1.95924C4.33467 1.97081 4.39499 1.96487 4.44981 1.94216C4.50462 1.91946 4.55148 1.88101 4.58444 1.83167C4.6174 1.78234 4.635 1.72433 4.635 1.665C4.635 1.58544 4.60339 1.50913 4.54713 1.45287C4.49087 1.39661 4.41457 1.365 4.335 1.365ZM5.485 1.97C5.48014 1.76257 5.44129 1.55735 5.37 1.3625C5.30643 1.19578 5.2075 1.04482 5.08 0.92C4.95621 0.791856 4.80489 0.693544 4.6375 0.6325C4.44316 0.55904 4.23771 0.519302 4.03 0.515C3.765 0.5 3.68 0.5 3 0.5C2.32 0.5 2.235 0.5 1.97 0.515C1.76229 0.519302 1.55684 0.55904 1.3625 0.6325C1.19542 0.694162 1.04423 0.79239 0.92 0.92C0.791856 1.04379 0.693544 1.19511 0.6325 1.3625C0.55904 1.55684 0.519302 1.76229 0.515 1.97C0.5 2.235 0.5 2.32 0.5 3C0.5 3.68 0.5 3.765 0.515 4.03C0.519302 4.23771 0.55904 4.44316 0.6325 4.6375C0.693544 4.80489 0.791856 4.95621 0.92 5.08C1.04423 5.20761 1.19542 5.30584 1.3625 5.3675C1.55684 5.44096 1.76229 5.4807 1.97 5.485C2.235 5.5 2.32 5.5 3 5.5C3.68 5.5 3.765 5.5 4.03 5.485C4.23771 5.4807 4.44316 5.44096 4.6375 5.3675C4.80489 5.30646 4.95621 5.20814 5.08 5.08C5.20806 4.95565 5.30708 4.80455 5.37 4.6375C5.44129 4.44265 5.48014 4.23743 5.485 4.03C5.485 3.765 5.5 3.68 5.5 3C5.5 2.32 5.5 2.235 5.485 1.97ZM5.035 4C5.03318 4.15869 5.00444 4.31593 4.95 4.465C4.91008 4.5738 4.84597 4.67211 4.7625 4.7525C4.68141 4.83513 4.58331 4.89911 4.475 4.94C4.32593 4.99444 4.16869 5.02318 4.01 5.025C3.76 5.0375 3.6675 5.04 3.01 5.04C2.3525 5.04 2.26 5.04 2.01 5.025C1.84522 5.02809 1.68115 5.00271 1.525 4.95C1.42145 4.90702 1.32784 4.8432 1.25 4.7625C1.16702 4.68219 1.10371 4.5838 1.065 4.475C1.00396 4.32379 0.970111 4.16299 0.965 4C0.965 3.75 0.95 3.6575 0.95 3C0.95 2.3425 0.95 2.25 0.965 2C0.966121 1.83776 0.995738 1.67699 1.0525 1.525C1.09651 1.41948 1.16407 1.32541 1.25 1.25C1.32595 1.16404 1.41982 1.09577 1.525 1.05C1.67739 0.995011 1.838 0.96627 2 0.965C2.25 0.965 2.3425 0.95 3 0.95C3.6575 0.95 3.75 0.95 4 0.965C4.15869 0.96682 4.31593 0.995562 4.465 1.05C4.57861 1.09216 4.68057 1.16071 4.7625 1.25C4.84443 1.32679 4.90844 1.42068 4.95 1.525C5.00556 1.67723 5.03432 1.83794 5.035 2C5.0475 2.25 5.05 2.3425 5.05 3C5.05 3.6575 5.0475 3.75 5.035 4ZM3 1.7175C2.74645 1.71799 2.49874 1.79363 2.28816 1.93485C2.07759 2.07608 1.91359 2.27655 1.81691 2.51094C1.72022 2.74533 1.69518 3.00312 1.74495 3.25173C1.79472 3.50035 1.91706 3.72863 2.09652 3.90774C2.27598 4.08686 2.5045 4.20875 2.75321 4.25804C3.00193 4.30732 3.25967 4.28178 3.49387 4.18463C3.72807 4.08749 3.92822 3.92311 4.06903 3.71226C4.20984 3.50141 4.285 3.25355 4.285 3C4.28533 2.83128 4.2523 2.66415 4.18781 2.50824C4.12332 2.35233 4.02864 2.2107 3.90922 2.09151C3.7898 1.97232 3.64799 1.87792 3.49195 1.81373C3.33591 1.74954 3.16872 1.71684 3 1.7175ZM3 3.8325C2.83535 3.8325 2.67439 3.78367 2.53749 3.6922C2.40058 3.60072 2.29388 3.4707 2.23087 3.31858C2.16786 3.16646 2.15137 2.99908 2.1835 2.83759C2.21562 2.6761 2.29491 2.52776 2.41133 2.41133C2.52776 2.29491 2.6761 2.21562 2.83759 2.1835C2.99908 2.15137 3.16646 2.16786 3.31858 2.23087C3.4707 2.29388 3.60072 2.40058 3.6922 2.53749C3.78367 2.67439 3.8325 2.83535 3.8325 3C3.8325 3.10933 3.81097 3.21758 3.76913 3.31858C3.72729 3.41959 3.66597 3.51136 3.58867 3.58867C3.51136 3.66597 3.41959 3.72729 3.31858 3.76913C3.21758 3.81097 3.10933 3.8325 3 3.8325Z"
                                  fill="url(#grad1)"
                                />
                              </g>
                              <defs>
                                <clippath
                                  id="clip0_3774_843"
                                >
                                  <rect
                                    fill="white"
                                    height="6"
                                    width="6"
                                  />
                                </clippath>
                              </defs>
                            </svg>
                          </svg>
                          <svg
                            class="w-5 h-5"
                            height="16"
                            viewBox="0 0 16 16"
                            width="16"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <circle
                              cx="8"
                              cy="8"
                              fill="#2476F1"
                              r="6"
                            />
                            <svg
                              fill="none"
                              height="13"
                              viewBox="-2 -3 8 10"
                              width="13"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <g
                                clip-path="url(#clip0_3748_803)"
                              >
                                <path
                                  d="M5.11739 0.499974H0.882393C0.834787 0.499313 0.787518 0.508036 0.743284 0.525645C0.69905 0.543254 0.658718 0.569404 0.624592 0.602602C0.590466 0.6358 0.563213 0.675395 0.544391 0.719126C0.525568 0.762858 0.515545 0.809868 0.514893 0.857474V5.14247C0.515545 5.19008 0.525568 5.23709 0.544391 5.28082C0.563213 5.32455 0.590466 5.36415 0.624592 5.39735C0.658718 5.43054 0.69905 5.45669 0.743284 5.4743C0.787518 5.49191 0.834787 5.50064 0.882393 5.49997H5.11739C5.165 5.50064 5.21227 5.49191 5.2565 5.4743C5.30073 5.45669 5.34107 5.43054 5.37519 5.39735C5.40932 5.36415 5.43657 5.32455 5.45539 5.28082C5.47422 5.23709 5.48424 5.19008 5.48489 5.14247V0.857474C5.48424 0.809868 5.47422 0.762858 5.45539 0.719126C5.43657 0.675395 5.40932 0.6358 5.37519 0.602602C5.34107 0.569404 5.30073 0.543254 5.2565 0.525645C5.21227 0.508036 5.165 0.499313 5.11739 0.499974ZM2.02239 4.68497H1.27239V2.43497H2.02239V4.68497ZM1.64739 2.11997C1.54396 2.11997 1.44476 2.07888 1.37162 2.00575C1.29848 1.93261 1.25739 1.83341 1.25739 1.72997C1.25739 1.62654 1.29848 1.52734 1.37162 1.4542C1.44476 1.38106 1.54396 1.33997 1.64739 1.33997C1.70232 1.33375 1.75794 1.33919 1.81061 1.35594C1.86329 1.3727 1.91183 1.4004 1.95306 1.43721C1.99429 1.47403 2.02727 1.51914 2.04986 1.56959C2.07245 1.62005 2.08412 1.6747 2.08412 1.72997C2.08412 1.78525 2.07245 1.8399 2.04986 1.89035C2.02727 1.9408 1.99429 1.98592 1.95306 2.02273C1.91183 2.05955 1.86329 2.08725 1.81061 2.104C1.75794 2.12076 1.70232 2.1262 1.64739 2.11997ZM4.72739 4.68497H3.97739V3.47747C3.97739 3.17497 3.86989 2.97747 3.59739 2.97747C3.51306 2.97809 3.43094 3.00454 3.3621 3.05327C3.29326 3.10199 3.24101 3.17064 3.21239 3.24997C3.19283 3.30873 3.18435 3.37062 3.18739 3.43247V4.68247H2.43739V2.43247H3.18739V2.74997C3.25553 2.63175 3.35462 2.53435 3.474 2.46827C3.59339 2.4022 3.72853 2.36994 3.86489 2.37497C4.36489 2.37497 4.72739 2.69747 4.72739 3.38997V4.68497Z"
                                  fill="white"
                                />
                              </g>
                              <defs>
                                <clippath
                                  id="clip0_3748_803"
                                >
                                  <rect
                                    class=" fill-current text-white"
                                    height="6"
                                    width="6"
                                  />
                                </clippath>
                              </defs>
                            </svg>
                          </svg>
                        </div>
                      </div>
                    </div>
                    <div
                      class="absolute -top-[0.525rem] -right-[0.675rem] shrink h-42 w-32"
                    />
                  </div>
                  <div
                    class="relative rounded-xl border-0 bg-white border-gray-100 p-3"
                  >
                    <img
                      class="rounded-[0.75rem] mb-2 w-full"
                      src=""
                    />
                    <div
                      class="flex flex-col gap-1"
                    >
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4 text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535M10.34 6.66a23.847 23.847 0 0 0 8.835-2.535m0 0A23.74 23.74 0 0 0 18.795 3m.38 1.125a23.91 23.91 0 0 1 1.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 0 0 1.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 0 1 0 3.46"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.3125rem]"
                        >
                          Quer ficar grandao?
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4 text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M13.5 21v-7.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349M3.75 21V9.349m0 0a3.001 3.001 0 0 0 3.75-.615A2.993 2.993 0 0 0 9.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 0 0 2.25 1.016c.896 0 1.7-.393 2.25-1.015a3.001 3.001 0 0 0 3.75.614m-16.5 0a3.004 3.004 0 0 1-.621-4.72l1.189-1.19A1.5 1.5 0 0 1 5.378 3h13.243a1.5 1.5 0 0 1 1.06.44l1.19 1.189a3 3 0 0 1-.621 4.72M6.75 18h3.75a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75H6.75a.75.75 0 0 0-.75.75v3.75c0 .414.336.75.75.75Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]"
                        >
                          Eric Personal
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4  text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]"
                        >
                          Finalizada
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4 text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]"
                        >
                          10/12/2023 
                          campaign-list.cards.preposition
                           10/02/2024
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <div
                          class="flex gap-1"
                        >
                          <svg
                            aria-hidden="true"
                            class="w-4 h-4 mt-1 mr-1 text-gray-800"
                            data-slot="icon"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="1.5"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M7.217 10.907a2.25 2.25 0 1 0 0 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186 9.566-5.314m-9.566 7.5 9.566 5.314m0 0a2.25 2.25 0 1 0 3.935 2.186 2.25 2.25 0 0 0-3.935-2.186Zm0-12.814a2.25 2.25 0 1 0 3.933-2.185 2.25 2.25 0 0 0-3.933 2.185Z"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                          <svg
                            class="w-5 h-5"
                            height="16"
                            viewBox="0 0 16 16"
                            width="16"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <circle
                              cx="8"
                              cy="8"
                              fill="#2476F1"
                              r="6"
                            />
                            <svg
                              class="text-white"
                              viewBox="-1 -1 10 8"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                class="fill-current"
                                d="M4.75958 1.33001H5.22958V0.535007C5.00202 0.511344 4.77337 0.499661 4.54458 0.500007C3.86458 0.500007 3.39958 0.915007 3.39958 1.67501V2.33001H2.63208V3.22001H3.39958V5.50001H4.31958V3.22001H5.08458L5.19958 2.33001H4.31958V1.76251C4.31958 1.50001 4.38958 1.33001 4.75958 1.33001Z"
                              />
                            </svg>
                          </svg>
                          <svg
                            class="w-5 h-5"
                            height="16"
                            viewBox="0 0 16 16"
                            width="16"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <defs>
                              <lineargradient
                                id="b"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="100%"
                              >
                                <stop
                                  offset="0"
                                  stop-color="#3771c8"
                                />
                                <stop
                                  offset=".128"
                                  stop-color="#3771c8"
                                />
                                <stop
                                  offset="1"
                                  stop-color="#60f"
                                  stop-opacity="0"
                                />
                              </lineargradient>
                              <lineargradient
                                id="a"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="100%"
                              >
                                <stop
                                  offset="0"
                                  stop-color="#fd5"
                                />
                                <stop
                                  offset=".1"
                                  stop-color="#fd5"
                                />
                                <stop
                                  offset=".5"
                                  stop-color="#ff543e"
                                />
                                <stop
                                  offset="1"
                                  stop-color="#c837ab"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="8"
                              cy="8"
                              fill="url(#b)"
                              r="6"
                            />
                            <circle
                              cx="8"
                              cy="8"
                              fill="url(#a)"
                              r="6"
                            />
                            <svg
                              class="text-white"
                              height="13"
                              viewBox="-3 -2 10 8"
                              width="13"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <defs>
                                <lineargradient
                                  id="grad1"
                                  x1="0%"
                                  x2="100%"
                                  y1="0%"
                                  y2="0%"
                                >
                                  <stop
                                    offset="0%"
                                  />
                                  <stop
                                    offset="100%"
                                  />
                                </lineargradient>
                              </defs>
                              <g
                                clip-path="url(#clip0_3774_843)"
                              >
                                <path
                                  class="fill-current"
                                  d="M4.335 1.365C4.27567 1.365 4.21766 1.38259 4.16833 1.41556C4.11899 1.44852 4.08054 1.49538 4.05784 1.55019C4.03513 1.60501 4.02919 1.66533 4.04076 1.72353C4.05234 1.78172 4.08091 1.83518 4.12287 1.87713C4.16482 1.91909 4.21828 1.94766 4.27647 1.95924C4.33467 1.97081 4.39499 1.96487 4.44981 1.94216C4.50462 1.91946 4.55148 1.88101 4.58444 1.83167C4.6174 1.78234 4.635 1.72433 4.635 1.665C4.635 1.58544 4.60339 1.50913 4.54713 1.45287C4.49087 1.39661 4.41457 1.365 4.335 1.365ZM5.485 1.97C5.48014 1.76257 5.44129 1.55735 5.37 1.3625C5.30643 1.19578 5.2075 1.04482 5.08 0.92C4.95621 0.791856 4.80489 0.693544 4.6375 0.6325C4.44316 0.55904 4.23771 0.519302 4.03 0.515C3.765 0.5 3.68 0.5 3 0.5C2.32 0.5 2.235 0.5 1.97 0.515C1.76229 0.519302 1.55684 0.55904 1.3625 0.6325C1.19542 0.694162 1.04423 0.79239 0.92 0.92C0.791856 1.04379 0.693544 1.19511 0.6325 1.3625C0.55904 1.55684 0.519302 1.76229 0.515 1.97C0.5 2.235 0.5 2.32 0.5 3C0.5 3.68 0.5 3.765 0.515 4.03C0.519302 4.23771 0.55904 4.44316 0.6325 4.6375C0.693544 4.80489 0.791856 4.95621 0.92 5.08C1.04423 5.20761 1.19542 5.30584 1.3625 5.3675C1.55684 5.44096 1.76229 5.4807 1.97 5.485C2.235 5.5 2.32 5.5 3 5.5C3.68 5.5 3.765 5.5 4.03 5.485C4.23771 5.4807 4.44316 5.44096 4.6375 5.3675C4.80489 5.30646 4.95621 5.20814 5.08 5.08C5.20806 4.95565 5.30708 4.80455 5.37 4.6375C5.44129 4.44265 5.48014 4.23743 5.485 4.03C5.485 3.765 5.5 3.68 5.5 3C5.5 2.32 5.5 2.235 5.485 1.97ZM5.035 4C5.03318 4.15869 5.00444 4.31593 4.95 4.465C4.91008 4.5738 4.84597 4.67211 4.7625 4.7525C4.68141 4.83513 4.58331 4.89911 4.475 4.94C4.32593 4.99444 4.16869 5.02318 4.01 5.025C3.76 5.0375 3.6675 5.04 3.01 5.04C2.3525 5.04 2.26 5.04 2.01 5.025C1.84522 5.02809 1.68115 5.00271 1.525 4.95C1.42145 4.90702 1.32784 4.8432 1.25 4.7625C1.16702 4.68219 1.10371 4.5838 1.065 4.475C1.00396 4.32379 0.970111 4.16299 0.965 4C0.965 3.75 0.95 3.6575 0.95 3C0.95 2.3425 0.95 2.25 0.965 2C0.966121 1.83776 0.995738 1.67699 1.0525 1.525C1.09651 1.41948 1.16407 1.32541 1.25 1.25C1.32595 1.16404 1.41982 1.09577 1.525 1.05C1.67739 0.995011 1.838 0.96627 2 0.965C2.25 0.965 2.3425 0.95 3 0.95C3.6575 0.95 3.75 0.95 4 0.965C4.15869 0.96682 4.31593 0.995562 4.465 1.05C4.57861 1.09216 4.68057 1.16071 4.7625 1.25C4.84443 1.32679 4.90844 1.42068 4.95 1.525C5.00556 1.67723 5.03432 1.83794 5.035 2C5.0475 2.25 5.05 2.3425 5.05 3C5.05 3.6575 5.0475 3.75 5.035 4ZM3 1.7175C2.74645 1.71799 2.49874 1.79363 2.28816 1.93485C2.07759 2.07608 1.91359 2.27655 1.81691 2.51094C1.72022 2.74533 1.69518 3.00312 1.74495 3.25173C1.79472 3.50035 1.91706 3.72863 2.09652 3.90774C2.27598 4.08686 2.5045 4.20875 2.75321 4.25804C3.00193 4.30732 3.25967 4.28178 3.49387 4.18463C3.72807 4.08749 3.92822 3.92311 4.06903 3.71226C4.20984 3.50141 4.285 3.25355 4.285 3C4.28533 2.83128 4.2523 2.66415 4.18781 2.50824C4.12332 2.35233 4.02864 2.2107 3.90922 2.09151C3.7898 1.97232 3.64799 1.87792 3.49195 1.81373C3.33591 1.74954 3.16872 1.71684 3 1.7175ZM3 3.8325C2.83535 3.8325 2.67439 3.78367 2.53749 3.6922C2.40058 3.60072 2.29388 3.4707 2.23087 3.31858C2.16786 3.16646 2.15137 2.99908 2.1835 2.83759C2.21562 2.6761 2.29491 2.52776 2.41133 2.41133C2.52776 2.29491 2.6761 2.21562 2.83759 2.1835C2.99908 2.15137 3.16646 2.16786 3.31858 2.23087C3.4707 2.29388 3.60072 2.40058 3.6922 2.53749C3.78367 2.67439 3.8325 2.83535 3.8325 3C3.8325 3.10933 3.81097 3.21758 3.76913 3.31858C3.72729 3.41959 3.66597 3.51136 3.58867 3.58867C3.51136 3.66597 3.41959 3.72729 3.31858 3.76913C3.21758 3.81097 3.10933 3.8325 3 3.8325Z"
                                  fill="url(#grad1)"
                                />
                              </g>
                              <defs>
                                <clippath
                                  id="clip0_3774_843"
                                >
                                  <rect
                                    fill="white"
                                    height="6"
                                    width="6"
                                  />
                                </clippath>
                              </defs>
                            </svg>
                          </svg>
                          <svg
                            class="w-5 h-5"
                            height="16"
                            viewBox="0 0 16 16"
                            width="16"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <circle
                              cx="8"
                              cy="8"
                              fill="#2476F1"
                              r="6"
                            />
                            <svg
                              fill="none"
                              height="13"
                              viewBox="-2 -3 8 10"
                              width="13"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <g
                                clip-path="url(#clip0_3748_803)"
                              >
                                <path
                                  d="M5.11739 0.499974H0.882393C0.834787 0.499313 0.787518 0.508036 0.743284 0.525645C0.69905 0.543254 0.658718 0.569404 0.624592 0.602602C0.590466 0.6358 0.563213 0.675395 0.544391 0.719126C0.525568 0.762858 0.515545 0.809868 0.514893 0.857474V5.14247C0.515545 5.19008 0.525568 5.23709 0.544391 5.28082C0.563213 5.32455 0.590466 5.36415 0.624592 5.39735C0.658718 5.43054 0.69905 5.45669 0.743284 5.4743C0.787518 5.49191 0.834787 5.50064 0.882393 5.49997H5.11739C5.165 5.50064 5.21227 5.49191 5.2565 5.4743C5.30073 5.45669 5.34107 5.43054 5.37519 5.39735C5.40932 5.36415 5.43657 5.32455 5.45539 5.28082C5.47422 5.23709 5.48424 5.19008 5.48489 5.14247V0.857474C5.48424 0.809868 5.47422 0.762858 5.45539 0.719126C5.43657 0.675395 5.40932 0.6358 5.37519 0.602602C5.34107 0.569404 5.30073 0.543254 5.2565 0.525645C5.21227 0.508036 5.165 0.499313 5.11739 0.499974ZM2.02239 4.68497H1.27239V2.43497H2.02239V4.68497ZM1.64739 2.11997C1.54396 2.11997 1.44476 2.07888 1.37162 2.00575C1.29848 1.93261 1.25739 1.83341 1.25739 1.72997C1.25739 1.62654 1.29848 1.52734 1.37162 1.4542C1.44476 1.38106 1.54396 1.33997 1.64739 1.33997C1.70232 1.33375 1.75794 1.33919 1.81061 1.35594C1.86329 1.3727 1.91183 1.4004 1.95306 1.43721C1.99429 1.47403 2.02727 1.51914 2.04986 1.56959C2.07245 1.62005 2.08412 1.6747 2.08412 1.72997C2.08412 1.78525 2.07245 1.8399 2.04986 1.89035C2.02727 1.9408 1.99429 1.98592 1.95306 2.02273C1.91183 2.05955 1.86329 2.08725 1.81061 2.104C1.75794 2.12076 1.70232 2.1262 1.64739 2.11997ZM4.72739 4.68497H3.97739V3.47747C3.97739 3.17497 3.86989 2.97747 3.59739 2.97747C3.51306 2.97809 3.43094 3.00454 3.3621 3.05327C3.29326 3.10199 3.24101 3.17064 3.21239 3.24997C3.19283 3.30873 3.18435 3.37062 3.18739 3.43247V4.68247H2.43739V2.43247H3.18739V2.74997C3.25553 2.63175 3.35462 2.53435 3.474 2.46827C3.59339 2.4022 3.72853 2.36994 3.86489 2.37497C4.36489 2.37497 4.72739 2.69747 4.72739 3.38997V4.68497Z"
                                  fill="white"
                                />
                              </g>
                              <defs>
                                <clippath
                                  id="clip0_3748_803"
                                >
                                  <rect
                                    class=" fill-current text-white"
                                    height="6"
                                    width="6"
                                  />
                                </clippath>
                              </defs>
                            </svg>
                          </svg>
                        </div>
                      </div>
                    </div>
                    <div
                      class="absolute -top-[0.525rem] -right-[0.675rem] shrink h-42 w-32"
                    />
                  </div>
                  <div
                    class="relative rounded-xl border-0 bg-white border-gray-100 p-3"
                  >
                    <img
                      class="rounded-[0.75rem] mb-2 w-full"
                      src=""
                    />
                    <div
                      class="flex flex-col gap-1"
                    >
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4 text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535M10.34 6.66a23.847 23.847 0 0 0 8.835-2.535m0 0A23.74 23.74 0 0 0 18.795 3m.38 1.125a23.91 23.91 0 0 1 1.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 0 0 1.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 0 1 0 3.46"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.3125rem]"
                        >
                          Quer ficar grandao?
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4 text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M13.5 21v-7.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349M3.75 21V9.349m0 0a3.001 3.001 0 0 0 3.75-.615A2.993 2.993 0 0 0 9.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 0 0 2.25 1.016c.896 0 1.7-.393 2.25-1.015a3.001 3.001 0 0 0 3.75.614m-16.5 0a3.004 3.004 0 0 1-.621-4.72l1.189-1.19A1.5 1.5 0 0 1 5.378 3h13.243a1.5 1.5 0 0 1 1.06.44l1.19 1.189a3 3 0 0 1-.621 4.72M6.75 18h3.75a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75H6.75a.75.75 0 0 0-.75.75v3.75c0 .414.336.75.75.75Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]"
                        >
                          Eric Personal
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4  text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]"
                        >
                          Finalizada
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <svg
                          aria-hidden="true"
                          class="w-4 h-4 text-gray-800"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p
                          class="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]"
                        >
                          10/12/2023 
                          campaign-list.cards.preposition
                           10/02/2024
                        </p>
                      </div>
                      <div
                        class="flex flex-row items-center gap-1"
                      >
                        <div
                          class="flex gap-1"
                        >
                          <svg
                            aria-hidden="true"
                            class="w-4 h-4 mt-1 mr-1 text-gray-800"
                            data-slot="icon"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="1.5"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M7.217 10.907a2.25 2.25 0 1 0 0 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186 9.566-5.314m-9.566 7.5 9.566 5.314m0 0a2.25 2.25 0 1 0 3.935 2.186 2.25 2.25 0 0 0-3.935-2.186Zm0-12.814a2.25 2.25 0 1 0 3.933-2.185 2.25 2.25 0 0 0-3.933 2.185Z"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                          <svg
                            class="w-5 h-5"
                            height="16"
                            viewBox="0 0 16 16"
                            width="16"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <circle
                              cx="8"
                              cy="8"
                              fill="#2476F1"
                              r="6"
                            />
                            <svg
                              class="text-white"
                              viewBox="-1 -1 10 8"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                class="fill-current"
                                d="M4.75958 1.33001H5.22958V0.535007C5.00202 0.511344 4.77337 0.499661 4.54458 0.500007C3.86458 0.500007 3.39958 0.915007 3.39958 1.67501V2.33001H2.63208V3.22001H3.39958V5.50001H4.31958V3.22001H5.08458L5.19958 2.33001H4.31958V1.76251C4.31958 1.50001 4.38958 1.33001 4.75958 1.33001Z"
                              />
                            </svg>
                          </svg>
                          <svg
                            class="w-5 h-5"
                            height="16"
                            viewBox="0 0 16 16"
                            width="16"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <defs>
                              <lineargradient
                                id="b"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="100%"
                              >
                                <stop
                                  offset="0"
                                  stop-color="#3771c8"
                                />
                                <stop
                                  offset=".128"
                                  stop-color="#3771c8"
                                />
                                <stop
                                  offset="1"
                                  stop-color="#60f"
                                  stop-opacity="0"
                                />
                              </lineargradient>
                              <lineargradient
                                id="a"
                                x1="0%"
                                x2="100%"
                                y1="0%"
                                y2="100%"
                              >
                                <stop
                                  offset="0"
                                  stop-color="#fd5"
                                />
                                <stop
                                  offset=".1"
                                  stop-color="#fd5"
                                />
                                <stop
                                  offset=".5"
                                  stop-color="#ff543e"
                                />
                                <stop
                                  offset="1"
                                  stop-color="#c837ab"
                                />
                              </lineargradient>
                            </defs>
                            <circle
                              cx="8"
                              cy="8"
                              fill="url(#b)"
                              r="6"
                            />
                            <circle
                              cx="8"
                              cy="8"
                              fill="url(#a)"
                              r="6"
                            />
                            <svg
                              class="text-white"
                              height="13"
                              viewBox="-3 -2 10 8"
                              width="13"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <defs>
                                <lineargradient
                                  id="grad1"
                                  x1="0%"
                                  x2="100%"
                                  y1="0%"
                                  y2="0%"
                                >
                                  <stop
                                    offset="0%"
                                  />
                                  <stop
                                    offset="100%"
                                  />
                                </lineargradient>
                              </defs>
                              <g
                                clip-path="url(#clip0_3774_843)"
                              >
                                <path
                                  class="fill-current"
                                  d="M4.335 1.365C4.27567 1.365 4.21766 1.38259 4.16833 1.41556C4.11899 1.44852 4.08054 1.49538 4.05784 1.55019C4.03513 1.60501 4.02919 1.66533 4.04076 1.72353C4.05234 1.78172 4.08091 1.83518 4.12287 1.87713C4.16482 1.91909 4.21828 1.94766 4.27647 1.95924C4.33467 1.97081 4.39499 1.96487 4.44981 1.94216C4.50462 1.91946 4.55148 1.88101 4.58444 1.83167C4.6174 1.78234 4.635 1.72433 4.635 1.665C4.635 1.58544 4.60339 1.50913 4.54713 1.45287C4.49087 1.39661 4.41457 1.365 4.335 1.365ZM5.485 1.97C5.48014 1.76257 5.44129 1.55735 5.37 1.3625C5.30643 1.19578 5.2075 1.04482 5.08 0.92C4.95621 0.791856 4.80489 0.693544 4.6375 0.6325C4.44316 0.55904 4.23771 0.519302 4.03 0.515C3.765 0.5 3.68 0.5 3 0.5C2.32 0.5 2.235 0.5 1.97 0.515C1.76229 0.519302 1.55684 0.55904 1.3625 0.6325C1.19542 0.694162 1.04423 0.79239 0.92 0.92C0.791856 1.04379 0.693544 1.19511 0.6325 1.3625C0.55904 1.55684 0.519302 1.76229 0.515 1.97C0.5 2.235 0.5 2.32 0.5 3C0.5 3.68 0.5 3.765 0.515 4.03C0.519302 4.23771 0.55904 4.44316 0.6325 4.6375C0.693544 4.80489 0.791856 4.95621 0.92 5.08C1.04423 5.20761 1.19542 5.30584 1.3625 5.3675C1.55684 5.44096 1.76229 5.4807 1.97 5.485C2.235 5.5 2.32 5.5 3 5.5C3.68 5.5 3.765 5.5 4.03 5.485C4.23771 5.4807 4.44316 5.44096 4.6375 5.3675C4.80489 5.30646 4.95621 5.20814 5.08 5.08C5.20806 4.95565 5.30708 4.80455 5.37 4.6375C5.44129 4.44265 5.48014 4.23743 5.485 4.03C5.485 3.765 5.5 3.68 5.5 3C5.5 2.32 5.5 2.235 5.485 1.97ZM5.035 4C5.03318 4.15869 5.00444 4.31593 4.95 4.465C4.91008 4.5738 4.84597 4.67211 4.7625 4.7525C4.68141 4.83513 4.58331 4.89911 4.475 4.94C4.32593 4.99444 4.16869 5.02318 4.01 5.025C3.76 5.0375 3.6675 5.04 3.01 5.04C2.3525 5.04 2.26 5.04 2.01 5.025C1.84522 5.02809 1.68115 5.00271 1.525 4.95C1.42145 4.90702 1.32784 4.8432 1.25 4.7625C1.16702 4.68219 1.10371 4.5838 1.065 4.475C1.00396 4.32379 0.970111 4.16299 0.965 4C0.965 3.75 0.95 3.6575 0.95 3C0.95 2.3425 0.95 2.25 0.965 2C0.966121 1.83776 0.995738 1.67699 1.0525 1.525C1.09651 1.41948 1.16407 1.32541 1.25 1.25C1.32595 1.16404 1.41982 1.09577 1.525 1.05C1.67739 0.995011 1.838 0.96627 2 0.965C2.25 0.965 2.3425 0.95 3 0.95C3.6575 0.95 3.75 0.95 4 0.965C4.15869 0.96682 4.31593 0.995562 4.465 1.05C4.57861 1.09216 4.68057 1.16071 4.7625 1.25C4.84443 1.32679 4.90844 1.42068 4.95 1.525C5.00556 1.67723 5.03432 1.83794 5.035 2C5.0475 2.25 5.05 2.3425 5.05 3C5.05 3.6575 5.0475 3.75 5.035 4ZM3 1.7175C2.74645 1.71799 2.49874 1.79363 2.28816 1.93485C2.07759 2.07608 1.91359 2.27655 1.81691 2.51094C1.72022 2.74533 1.69518 3.00312 1.74495 3.25173C1.79472 3.50035 1.91706 3.72863 2.09652 3.90774C2.27598 4.08686 2.5045 4.20875 2.75321 4.25804C3.00193 4.30732 3.25967 4.28178 3.49387 4.18463C3.72807 4.08749 3.92822 3.92311 4.06903 3.71226C4.20984 3.50141 4.285 3.25355 4.285 3C4.28533 2.83128 4.2523 2.66415 4.18781 2.50824C4.12332 2.35233 4.02864 2.2107 3.90922 2.09151C3.7898 1.97232 3.64799 1.87792 3.49195 1.81373C3.33591 1.74954 3.16872 1.71684 3 1.7175ZM3 3.8325C2.83535 3.8325 2.67439 3.78367 2.53749 3.6922C2.40058 3.60072 2.29388 3.4707 2.23087 3.31858C2.16786 3.16646 2.15137 2.99908 2.1835 2.83759C2.21562 2.6761 2.29491 2.52776 2.41133 2.41133C2.52776 2.29491 2.6761 2.21562 2.83759 2.1835C2.99908 2.15137 3.16646 2.16786 3.31858 2.23087C3.4707 2.29388 3.60072 2.40058 3.6922 2.53749C3.78367 2.67439 3.8325 2.83535 3.8325 3C3.8325 3.10933 3.81097 3.21758 3.76913 3.31858C3.72729 3.41959 3.66597 3.51136 3.58867 3.58867C3.51136 3.66597 3.41959 3.72729 3.31858 3.76913C3.21758 3.81097 3.10933 3.8325 3 3.8325Z"
                                  fill="url(#grad1)"
                                />
                              </g>
                              <defs>
                                <clippath
                                  id="clip0_3774_843"
                                >
                                  <rect
                                    fill="white"
                                    height="6"
                                    width="6"
                                  />
                                </clippath>
                              </defs>
                            </svg>
                          </svg>
                          <svg
                            class="w-5 h-5"
                            height="16"
                            viewBox="0 0 16 16"
                            width="16"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <circle
                              cx="8"
                              cy="8"
                              fill="#2476F1"
                              r="6"
                            />
                            <svg
                              fill="none"
                              height="13"
                              viewBox="-2 -3 8 10"
                              width="13"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <g
                                clip-path="url(#clip0_3748_803)"
                              >
                                <path
                                  d="M5.11739 0.499974H0.882393C0.834787 0.499313 0.787518 0.508036 0.743284 0.525645C0.69905 0.543254 0.658718 0.569404 0.624592 0.602602C0.590466 0.6358 0.563213 0.675395 0.544391 0.719126C0.525568 0.762858 0.515545 0.809868 0.514893 0.857474V5.14247C0.515545 5.19008 0.525568 5.23709 0.544391 5.28082C0.563213 5.32455 0.590466 5.36415 0.624592 5.39735C0.658718 5.43054 0.69905 5.45669 0.743284 5.4743C0.787518 5.49191 0.834787 5.50064 0.882393 5.49997H5.11739C5.165 5.50064 5.21227 5.49191 5.2565 5.4743C5.30073 5.45669 5.34107 5.43054 5.37519 5.39735C5.40932 5.36415 5.43657 5.32455 5.45539 5.28082C5.47422 5.23709 5.48424 5.19008 5.48489 5.14247V0.857474C5.48424 0.809868 5.47422 0.762858 5.45539 0.719126C5.43657 0.675395 5.40932 0.6358 5.37519 0.602602C5.34107 0.569404 5.30073 0.543254 5.2565 0.525645C5.21227 0.508036 5.165 0.499313 5.11739 0.499974ZM2.02239 4.68497H1.27239V2.43497H2.02239V4.68497ZM1.64739 2.11997C1.54396 2.11997 1.44476 2.07888 1.37162 2.00575C1.29848 1.93261 1.25739 1.83341 1.25739 1.72997C1.25739 1.62654 1.29848 1.52734 1.37162 1.4542C1.44476 1.38106 1.54396 1.33997 1.64739 1.33997C1.70232 1.33375 1.75794 1.33919 1.81061 1.35594C1.86329 1.3727 1.91183 1.4004 1.95306 1.43721C1.99429 1.47403 2.02727 1.51914 2.04986 1.56959C2.07245 1.62005 2.08412 1.6747 2.08412 1.72997C2.08412 1.78525 2.07245 1.8399 2.04986 1.89035C2.02727 1.9408 1.99429 1.98592 1.95306 2.02273C1.91183 2.05955 1.86329 2.08725 1.81061 2.104C1.75794 2.12076 1.70232 2.1262 1.64739 2.11997ZM4.72739 4.68497H3.97739V3.47747C3.97739 3.17497 3.86989 2.97747 3.59739 2.97747C3.51306 2.97809 3.43094 3.00454 3.3621 3.05327C3.29326 3.10199 3.24101 3.17064 3.21239 3.24997C3.19283 3.30873 3.18435 3.37062 3.18739 3.43247V4.68247H2.43739V2.43247H3.18739V2.74997C3.25553 2.63175 3.35462 2.53435 3.474 2.46827C3.59339 2.4022 3.72853 2.36994 3.86489 2.37497C4.36489 2.37497 4.72739 2.69747 4.72739 3.38997V4.68497Z"
                                  fill="white"
                                />
                              </g>
                              <defs>
                                <clippath
                                  id="clip0_3748_803"
                                >
                                  <rect
                                    class=" fill-current text-white"
                                    height="6"
                                    width="6"
                                  />
                                </clippath>
                              </defs>
                            </svg>
                          </svg>
                        </div>
                      </div>
                    </div>
                    <div
                      class="absolute -top-[0.525rem] -right-[0.675rem] shrink h-42 w-32"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>
</div>
`;
