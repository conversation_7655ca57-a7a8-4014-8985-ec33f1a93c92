import { useLocation, useNavigate } from "react-router-dom";
import ResetPasswordConfirm from "./ResetPasswordConfirm";

export function ResetPasswordConfirmContainer() {
  const navigate = useNavigate();
  const location = useLocation();
  const { code } = location.state || { code: "" };

  function handleOnClick() {
    navigate("/set-new-password", { state: { code: code } });
  }

  return <ResetPasswordConfirm onClick={handleOnClick} />;
}

export default ResetPasswordConfirmContainer;
