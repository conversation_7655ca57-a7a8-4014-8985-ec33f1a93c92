import { useNavigate } from "react-router-dom";
import SetNewPassword from "./SetNewPassword";
import { useMutation } from "@tanstack/react-query";
import { callApi } from "@/utils";
import { SET_NEW_PASSWORD } from "@/queries/setNewPassword";

export type SignInWithEmailParam = {
  code: string;
  password: string;
  repeatPassword: string;
};

export function SetNewPasswordContainer() {
  const navigate = useNavigate();

  const { mutate, error, isPending } = useMutation<
    unknown,
    Error,
    SignInWithEmailParam
  >({
    mutationFn: (data: SignInWithEmailParam) => callApi(SET_NEW_PASSWORD, data),
    onSuccess: () => navigate("/set-new-password-success"),
  });

  return (
    <SetNewPassword onSend={mutate} isLoading={isPending} error={error!} />
  );
}

export default SetNewPasswordContainer;
