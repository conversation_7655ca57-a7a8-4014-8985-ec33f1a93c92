import { fireEvent, render, screen, waitFor } from "@tests/render";
import SetNewPassword from "./SetNewPassword";

describe("<SetNewPassword />", () => {
  it("should render the page", () => {
    render(<SetNewPassword onSend={jest.fn()} isLoading={false} />);
    expect(screen.getByTestId("set-new-password")).toMatchSnapshot();
  });

  it("should validate the form fields", async () => {
    const onSend = jest.fn();
    render(<SetNewPassword onSend={onSend} isLoading={false} />);

    const submitBtn = screen.getByTestId("set-new-password-btn");

    fireEvent.submit(submitBtn);

    await waitFor(() => {
      expect(
        screen.getAllByText("set-new-password.form.errors.required"),
      ).toBeTruthy();
      expect(onSend).toHaveBeenCalledTimes(0);
    });
  });

  it("should validate minimum character for password form field", async () => {
    const onSend = jest.fn();
    render(<SetNewPassword onSend={onSend} isLoading={false} />);

    const passwordInput = screen.getByTestId("password-input");
    const submitBtn = screen.getByTestId("set-new-password-btn");

    fireEvent.change(passwordInput, {
      target: { value: "132" },
    });
    fireEvent.submit(submitBtn);
    await waitFor(() => {
      expect(
        screen.getByText("set-new-password.form.errors.password-short"),
      ).toBeTruthy();
      expect(onSend).toHaveBeenCalledTimes(0);
    });
  });

  it("should validate different password for repeat password form field", async () => {
    const onSend = jest.fn();
    render(<SetNewPassword onSend={onSend} isLoading={false} />);

    const passwordInput = screen.getByTestId("password-input");
    const repeatPasswordInput = screen.getByTestId("repeat-password-input");
    const submitBtn = screen.getByTestId("set-new-password-btn");

    fireEvent.change(passwordInput, {
      target: { value: "qew132" },
    });
    fireEvent.change(repeatPasswordInput, {
      target: { value: "123asd" },
    });
    fireEvent.submit(submitBtn);
    await waitFor(() => {
      expect(
        screen.getByText("set-new-password.form.errors.password-not-match"),
      ).toBeTruthy();
      expect(onSend).toHaveBeenCalledTimes(0);
    });
  });

  it("should show the error", async () => {
    const onSend = jest.fn();
    const error = new Error("Invalid");
    render(<SetNewPassword onSend={onSend} isLoading={false} error={error} />);

    expect(screen.getByText("Invalid")).toBeTruthy();
  });

  it("should call onSend with correct data when form is submitted with valid data", async () => {
    const onSend = jest.fn();
    render(<SetNewPassword onSend={onSend} isLoading={false} />);

    const codeInput = screen.getByTestId("code-input");
    const passwordInput = screen.getByTestId("password-input");
    const repeatPasswordInput = screen.getByTestId("repeat-password-input");
    const submitBtn = screen.getByTestId("set-new-password-btn");

    fireEvent.change(codeInput, {
      target: { value: "code" },
    });
    fireEvent.change(passwordInput, {
      target: { value: "qwe123" },
    });
    fireEvent.change(repeatPasswordInput, {
      target: { value: "qwe123" },
    });
    fireEvent.submit(submitBtn);
    await waitFor(() => {
      expect(onSend).toHaveBeenCalledWith(
        {
          code: "code",
          repeatPassword: "qwe123",
          password: "qwe123",
        },
        expect.objectContaining({}),
      );
    });
  });
});
