import {
  Button,
  Heading,
  Input,
  PasswordStrength,
  Text,
  Image,
} from "@/components";
import CalculateStrength from "@/components/PasswordStrength/CalculateStrength";
import { ArrowRightIcon } from "@heroicons/react/24/outline";
import { ExclamationTriangleIcon } from "@heroicons/react/24/solid";
import { yupResolver } from "@hookform/resolvers/yup";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Trans, useTranslation } from "react-i18next";
import * as yup from "yup";
import logo from "@/assets/logo.svg";
import { SignInWithEmailParam } from "./SetNewPasswordContainer";

export type SetNewPasswordProps = {
  onSend: (params: SignInWithEmailParam) => void;
  isLoading: boolean;
  error?: Error;
};

export function SetNewPassword({
  onSend,
  isLoading,
  error,
}: SetNewPasswordProps) {
  const { t } = useTranslation();

  const schema = yup.object().shape({
    code: yup.string().required(t("set-new-password.form.errors.required")),
    password: yup
      .string()
      .required(t("set-new-password.form.errors.required"))
      .min(6, t("set-new-password.form.errors.password-short"))
      .test(
        "password-strength",
        t("set-new-password.form.errors.invalid-password"),
        (value) => {
          return CalculateStrength(value) > 2;
        },
      ),
    repeatPassword: yup
      .string()
      .required(t("set-new-password.form.errors.required"))
      .oneOf(
        [yup.ref("password")],
        t("set-new-password.form.errors.password-not-match"),
      ),
  });

  const {
    register,
    handleSubmit,
    setError,
    watch,
    formState: { errors },
  } = useForm<SignInWithEmailParam>({
    resolver: yupResolver(schema),
    delayError: 1000,
    mode: "onChange",
    shouldFocusError: true,
  });

  useEffect(() => {
    if (error) {
      setError("code", {
        message: error.message,
      });
    }
  }, [error]);

  const watchPassword = watch("password", "");

  return (
    <div
      data-testid="set-new-password"
      className="flex h-screen place-items-center justify-center px-4 overflow-x-hidden before:content-[''] before:opacity-35 before:blur-3xl before:fixed before:top-[0] before:left-[-20%] before:w-[2000px] before:h-[2000px] before:rounded-full before:block before:z-[-2] before:bg-amber-100 before:border-solid before:border-[200px] before:border-amber-50 before:opacity-80
after:blur-3xl after:content-[''] after:fixed after:top-[180px] after:right-[-35%] after:w-[2000px] after:h-[2000px] after:rounded-full after:block after:z-[-1] after:bg-slate-300 after:border-solid after:border-[200px] after:border-slate-5 after:opacity-50"
    >
      <div className="flex flex-col items-center text-center w-[30rem] sm:p-20 p-6 rounded-2xl bg-white">
        <Image src={logo} alt="Conteoodo" className="w-[10rem]" />
        <form className="w-full" onSubmit={handleSubmit(onSend)}>
          <Heading size="3" className="mt-[1.5rem]">
            {t("set-new-password.title")}
          </Heading>
          <Text className="mt-[1.5rem] leading-4">
            {t("set-new-password.description")}
          </Text>
          <label className="block text-black-900 text-[0.625rem] text-left font-semibold mb-2 mt-[1.5rem]">
            {t("set-new-password.form.labels.code")}
          </label>
          <Input
            placeholder={t("set-new-password.form.placeholders.code")}
            state={errors.code ? "error" : "default"}
            autoFocus
            data-testid="code-input"
            {...register("code")}
          />
          {errors.code && (
            <div className="flex flex-row gap-1 ml-1 mt-1 items-center">
              <ExclamationTriangleIcon className="h-4 text-red-700" />
              <Text className="text-red-700 text-left">
                {errors.code.message}
              </Text>
            </div>
          )}
          <label className="block text-black-900 text-[0.625rem] text-left font-semibold mb-2 mt-[1.5rem]">
            {t("sign-up-with-email.form.labels.password")}
          </label>
          <Input
            state={errors.password ? "error" : "default"}
            autoComplete="off"
            type="password"
            data-testid="password-input"
            {...register("password")}
          />
          {errors.password && (
            <div className="flex flex-row gap-1 ml-1 mt-1 items-center">
              <ExclamationTriangleIcon className="h-4 text-red-700" />
              <Text className="text-red-700 text-left">
                {errors.password.message}
              </Text>
            </div>
          )}
          <label className="block text-black-900 text-[0.625rem] text-left font-semibold mb-2 mt-6">
            {t("set-new-password.form.labels.repeat-password")}
          </label>
          <Input
            state={errors.repeatPassword ? "error" : "default"}
            autoComplete="off"
            type="password"
            data-testid="repeat-password-input"
            {...register("repeatPassword")}
          />
          {errors.repeatPassword && (
            <div className="flex flex-row gap-1 ml-1 mt-1 items-center">
              <ExclamationTriangleIcon className="h-4 text-red-700" />
              <Text className="text-red-700 text-left">
                {errors.repeatPassword.message}
              </Text>
            </div>
          )}
          {watchPassword.length != 0 && (
            <PasswordStrength value={watchPassword} className="mt-6" />
          )}
          {CalculateStrength(watchPassword) <= 2 &&
            CalculateStrength(watchPassword) > 0 && (
              <Text className="text-left mt-2 text-[0.625rem] leading-[0.875rem]">
                <Trans i18nKey={"set-new-password.form.weak-password-text"} />
              </Text>
            )}
          <Button
            className="mt-[1.5rem] flex justify-center items-center text-xs gap-2"
            type="submit"
            disabled={isLoading}
            data-testid="set-new-password-btn"
          >
            {t("set-new-password.form.continue")}
            <ArrowRightIcon className="w-[1.125rem] stroke-[0.15rem]" />
          </Button>
        </form>
      </div>
    </div>
  );
}

export default SetNewPassword;
