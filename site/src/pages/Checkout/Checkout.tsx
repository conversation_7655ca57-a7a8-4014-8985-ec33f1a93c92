import FAQSection from "./components/FAQSection";
import Footer from "./components/Footer";
import Navbar from "./components/Navbar";
import PlansSection from "./components/PlansSection";

export function Checkout() {
  return (
    <div
      data-testid="checkout"
      className="flex flex-col h-screen w-full overflow-x-hidden before:content-[''] before:opacity-35 before:blur-3xl before:fixed before:top-[0] before:left-[-20%] before:w-[2000px] before:h-[2000px] before:rounded-full before:block before:z-[-2] before:bg-amber-100 before:border-solid before:border-[200px] before:border-amber-50 before:opacity-80
      after:blur-3xl after:content-[''] after:fixed after:top-[180px] after:right-[-35%] after:w-[2000px] after:h-[2000px] after:rounded-full after:block after:z-[-1] after:bg-slate-300 after:border-solid after:border-[200px] after:border-slate-5 after:opacity-50"
    >
      <Navbar />
      <PlansSection />
      <FAQSection />
      <Footer />
    </div>
  );
}

export default Checkout;
