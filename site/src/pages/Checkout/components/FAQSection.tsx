import { Accordion, Heading, Text } from "@/components";
import {
  AccordionContent,
  AccordionPanel,
  AccordionTitle,
} from "@/components/Accordion";
import { Trans, useTranslation } from "react-i18next";
import { Link } from "@/components";

export function FAQSection() {
  const { t } = useTranslation();

  return (
    <section className="lg:py-20 py-14 sm:px-8 px-4">
      <div className="mx-auto max-w-[100rem] rounded-[2rem] bg-white pt-20 pb-14 lg:px-24">
        <div className="w-[90%] flex sm:flex-row flex-col sm:gap-20 gap-12 justify-between mx-auto">
          <div className="text-left sm:max-w-[45%]">
            <Heading className="font-semibold lg:leading-[3rem] leading-10 tracking-[-0.0375rem] max-lg:text-[2rem]">
              {t("checkout.questions.title")}
            </Heading>
            <Text className="font-outfit font-medium lg:text-2xl text-sm mt-6">
              <Trans i18nKey={"checkout.questions.description"}>
                ***
                <Link href="#"> *** </Link>***
                <Link href="#"> ***</Link>***
              </Trans>
            </Text>
          </div>
          <Accordion className="sm:shrink-0 sm:w-96 min-h-[30rem] max-w-[30rem] grow">
            <AccordionPanel>
              <AccordionTitle className="text-2xl font-semibold">
                {t("checkout.questions.question-1")}
              </AccordionTitle>
              <AccordionContent className="text-base font-normal text-left mb-2">
                {t("checkout.questions.answer-1")}
              </AccordionContent>
            </AccordionPanel>
            <AccordionPanel>
              <AccordionTitle className="text-2xl font-semibold">
                {t("checkout.questions.question-2")}
              </AccordionTitle>
              <AccordionContent className="text-base font-normal text-left mb-2">
                {t("checkout.questions.answer-2")}
              </AccordionContent>
            </AccordionPanel>
            <AccordionPanel>
              <AccordionTitle className="text-2xl font-semibold">
                {t("checkout.questions.question-3")}
              </AccordionTitle>
              <AccordionContent className="text-base font-normal text-left mb-2">
                {t("checkout.questions.answer-3")}
              </AccordionContent>
            </AccordionPanel>
            <AccordionPanel>
              <AccordionTitle className="text-2xl font-semibold">
                {t("checkout.questions.question-4")}
              </AccordionTitle>
              <AccordionContent className="text-base font-normal text-left mb-2">
                {t("checkout.questions.answer-4")}
              </AccordionContent>
            </AccordionPanel>
          </Accordion>
        </div>
      </div>
    </section>
  );
}

export default FAQSection;
