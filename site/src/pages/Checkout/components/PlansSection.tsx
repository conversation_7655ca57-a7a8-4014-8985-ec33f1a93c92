import { <PERSON><PERSON>, <PERSON>, Heading, Input, Text } from "@/components";
import { ArrowRightIcon } from "@heroicons/react/24/outline";
import { useTranslation } from "react-i18next";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";

export type Input = {
  addCredits?: number;
};

export function PlansSection() {
  const { t } = useTranslation();

  const schema = yup.object().shape({
    addCredits: yup
      .number()
      .test(
        "minimum 100",
        t("checkout.erros.min-added-credits"),
        (value) => Number(value) >= 100 || value === 0,
      )
      .integer(t("checkout.erros.type")),
  });

  const {
    watch,
    register,
    getValues,
    setValue,
    setError,
    clearErrors,
    formState: { errors },
  } = useForm<Input>({
    resolver: yupResolver(schema),
    delayError: 1000,
    mode: "onChange",
  });

  const watchAddCredits = watch("addCredits");

  const customDefaultValues = {
    price: 799.9,
    credits: 4800,
  };

  function onSubmit(plan: "basic" | "plus" | "advanced" | "custom") {
    switch (plan) {
      case "basic":
        console.log({ plan: { type: "basic" } });
        break;
      case "plus":
        console.log({ plan: { type: "plus" } });
        break;
      case "advanced":
        console.log({ plan: { type: "advanced" } });
        break;
      case "custom":
        if (Number(watchAddCredits) > 0 && errors.addCredits) {
          break;
        } else {
          console.log({
            plan: { type: "custom", addCredits: watchAddCredits || 0 },
          });
          break;
        }
    }
  }

  function getLanguage() {
    switch (t("checkout.language")) {
      case "checkout.language":
        return "en-US";
      default:
        return t("checkout.language");
    }
  }

  function changeValue(type: "+" | "-") {
    if (getValues("addCredits") && type === "+") {
      setValue("addCredits", Number(getValues("addCredits")) + 100);
      clearErrors();
    } else if (getValues("addCredits") && type === "-") {
      if (Number(getValues("addCredits")) >= 100) {
        setValue("addCredits", Number(getValues("addCredits")) - 100);
        if (
          Number(getValues("addCredits")) < 100 &&
          Number(getValues("addCredits")) > 0
        ) {
          setError("addCredits", {
            type: "manual",
            message: t("checkout.erros.min-added-credits"),
          });
        } else {
          clearErrors();
        }
      } else {
        setValue("addCredits", 0);
        clearErrors();
      }
    } else if (type === "+") {
      setValue("addCredits", 100);
      clearErrors();
    }
  }

  function creditPriceReasoning(credit: number) {
    return credit / 50;
  }

  return (
    <section className="mt-6 max-sm:px-4">
      <Heading className="text-center">{t("checkout.title")}</Heading>
      <div className="mt-14 flex lg:flex-row flex-col items-center justify-center min-xl:gap-[2.31rem] min-lg:gap-7 gap-8">
        <div className="flex sm:flex-row flex-col min-sm:gap-[2.31rem] gap-8">
          <Card
            fullWidth
            color="secondary"
            className="xl:w-64 sm:w-56 w-full h-[22.5rem] min-xl:p-8 p-6 flex flex-col gap-4 items-start"
          >
            <Heading size="3">{t("checkout.plans.card-1.title")}</Heading>
            <Text className="text-base font-normal leading-[1.3125rem]">
              {t("checkout.plans.card-1.description")}
            </Text>
            <div className="flex flex-row gap-2 items-center">
              <Text className="text-yellow-900 font-outfit font-semibold text-base">
                300 {t("checkout.plans.card-1.credits")}
              </Text>
              <Text>{t("checkout.plans.per-month")}</Text>
            </div>
            <div className="flex flex-row items-end">
              <Text className="font-outfit text-base font-semibold mr-1">
                {t("checkout.plans.currency")}
              </Text>
              <Text className="font-outfit text-[2rem] font-bold leading-9 mr-2">
                {t("checkout.plans.card-1.price")}
              </Text>
              <Text>{t("checkout.plans.per-month")}</Text>
            </div>
            <Button
              size="small"
              className="flex flex-row gap-2 justify-center items-center mt-4"
              onClick={() => onSubmit("basic")}
            >
              {t("checkout.plans.sign")}
              <ArrowRightIcon className="h-3 stroke-[0.2rem]" />
            </Button>
          </Card>
          <Card
            fullWidth
            color="secondary"
            className="xl:w-64 sm:w-56 w-full h-[22.5rem] min-xl:p-8 p-6 flex flex-col gap-4 items-start"
          >
            <Heading size="3">{t("checkout.plans.card-2.title")}</Heading>
            <Text className="text-base font-normal leading-[1.3125rem]">
              {t("checkout.plans.card-2.description")}
            </Text>
            <div className="flex flex-row gap-2 items-center">
              <Text className="text-yellow-900 font-outfit font-semibold text-base">
                1000 {t("checkout.plans.card-2.credits")}
              </Text>
              <Text>{t("checkout.plans.per-month")}</Text>
            </div>
            <div className="flex flex-row items-end">
              <Text className="font-outfit text-base font-semibold mr-1">
                {t("checkout.plans.currency")}
              </Text>
              <Text className="font-outfit text-[2rem] font-bold leading-9 mr-2">
                {t("checkout.plans.card-2.price")}
              </Text>
              <Text>{t("checkout.plans.per-month")}</Text>
            </div>
            <Button
              size="small"
              className="flex flex-row gap-2 justify-center items-center mt-4"
              onClick={() => onSubmit("plus")}
            >
              {t("checkout.plans.sign")}
              <ArrowRightIcon className="h-3 stroke-[0.2rem]" />
            </Button>
          </Card>
        </div>
        <div className="flex sm:flex-row flex-col min-sm:gap-[2.31rem] gap-8">
          <Card
            fullWidth
            color="quinternary"
            badge={{ content: "Recomendado", variant: "quaternary" }}
            className="xl:w-64 sm:w-56 w-full h-[22.5rem] min-xl:p-8 p-6 flex flex-col gap-4 items-start"
          >
            <Heading size="3">{t("checkout.plans.card-3.title")}</Heading>
            <Text className="text-base font-normal leading-[1.3125rem]">
              {t("checkout.plans.card-3.description")}
            </Text>
            <div className="flex flex-row gap-2 items-center">
              <Text className="text-yellow-900 font-outfit font-semibold text-base">
                2500 {t("checkout.plans.card-3.credits")}
              </Text>
              <Text>{t("checkout.plans.per-month")}</Text>
            </div>
            <div className="flex flex-row items-end">
              <Text className="font-outfit text-base font-semibold mr-1">
                {t("checkout.plans.currency")}
              </Text>
              <Text className="font-outfit text-[2rem] font-bold leading-9 mr-2">
                {t("checkout.plans.card-3.price")}
              </Text>
              <Text>{t("checkout.plans.per-month")}</Text>
            </div>
            <Button
              size="small"
              className="flex flex-row gap-2 justify-center items-center mt-4"
              onClick={() => onSubmit("advanced")}
            >
              {t("checkout.plans.sign")}
              <ArrowRightIcon className="h-3 stroke-[0.2rem]" />
            </Button>
          </Card>
          <Card
            fullWidth
            color="secondary"
            className="xl:w-64 sm:w-56 w-full h-[22.5rem] min-xl:p-8 p-6 flex flex-col gap-4 items-start"
          >
            <Heading size="3">{t("checkout.plans.card-4.title")}</Heading>
            <Text className="text-base font-normal leading-[1.3125rem]">
              {t("checkout.plans.card-4.description")}
            </Text>
            <div className="flex flex-row gap-2 items-center">
              <Text className="text-yellow-900 font-outfit font-semibold text-base">
                {Number(watchAddCredits)
                  ? customDefaultValues.credits + Number(watchAddCredits)
                  : customDefaultValues.credits}{" "}
                {t("checkout.plans.card-4.credits")}
              </Text>
              <Text>{t("checkout.plans.per-month")}</Text>
            </div>
            <div className="flex flex-row items-end">
              <Text className="font-outfit text-base font-semibold mr-1">
                {t("checkout.plans.currency")}
              </Text>
              <Text className="font-outfit text-[2rem] font-bold leading-9 mr-2">
                {(Number(watchAddCredits)
                  ? customDefaultValues.price +
                    creditPriceReasoning(watchAddCredits ? watchAddCredits : 0)
                  : customDefaultValues.price
                ).toLocaleString(getLanguage(), {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}
              </Text>
              <Text>{t("checkout.plans.per-month")}</Text>
            </div>
            <div className="flex flex-row items-center justify-center gap-2">
              <Button
                size="small"
                color="secondary"
                className="w-7 h-7 text-xl font-medium py-0 px-2 rounded-full"
                onClick={() => changeValue("-")}
                rounded
              >
                -
              </Button>
              <Input
                {...register("addCredits")}
                state={
                  errors.addCredits
                    ? !watchAddCredits
                      ? "default"
                      : "error"
                    : "default"
                }
              />
              <Button
                size="small"
                color="secondary"
                className="w-7 h-7 text-xl font-medium py-0 px-2 rounded-full"
                onClick={() => changeValue("+")}
                rounded
              >
                +
              </Button>
            </div>
            {errors.addCredits && (
              <Text className="text-red-600 mt-[-1rem] mx-auto">
                {errors.addCredits.type === "typeError"
                  ? !watchAddCredits
                    ? ""
                    : t("checkout.erros.type")
                  : errors.addCredits.message}
              </Text>
            )}
            <Button
              size="small"
              className="flex flex-row gap-2 justify-center items-center"
              onClick={() => onSubmit("custom")}
            >
              {t("checkout.plans.sign")}
              <ArrowRightIcon className="h-3 stroke-[0.2rem]" />
            </Button>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default PlansSection;
