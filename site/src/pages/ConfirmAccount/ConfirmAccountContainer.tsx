import { useMutation } from "@tanstack/react-query";
import ConfirmAccount from "./ConfirmAccount";
import { useLocation, useNavigate } from "react-router-dom";
import { callApi } from "@/utils";
import { CONFIRM_ACCOUNT } from "@/queries/confirmAccount";

export type ConfirmAccountParam = {
  code: string;
};

function ConfirmAccountContainer() {
  const navigate = useNavigate();
  const location = useLocation();

  const { isPending, mutate, error } = useMutation<
    unknown,
    Error,
    ConfirmAccountParam
  >({
    mutationFn: (data: ConfirmAccountParam) =>
      callApi(CONFIRM_ACCOUNT, {
        email: location.state.email,
        code: data.code,
      }),
    onSuccess: async () => navigate("/sign-in-with-email"),
  });

  return (
    <ConfirmAccount onConfirm={mutate} isLoading={isPending} error={error!} />
  );
}

export default ConfirmAccountContainer;
