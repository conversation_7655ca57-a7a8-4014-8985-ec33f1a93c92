import { fireEvent, render, screen, waitFor } from "@tests/render";
import ConfirmAccount from "./ConfirmAccount";

describe("<ConfirmAccount>", () => {
  it("should render the page", () => {
    render(<ConfirmAccount onConfirm={jest.fn()} isLoading={false} />);
    expect(screen.getByTestId("confirm-account")).toMatchSnapshot();
  });

  it("should validate the form fields", async () => {
    const onConfirm = jest.fn();
    render(<ConfirmAccount onConfirm={onConfirm} isLoading={false} />);

    const confirmBtn = screen.getByTestId("confirm-btn");

    fireEvent.submit(confirmBtn);

    await waitFor(() => {
      expect(
        screen.getByText("confirm-account.form.errors.required"),
      ).toBeTruthy();
      expect(onConfirm).toHaveBeenCalledTimes(0);
    });
  });

  it("should call onConfirm with correct data when form is submitted with valid data", async () => {
    const onConfirm = jest.fn();
    render(<ConfirmAccount onConfirm={onConfirm} isLoading={false} />);
    const codeInput = screen.getByPlaceholderText(
      /confirm-account.form.placeholders.code/i,
    );
    const confirmBtn = screen.getByTestId("confirm-btn");

    fireEvent.change(codeInput, {
      target: { value: "qwe123" },
    });

    fireEvent.submit(confirmBtn);
    await waitFor(() => {
      expect(onConfirm).toHaveBeenCalledWith(
        {
          code: "qwe123",
        },
        expect.objectContaining({}),
      );
    });
  });
});
