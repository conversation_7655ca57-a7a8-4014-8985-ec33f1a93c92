import { Card, Text, Image, Button, Heading, Modal, Input } from "@/components";
import brunaoPersonal from "@/assets/brunaoPersonal.png";
import { BuildingStorefrontIcon, PlusIcon } from "@heroicons/react/24/outline";

import { HTMLAttributes, useState } from "react";
import { useTranslation } from "react-i18next";
import { ModalBody, ModalHeader } from "@/components/Modal";
import { RoundedIcon } from "@/components/CustomIcons";

interface MainContentProps extends HTMLAttributes<HTMLDivElement> {}

export function MainContent(props: MainContentProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { t } = useTranslation();

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  return (
    <div className="container m-auto" {...props}>
      <section className="flex-col inline-flex gap-8 mt-8 mr-32">
        <div className="flex justify-between">
          <Heading size="3" className="flex items-center">
            <BuildingStorefrontIcon className="w-6 h-6 mr-1" />
            {t("clientsaccounts.title")}
          </Heading>
          <Button
            onClick={openModal}
            color="primary"
            className="w-auto flex items-center py-3 px-[0.813rem]"
          >
            {t("clientsaccounts.button")}
            <PlusIcon className="w-8 h-5" />
          </Button>
          {isModalOpen && (
            <Modal
              size="md"
              className="flex-col inline-flex gap-6"
              show={isModalOpen}
              onClose={closeModal}
            >
              <ModalHeader>
                <div className="flex items-center justify-center mx-auto">
                  <RoundedIcon>
                    <BuildingStorefrontIcon width="40px" height="40px" />
                  </RoundedIcon>
                </div>
                <Heading size="3" className="mt-6">
                  {t("createclient.modalcreateclient.hadear")}
                </Heading>
              </ModalHeader>
              <ModalBody>
                <form>
                  <div className="mb-4">
                    <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
                      {t("createclient.modalcreateclient.body.text1")}
                    </Text>
                    <Input state="default" className="border-gray-300" />
                  </div>
                  <div className="mb-4">
                    <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
                      {t("createclient.modalcreateclient.body.text2")}
                    </Text>
                    <select
                      id="teamName"
                      className="border rounded-lg border-gray-300 px-4 py-2 w-full"
                    >
                      <option value="team1">Team 1</option>
                      <option value="team2">Team 2</option>
                      <option value="team3">Team 3</option>
                    </select>
                  </div>
                  <div className="mb-4">
                    <Text className="font-outfit text-sm text-black-900 font-semibold leading-[1.079rem] block mb-2">
                      {t("createclient.modalcreateclient.body.text3")}
                    </Text>
                    <select
                      id="teamMembers"
                      className="border rounded-lg border-gray-300 px-4 py-2 w-full"
                    >
                      <option value="member1">
                        {t(
                          "createclient.modalcreateclient.body.options.permission1",
                        )}
                      </option>
                      <option value="member2">
                        {t(
                          "createclient.modalcreateclient.body.options.permission2",
                        )}
                      </option>
                    </select>
                  </div>
                </form>
                <div className="flex mt-7 gap-4">
                  <Button color="secondary" onClick={closeModal}>
                    {t("createclient.modalcreateclient.fotter.botton1")}
                  </Button>
                  <Button>
                    {t("createclient.modalcreateclient.fotter.botton2")}
                  </Button>
                </div>
              </ModalBody>
            </Modal>
          )}
        </div>
        <div className="grid gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
          {[...Array(20)].map((_, index) => (
            <Card key={index} color="quinternary">
              <Image
                className="rounded-[0.75rem] mb-2 w-full"
                src={brunaoPersonal}
              />
              <div className="flex flex-col gap-1">
                <div className="flex flex-row items-center gap-1">
                  <BuildingStorefrontIcon className="w-4 h-4 text-gray-800" />
                  <Text className="font-outfit text-xs text-black-900 font-semibold leading-[1.079rem]">
                    Fernanda Nutricionista
                  </Text>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </section>
    </div>
  );
}
