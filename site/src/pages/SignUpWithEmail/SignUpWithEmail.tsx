import logo from "@/assets/logo.svg";
import { Button, Heading, Input, Text, Image } from "@/components";
import { useForm } from "react-hook-form";
import { ArrowRightIcon } from "@heroicons/react/24/outline";
import { useTranslation } from "react-i18next";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { ExclamationTriangleIcon } from "@heroicons/react/24/solid";
import { SignUpParam } from "./SignUpWithEmailContainer";

type SignUpWithEmailProps = {
  onSignUp: (params: SignUpParam) => void;
};

function SignUpWithEmail({ onSignUp }: SignUpWithEmailProps) {
  const { t } = useTranslation();
  const schema = yup.object().shape({
    fullName: yup
      .string()
      .required(t("sign-up-with-email.form.errors.required")),
    email: yup
      .string()
      .required(t("sign-up-with-email.form.errors.required"))
      .email(t("sign-up-with-email.form.errors.invalid-email")),
  });

  const {
    handleSubmit,
    register,
    formState: { errors },
  } = useForm<SignUpParam>({
    resolver: yupResolver(schema),
    mode: "onChange",
  });

  return (
    <div
      data-testid="sign-up-with-email"
      className="flex h-screen place-items-center justify-center before:content-[''] before:opacity-35 before:blur-3xl before:fixed before:top-[0] before:left-[-20%] before:w-[2000px] before:h-[2000px] before:rounded-full before:block before:z-[-2] before:bg-amber-100 before:border-solid before:border-[200px] before:border-amber-50 before:opacity-80
      after:blur-3xl after:content-[''] after:fixed after:top-[180px] after:right-[-35%] after:w-[2000px] after:h-[2000px] after:rounded-full after:block after:z-[-1] after:bg-slate-300 after:border-solid after:border-[200px] after:border-slate-5 after:opacity-80"
    >
      <div className="flex flex-col items-center text-center w-[30rem] p-20 rounded-2xl bg-white">
        <Image src={logo} alt="Conteoodo" className="w-[10rem]" />
        <div className="mt-[1.5rem] w-full">
          <form onSubmit={handleSubmit(onSignUp)}>
            <Heading size="3">{t("sign-up-with-email.title")}</Heading>
            <Text className="font-outfit font-semibold text-[0.75rem] leading-4 mt-[1.5rem]">
              {t("sign-up-with-email.description")}
            </Text>
            <label
              className="block text-black-900 text-[0.625rem] text-left font-semibold mb-2 mt-[1.5rem]"
              htmlFor="fullName"
            >
              {t("sign-up-with-email.form.labels.name")}
            </label>
            <Input
              placeholder={t("sign-up-with-email.form.placeholders.name")}
              state={errors.fullName ? "error" : "default"}
              autoFocus
              {...register("fullName")}
            />
            {errors.fullName && (
              <div className="flex flex-row gap-1 ml-1 mt-1 items-center">
                <ExclamationTriangleIcon className="h-4 text-red-700" />
                <Text className="text-red-700 text-left">
                  {errors.fullName.message}
                </Text>
              </div>
            )}

            <label
              className="block text-black-900 text-[0.625rem] text-left font-semibold mb-2 mt-6"
              htmlFor="email"
            >
              {t("sign-up-with-email.form.labels.email")}
            </label>
            <Input
              placeholder={t("sign-up-with-email.form.placeholders.email")}
              state={errors.email ? "error" : "default"}
              {...register("email")}
            />
            {errors.email && (
              <div className="flex flex-row gap-1 ml-1 mt-1 items-center">
                <ExclamationTriangleIcon className="h-4 text-red-700" />
                <Text className="text-red-700 text-left">
                  {errors.email.message}
                </Text>
              </div>
            )}
            <Button
              className="mt-[1.5rem] flex justify-center items-center text-xs gap-2"
              type="submit"
              data-testid="sign-up-btn"
            >
              {t("sign-up-with-email.continue")}
              <ArrowRightIcon className="w-[1.125rem] stroke-[0.15rem]" />
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
}

export default SignUpWithEmail;
