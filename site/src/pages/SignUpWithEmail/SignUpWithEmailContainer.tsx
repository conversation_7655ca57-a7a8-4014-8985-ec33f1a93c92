import SignUpWithEmail from "./SignUpWithEmail";
import { useNavigate } from "react-router-dom";

export type SignUpParam = {
  fullName: string;
  email: string;
};

function SignUpWithEmailContainer() {
  const navigate = useNavigate();

  const onSignUp = (data: SignUpParam) =>
    navigate("/sign-up-with-email2", {
      state: data,
    });

  return <SignUpWithEmail onSignUp={onSignUp} />;
}

export default SignUpWithEmailContainer;
