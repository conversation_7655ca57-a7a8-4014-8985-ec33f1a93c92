import { fireEvent, render, screen, waitFor } from "@tests/render";
import SignUpWithEmail from "./SignUpWithEmail";

describe("<SignUpWithEmail>", () => {
  it("should render the page", () => {
    render(<SignUpWithEmail onSignUp={jest.fn()} />);
    expect(screen.getByTestId("sign-up-with-email")).toMatchSnapshot();
  });

  it("should validate the form fields", async () => {
    const onSignUp = jest.fn();
    render(<SignUpWithEmail onSignUp={onSignUp} />);

    const emailInput = screen.getByPlaceholderText(
      /sign-up-with-email.form.placeholders.email/i,
    );
    const submitBtn = screen.getByTestId("sign-up-btn");

    fireEvent.change(emailInput, {
      target: { value: "invalid-email" },
    });

    fireEvent.submit(submitBtn);

    await waitFor(() => {
      expect(
        screen.getByText("sign-up-with-email.form.errors.required"),
      ).toBeTruthy();
      expect(
        screen.getByText("sign-up-with-email.form.errors.invalid-email"),
      ).toBeTruthy();
      expect(onSignUp).toHaveBeenCalledTimes(0);
    });
  });

  it("should call onSignUp with correct data when form is submitted with valid data", async () => {
    const onSignUp = jest.fn();
    render(<SignUpWithEmail onSignUp={onSignUp} />);
    const nameInput = screen.getByPlaceholderText(
      /sign-up-with-email.form.placeholders.name/i,
    );
    const emailInput = screen.getByPlaceholderText(
      /sign-up-with-email.form.placeholders.email/i,
    );
    const submitBtn = screen.getByTestId("sign-up-btn");

    fireEvent.change(nameInput, {
      target: { value: "Marques" },
    });
    fireEvent.change(emailInput, {
      target: { value: "<EMAIL>" },
    });

    fireEvent.submit(submitBtn);
    await waitFor(() => {
      expect(onSignUp).toHaveBeenCalledWith(
        {
          email: "<EMAIL>",
          fullName: "Marques",
        },
        expect.objectContaining({}),
      );
    });
  });
});
