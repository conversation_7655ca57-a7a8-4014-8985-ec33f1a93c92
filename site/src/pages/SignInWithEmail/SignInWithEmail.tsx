import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { Button, Input, Text, Image, Heading, Link } from "@/components";
import { ExclamationTriangleIcon } from "@heroicons/react/24/solid";
import { ArrowRightIcon } from "@heroicons/react/24/outline";
import { useTranslation } from "react-i18next";
import { useEffect } from "react";
import { SignInWithEmailParam } from "./SignInWithEmailContainer";
import logo from "@/assets/logo.svg";

export type SignInWithEmailProps = {
  onSignIn: (data: SignInWithEmailParam) => void;
  isLoading: boolean;
  error?: Error;
};

export function SignInWithEmail({
  onSignIn,
  isLoading,
  error,
}: SignInWithEmailProps) {
  const { t } = useTranslation();
  const schema = yup.object().shape({
    email: yup
      .string()
      .required(t("sign-in-with-email.form.errors.required"))
      .email(t("sign-in-with-email.form.errors.invalid-email")),
    password: yup
      .string()
      .required(t("sign-in-with-email.form.errors.required")),
  });
  const {
    handleSubmit,
    register,
    formState: { errors },
    setError,
  } = useForm<SignInWithEmailParam>({
    resolver: yupResolver(schema),
    mode: "onChange",
  });

  useEffect(() => {
    if (error) {
      setError("password", {
        type: "server",
        message: error.message,
      });
    }
  }, [error]);

  return (
    <div
      data-testid="sign-in-with-email"
      className="flex h-screen place-items-center justify-center px-4 overflow-x-hidden before:content-[''] before:opacity-35 before:blur-3xl before:fixed before:top-[0] before:left-[-20%] before:w-[2000px] before:h-[2000px] before:rounded-full before:block before:z-[-2] before:bg-amber-100 before:border-solid before:border-[200px] before:border-amber-50 before:opacity-80
    after:blur-3xl after:content-[''] after:fixed after:top-[180px] after:right-[-35%] after:w-[2000px] after:h-[2000px] after:rounded-full after:block after:z-[-1] after:bg-slate-300 after:border-solid after:border-[200px] after:border-slate-5 after:opacity-50"
    >
      <div className="flex flex-col items-center text-center w-[30rem] sm:p-20 p-6 rounded-2xl bg-white">
        <Image src={logo} alt="Conteoodo" className="w-[10rem]" />
        <Heading size="3" className="mt-[1.5rem]">
          {t("sign-in-with-email.title")}
        </Heading>
        <form className="w-full" onSubmit={handleSubmit(onSignIn)}>
          <label
            className="block text-black-900 text-[0.625rem] text-left font-semibold mb-2 mt-[1.5rem]"
            data-testid="email-label"
          >
            {t("sign-in-with-email.form.labels.email")}
          </label>
          <Input
            placeholder={t("sign-in-with-email.form.placeholders.email")}
            state={errors.email ? "error" : "default"}
            {...register("email")}
          />
          {errors.email && (
            <div className="flex flex-row gap-1 ml-1 mt-1 items-center">
              <ExclamationTriangleIcon className="h-4 text-red-700" />
              <Text className="text-red-700 text-left">
                {errors.email.message}
              </Text>
            </div>
          )}

          <label
            className="block text-black-900 text-[0.625rem] text-left font-semibold mb-2 mt-6"
            htmlFor="password-input"
            data-testid="password-label"
          >
            {t("sign-in-with-email.form.labels.password")}
          </label>
          <Input
            placeholder={t("sign-in-with-email.form.placeholders.password")}
            state={errors.password ? "error" : "default"}
            type="password"
            {...register("password")}
          />
          {errors.password && (
            <div className="flex flex-row gap-1 ml-1 mt-1 items-center">
              <ExclamationTriangleIcon className="h-4 text-red-700" />
              <Text className="text-red-700 text-left">
                {errors.password.message}
              </Text>
            </div>
          )}

          <Button
            className="mt-[1.5rem] flex justify-center items-center text-xs gap-2"
            disabled={isLoading}
            type="submit"
            data-testid="sign-in-btn"
          >
            {t("sign-in-with-email.continue")}
            <ArrowRightIcon className="w-[1.125rem] stroke-[0.15rem]" />
          </Button>
        </form>
        <Link className="mt-6 leading-none" href="/reset-password">
          {t("sign-in-with-email.reset-password")}
        </Link>
      </div>
    </div>
  );
}

export default SignInWithEmail;
