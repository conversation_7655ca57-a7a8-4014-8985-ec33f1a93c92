// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<SignInWithEmail> should render the page 1`] = `
<div
  class="flex h-screen place-items-center justify-center px-4 overflow-x-hidden before:content-[''] before:opacity-35 before:blur-3xl before:fixed before:top-[0] before:left-[-20%] before:w-[2000px] before:h-[2000px] before:rounded-full before:block before:z-[-2] before:bg-amber-100 before:border-solid before:border-[200px] before:border-amber-50 before:opacity-80 after:blur-3xl after:content-[''] after:fixed after:top-[180px] after:right-[-35%] after:w-[2000px] after:h-[2000px] after:rounded-full after:block after:z-[-1] after:bg-slate-300 after:border-solid after:border-[200px] after:border-slate-5 after:opacity-50"
  data-testid="sign-in-with-email"
>
  <div
    class="flex flex-col items-center text-center w-[30rem] sm:p-20 p-6 rounded-2xl bg-white"
  >
    <img
      alt="Conteoodo"
      class="w-[10rem]"
      src=""
    />
    <h1
      class="font-outfit font-[700] text-[1.5rem] mt-[1.5rem]"
    >
      sign-in-with-email.title
    </h1>
    <form
      class="w-full"
    >
      <label
        class="block text-black-900 text-[0.625rem] text-left font-semibold mb-2 mt-[1.5rem]"
        data-testid="email-label"
      >
        sign-in-with-email.form.labels.email
      </label>
      <div
        class="relative flex flex-row items-center w-full"
        data-testid="input"
      >
        <input
          class="py-[0.9375rem] px-[0.8125rem] text-xs border rounded-lg bg-white w-full h-10 disabled:border-gray-200 focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none border-gray-700"
          name="email"
          placeholder="sign-in-with-email.form.placeholders.email"
        />
      </div>
      <label
        class="block text-black-900 text-[0.625rem] text-left font-semibold mb-2 mt-6"
        data-testid="password-label"
        for="password-input"
      >
        sign-in-with-email.form.labels.password
      </label>
      <div
        class="relative flex flex-row items-center w-full"
        data-testid="input"
      >
        <input
          class="py-[0.9375rem] px-[0.8125rem] text-xs border rounded-lg bg-white w-full h-10 disabled:border-gray-200 focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none border-gray-700"
          name="password"
          placeholder="sign-in-with-email.form.placeholders.password"
          type="password"
        />
        <svg
          aria-hidden="true"
          class="h-10 cursor-pointer p-[0.65rem] stroke-[0.13rem] absolute right-0"
          data-slot="icon"
          fill="none"
          stroke="currentColor"
          stroke-width="1.5"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <button
        class="w-full py-3 px-4 rounded-md font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white mt-[1.5rem] flex justify-center items-center text-xs gap-2"
        data-testid="sign-in-btn"
        type="submit"
      >
        sign-in-with-email.continue
        <svg
          aria-hidden="true"
          class="w-[1.125rem] stroke-[0.15rem]"
          data-slot="icon"
          fill="none"
          stroke="currentColor"
          stroke-width="1.5"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </form>
    <a
      class="hover:underline text-yellow-900 hover:text-black-900 mt-6 leading-none"
      href="/reset-password"
    >
      sign-in-with-email.reset-password
    </a>
  </div>
</div>
`;
