import { fireEvent, render, screen, waitFor } from "@tests/render";
import SignInWithEmail from "./SignInWithEmail";

describe("<SignInWithEmail>", () => {
  it("should render the page", () => {
    render(<SignInWithEmail isLoading={false} onSignIn={jest.fn()} />);
    expect(screen.getByTestId("sign-in-with-email")).toMatchSnapshot();
  });

  it("should validate the form fields", async () => {
    const onSignIn = jest.fn();
    render(<SignInWithEmail isLoading={false} onSignIn={onSignIn} />);

    const emailInput = screen.getByPlaceholderText(
      /sign-in-with-email.form.placeholders.email/i,
    );
    const submitBtn = screen.getByTestId("sign-in-btn");

    fireEvent.change(emailInput, {
      target: { value: "invalid-email" },
    });

    fireEvent.submit(submitBtn);

    await waitFor(() => {
      expect(
        screen.getByText("sign-in-with-email.form.errors.required"),
      ).toBeTruthy();
      expect(
        screen.getByText("sign-in-with-email.form.errors.invalid-email"),
      ).toBeTruthy();
      expect(onSignIn).toHaveBeenCalledTimes(0);
    });
  });

  it("should show the error", async () => {
    const onSignIn = jest.fn();
    const error = new Error("Invalid");
    render(
      <SignInWithEmail onSignIn={onSignIn} isLoading={false} error={error} />,
    );

    expect(screen.getByText("Invalid")).toBeTruthy();
  });

  it("should call onSignIn with correct data when form is submitted with valid data", async () => {
    const onSignIn = jest.fn();
    render(<SignInWithEmail onSignIn={onSignIn} isLoading={false} />);
    const emailInput = screen.getByPlaceholderText(
      /sign-in-with-email.form.placeholders.email/i,
    );
    const passwordInput = screen.getByPlaceholderText(
      /sign-in-with-email.form.placeholders.password/i,
    );
    const submitBtn = screen.getByTestId("sign-in-btn");

    fireEvent.change(emailInput, {
      target: { value: "<EMAIL>" },
    });
    fireEvent.change(passwordInput, { target: { value: "qwe123" } });

    fireEvent.submit(submitBtn);
    await waitFor(() => {
      expect(onSignIn).toHaveBeenCalledWith(
        { email: "<EMAIL>", password: "qwe123" },
        expect.objectContaining({}),
      );
    });
  });
});
