import { useCookies } from "react-cookie";
import { useNavigate } from "react-router-dom";
import SignInWithEmail from "./SignInWithEmail";
import { callApi } from "@/utils";
import { SIGN_IN } from "@/queries/signIn";
import { useMutation } from "@tanstack/react-query";

interface ApiResponse {
  signIn: {
    token: string;
  };
}

export type SignInWithEmailParam = {
  email: string;
  password: string;
};

export function SignInWithEmailContainer() {
  const navigate = useNavigate();
  const [, setCookie] = useCookies(["token"]);

  const { isPending, mutate, error } = useMutation<
    ApiResponse,
    Error,
    SignInWithEmailParam
  >({
    mutationFn: (data: SignInWithEmailParam) => callApi(SIGN_IN, data),
    onSuccess: (data) => {
      setCookie("token", data.signIn.token, { path: "/" });
      navigate("/dashboard");
    },
  });

  return (
    <SignInWithEmail onSignIn={mutate} isLoading={isPending} error={error!} />
  );
}

export default SignInWithEmailContainer;
