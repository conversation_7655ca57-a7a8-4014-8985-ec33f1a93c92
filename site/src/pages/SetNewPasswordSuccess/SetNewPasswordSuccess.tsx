import { But<PERSON>, <PERSON>ing, Image, Text } from "@/components";
import { useTranslation } from "react-i18next";
import { ArrowRightIcon } from "@heroicons/react/24/outline";
import logo from "@/assets/logo.svg";
import passwordChangedImage from "@/assets/cadastroRedes.svg";

export type SetNewPasswordSuccessProps = {
  onClick: () => void;
};

export function SetNewPasswordSuccess({ onClick }: SetNewPasswordSuccessProps) {
  const { t } = useTranslation();

  return (
    <div
      data-testid="set-new-password-success"
      className="flex h-screen place-items-center justify-center px-4 overflow-x-hidden before:content-[''] before:opacity-35 before:blur-3xl before:fixed before:top-[0] before:left-[-20%] before:w-[2000px] before:h-[2000px] before:rounded-full before:block before:z-[-2] before:bg-amber-100 before:border-solid before:border-[200px] before:border-amber-50 before:opacity-80
after:blur-3xl after:content-[''] after:fixed after:top-[180px] after:right-[-35%] after:w-[2000px] after:h-[2000px] after:rounded-full after:block after:z-[-1] after:bg-slate-300 after:border-solid after:border-[200px] after:border-slate-5 after:opacity-50"
    >
      <div className="flex flex-col items-center text-center w-[30rem] sm:p-20 p-6 rounded-2xl bg-white">
        <Image src={logo} alt="Conteoodo" className="w-[10rem]" />
        <Heading size="3" className="mt-[1.5rem]">
          {t("set-new-password.phase-2.title")}
        </Heading>
        <Text className="mt-[1.5rem] leading-4">
          {t("set-new-password.phase-2.description")}
        </Text>
        <Image src={passwordChangedImage} className="mt-6" />
        <Button
          className="mt-[1.5rem] flex justify-center items-center text-xs gap-2"
          onClick={onClick}
        >
          {t("set-new-password.phase-2.goSignIn")}
          <ArrowRightIcon className="w-[1.125rem] stroke-[0.15rem]" />
        </Button>
      </div>
    </div>
  );
}

export default SetNewPasswordSuccess;
