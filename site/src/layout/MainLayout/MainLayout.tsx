import { Sidebar } from "@/components/Sidebar";
import { LoggedInNavbar } from "@/molecules/LoggedlnNavbar";
import { HTMLAttributes } from "react";

export interface MainLayoutProps extends HTMLAttributes<HTMLDivElement> {}

function MainLayout({ children, ...rest }: MainLayoutProps) {
  return (
    <div
      data-testid="MainLayout"
      className="h-full h-screen w-full overflow-hidden bg-gray-100"
      {...rest}
    >
      <div className="xl:relative flex h-screen w-full overflow-hidden">
        <aside className="hidden lg:flex z-20 h-full flex-shrink-0 flex-grow-0 flex-col transition-all">
          <Sidebar />
        </aside>

        <main className="relative flex xl:h-full w-full flex-col xl:overflow-hidden">
          <header className="z-[15]">
            <LoggedInNavbar hasLogo />
          </header>

          <section className="h-full w-full overflow-hidden transition-all">
            <div className="relative h-full w-full overflow-x-hidden overflow-y-scroll">
              <div className="flex flex-col xl:flex-row h-full w-full overflow-hidden">
                <div className="w-full xh-full overflow-y-auto scrollbar-hide scrollbar-hide p-5">
                  {children}
                </div>
              </div>
            </div>
          </section>
        </main>
      </div>
    </div>
  );
}

export default MainLayout;
