// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<SecondaryLayout /> should render the page 1`] = `
<div
  class="h-full h-screen w-full overflow-hidden bg-gray-100"
  data-testid="SecondaryLayout"
>
  <div
    class="xl:relative flex h-screen w-full overflow-hidden"
  >
    <aside
      class="hidden lg:flex z-20 flex h-full flex-shrink-0 flex-grow-0 flex-col transition-all"
    >
      <div
        class="relative"
        data-testid="sidebar"
      >
        <nav
          class="flex flex-col flex-auto items-start gap-4 p-6 h-full w-64 flex-shrink-0 min-h-screen antialiased bg-white border-r border-gray-100"
          data-testid="opened-sidebar"
        >
          <div
            class="flex flex-col gap-4 justify-center items-center"
          >
            <a
              class="h-7 mb-2"
              href="/"
            >
              <img
                alt="Logo"
                class="block h-full w-auto"
                src=""
              />
            </a>
            <div
              class="block lg:hidden flex w-full gap-2 items-center"
            >
              <img
                class="rounded-full w-8 h-8"
                loading="lazy"
                src="https://tecdn.b-cdn.net/img/new/avatars/2.jpg"
                style="max-width: none;"
              />
              <p
                class="font-outfit  text-xs capitalize text-black-900"
              >
                Bruno Sousa
              </p>
            </div>
          </div>
          <span
            class="w-full border-b border-gray-100"
          />
          <button
            class="w-full py-3 px-4 rounded-md font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-xs flex justify-center items-center"
          >
            components.Sidebar.text
            <svg
              aria-hidden="true"
              class="w-8 h-5"
              data-slot="icon"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 4.5v15m7.5-7.5h-15"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
          <span
            class="w-full border-b border-gray-100"
          />
          <div
            class="w-full"
          >
            <nav
              class="flex flex-col gap-2 "
            >
              <a
                class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                href="/"
              >
                <svg
                  aria-hidden="true"
                  class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class=" text-xs tracking-wide truncate text-black-900"
                >
                  components.Sidebar.labels.home
                </span>
              </a>
              <a
                class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                href="/"
              >
                <svg
                  aria-hidden="true"
                  class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535M10.34 6.66a23.847 23.847 0 0 0 8.835-2.535m0 0A23.74 23.74 0 0 0 18.795 3m.38 1.125a23.91 23.91 0 0 1 1.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 0 0 1.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 0 1 0 3.46"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class=" text-xs tracking-wide truncate text-black-900"
                >
                  components.Sidebar.labels.megaphone
                </span>
              </a>
              <a
                class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                href="/"
              >
                <svg
                  aria-hidden="true"
                  class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class=" text-xs tracking-wide truncate text-black-900"
                >
                  components.Sidebar.labels.calendarDays
                </span>
              </a>
              <a
                class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                href="/"
              >
                <svg
                  aria-hidden="true"
                  class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M13.5 21v-7.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349M3.75 21V9.349m0 0a3.001 3.001 0 0 0 3.75-.615A2.993 2.993 0 0 0 9.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 0 0 2.25 1.016c.896 0 1.7-.393 2.25-1.015a3.001 3.001 0 0 0 3.75.614m-16.5 0a3.004 3.004 0 0 1-.621-4.72l1.189-1.19A1.5 1.5 0 0 1 5.378 3h13.243a1.5 1.5 0 0 1 1.06.44l1.19 1.189a3 3 0 0 1-.621 4.72M6.75 18h3.75a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75H6.75a.75.75 0 0 0-.75.75v3.75c0 .414.336.75.75.75Z"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class=" text-xs tracking-wide truncate text-black-900"
                >
                  components.Sidebar.labels.buildingStorefront
                </span>
              </a>
              <a
                class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                href="/"
              >
                <svg
                  aria-hidden="true"
                  class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class=" text-xs tracking-wide truncate text-black-900"
                >
                  components.Sidebar.labels.users
                </span>
              </a>
              <a
                class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                href="/"
              >
                <svg
                  aria-hidden="true"
                  class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M3.75 3v11.25A2.25 2.25 0 0 0 6 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0 1 18 16.5h-2.25m-7.5 0h7.5m-7.5 0-1 3m8.5-3 1 3m0 0 .5 1.5m-.5-1.5h-9.5m0 0-.5 1.5M9 11.25v1.5M12 9v3.75m3-6v6"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class=" text-xs tracking-wide truncate text-black-900"
                >
                  components.Sidebar.labels.presentationChartBar
                </span>
              </a>
              <span
                class="flex items-center justify-center w-[14.5rem] h-[0.0625rem] border-b border-b-neutral-50"
              />
              <a
                class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300"
                href="/"
              >
                <svg
                  aria-hidden="true"
                  class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5 "
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class=" text-xs tracking-wide truncate text-black-900"
                >
                  components.Sidebar.labels.user
                </span>
              </a>
              <a
                class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300"
                href="/"
              >
                <svg
                  aria-hidden="true"
                  class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5 "
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class=" text-xs tracking-wide truncate text-black-900"
                >
                  components.Sidebar.labels.lockClose
                </span>
              </a>
              <a
                class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300"
                href="/"
              >
                <svg
                  aria-hidden="true"
                  class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5 "
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class=" text-xs tracking-wide truncate text-black-900"
                >
                  components.Sidebar.labels.creditCar
                </span>
              </a>
              <a
                class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300"
                href="/"
              >
                <svg
                  aria-hidden="true"
                  class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5 "
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span
                  class=" text-xs tracking-wide truncate text-black-900"
                >
                  components.Sidebar.labels.cog6Tooth
                </span>
              </a>
            </nav>
          </div>
        </nav>
        <button
          class="absolute bottom-4 right-4 flex justify-end w-full"
          data-testid="left-arrow"
        >
          <svg
            aria-hidden="true"
            class="w-4 h-4 transition ease-in-out delay-150 duration-300 hover:-translate-x-1 hover:text-black-900 text-yellow-900 cursor-pointer"
            data-slot="icon"
            fill="none"
            stroke="currentColor"
            stroke-width="1.5"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
    </aside>
    <main
      class="relative flex xl:h-full w-full flex-col xl:overflow-hidden"
    >
      <header
        class="z-[15]"
      >
        <nav
          class="flex justify-between items-center bg-white w-full h-14 py-2 px-4 border-b border-gray-100 z-10 grid-area-header"
          data-testid="loggedlnNavbar"
        >
          <div
            class="fixed w-full h-full top-0 left-0 z-20 bg-black-900 opacity-0 block pointer-events-none animate-delayedDisplayNone transition-all duration-300"
          />
          <div
            class="flex justify-between items-center gap-8"
          >
            <a
              class="h-12"
              href="/"
            >
              <img
                class="block lg:hidden"
                src=""
              />
            </a>
            <div
              class="hidden lg:block"
            >
              breadcrumbs here
            </div>
          </div>
          <div
            class="hidden lg:block"
          >
            search here
          </div>
          <div
            class="flex justify-between items-center gap-8"
          >
            <a
              class="w-auto h-10 py-3 px-4 rounded-md font-outfit text-xs text-black-900 tracking-[0.0625rem] bg-blue-100 hover:bg-yellow-900 hover:text-white duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed hidden sm:flex items-center truncate"
              href="/"
            >
              <svg
                aria-hidden="true"
                class="mr-2 w-6 h-6"
                data-slot="icon"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M16.5 6v.75m0 3v.75m0 3v.75m0 3V18m-9-5.25h5.25M7.5 15h3M3.375 5.25c-.621 0-1.125.504-1.125 1.125v3.026a2.999 2.999 0 0 1 0 5.198v3.026c0 .621.504 1.125 1.125 1.125h17.25c.621 0 1.125-.504 1.125-1.125v-3.026a2.999 2.999 0 0 1 0-5.198V6.375c0-.621-.504-1.125-1.125-1.125H3.375Z"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              <span
                class="hidden lg:inline"
              >
                10 Créditos
              </span>
              <span
                class="lg:hidden"
              >
                10
              </span>
            </a>
            <button
              class="lg:hidden p-4"
              role="button"
            >
              <svg
                aria-hidden="true"
                class="w-6 h-6"
                data-slot="icon"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
            <button
              class="w-10 h-10 flex justify-center items-center"
              id="dropdownMenuButton1"
              role="button"
            >
              <svg
                aria-hidden="true"
                class="w-6 h-6"
                data-slot="icon"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              <span
                class="absolute -mt-9 ml-3.5 rounded-full bg-danger px-[0.45em] py-[0.15em] text-[0.6rem] font-bold leading-none text-white"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 8 8"
                  width="8"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle
                    cx="4"
                    cy="4"
                    fill="#D63649"
                    r="4"
                  />
                </svg>
              </span>
            </button>
            <button
              class="hidden lg:block"
              role="button"
            >
              <img
                class="rounded-full w-8 h-8"
                src="https://tecdn.b-cdn.net/img/new/avatars/2.jpg"
              />
            </button>
            <button
              class="flex w-10 h-10 justify-center items-center lg:hidden"
            >
              <svg
                aria-hidden="true"
                class="w-6 h-6"
                data-slot="icon"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
          </div>
          <div
            class="block fixed top-0 left-0 w-[20.5rem] h-full bg-white borber-r border-gray-100 lg:hidden z-50 duration-200 transition-all ease-out -translate-x-full"
          >
            <section
              class="relative flex flex-col"
            >
              <button
                class="flex w-10 h-10 justify-center items-center lg:hidden absolute right-0 top-5 z-20 hover:rotate-90 transition-all duration-900"
              >
                <svg
                  aria-hidden="true"
                  class="w-6 h-6"
                  data-slot="icon"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6 18 18 6M6 6l12 12"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
              <div
                class="relative"
                data-testid="sidebar"
              >
                <nav
                  class="flex flex-col flex-auto items-start gap-4 p-6 h-full w-64 flex-shrink-0 min-h-screen antialiased bg-white border-r border-gray-100"
                  data-testid="opened-sidebar"
                >
                  <div
                    class="flex flex-col gap-4 justify-center items-center"
                  >
                    <a
                      class="h-7 mb-2"
                      href="/"
                    >
                      <img
                        alt="Logo"
                        class="block h-full w-auto"
                        src=""
                      />
                    </a>
                    <div
                      class="block lg:hidden flex w-full gap-2 items-center"
                    >
                      <img
                        class="rounded-full w-8 h-8"
                        loading="lazy"
                        src="https://tecdn.b-cdn.net/img/new/avatars/2.jpg"
                        style="max-width: none;"
                      />
                      <p
                        class="font-outfit  text-xs capitalize text-black-900"
                      >
                        Bruno Sousa
                      </p>
                    </div>
                  </div>
                  <span
                    class="w-full border-b border-gray-100"
                  />
                  <button
                    class="w-full py-3 px-4 rounded-md font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-xs flex justify-center items-center"
                  >
                    components.Sidebar.text
                    <svg
                      aria-hidden="true"
                      class="w-8 h-5"
                      data-slot="icon"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="1.5"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M12 4.5v15m7.5-7.5h-15"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </button>
                  <span
                    class="w-full border-b border-gray-100"
                  />
                  <div
                    class="w-full"
                  >
                    <nav
                      class="flex flex-col gap-2 "
                    >
                      <a
                        class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                        href="/"
                      >
                        <svg
                          aria-hidden="true"
                          class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <span
                          class=" text-xs tracking-wide truncate text-black-900"
                        >
                          components.Sidebar.labels.home
                        </span>
                      </a>
                      <a
                        class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                        href="/"
                      >
                        <svg
                          aria-hidden="true"
                          class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535M10.34 6.66a23.847 23.847 0 0 0 8.835-2.535m0 0A23.74 23.74 0 0 0 18.795 3m.38 1.125a23.91 23.91 0 0 1 1.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 0 0 1.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 0 1 0 3.46"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <span
                          class=" text-xs tracking-wide truncate text-black-900"
                        >
                          components.Sidebar.labels.megaphone
                        </span>
                      </a>
                      <a
                        class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                        href="/"
                      >
                        <svg
                          aria-hidden="true"
                          class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <span
                          class=" text-xs tracking-wide truncate text-black-900"
                        >
                          components.Sidebar.labels.calendarDays
                        </span>
                      </a>
                      <a
                        class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                        href="/"
                      >
                        <svg
                          aria-hidden="true"
                          class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M13.5 21v-7.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349M3.75 21V9.349m0 0a3.001 3.001 0 0 0 3.75-.615A2.993 2.993 0 0 0 9.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 0 0 2.25 1.016c.896 0 1.7-.393 2.25-1.015a3.001 3.001 0 0 0 3.75.614m-16.5 0a3.004 3.004 0 0 1-.621-4.72l1.189-1.19A1.5 1.5 0 0 1 5.378 3h13.243a1.5 1.5 0 0 1 1.06.44l1.19 1.189a3 3 0 0 1-.621 4.72M6.75 18h3.75a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75H6.75a.75.75 0 0 0-.75.75v3.75c0 .414.336.75.75.75Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <span
                          class=" text-xs tracking-wide truncate text-black-900"
                        >
                          components.Sidebar.labels.buildingStorefront
                        </span>
                      </a>
                      <a
                        class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                        href="/"
                      >
                        <svg
                          aria-hidden="true"
                          class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <span
                          class=" text-xs tracking-wide truncate text-black-900"
                        >
                          components.Sidebar.labels.users
                        </span>
                      </a>
                      <a
                        class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                        href="/"
                      >
                        <svg
                          aria-hidden="true"
                          class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5"
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M3.75 3v11.25A2.25 2.25 0 0 0 6 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0 1 18 16.5h-2.25m-7.5 0h7.5m-7.5 0-1 3m8.5-3 1 3m0 0 .5 1.5m-.5-1.5h-9.5m0 0-.5 1.5M9 11.25v1.5M12 9v3.75m3-6v6"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <span
                          class=" text-xs tracking-wide truncate text-black-900"
                        >
                          components.Sidebar.labels.presentationChartBar
                        </span>
                      </a>
                      <span
                        class="flex items-center justify-center w-[14.5rem] h-[0.0625rem] border-b border-b-neutral-50"
                      />
                      <a
                        class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300"
                        href="/"
                      >
                        <svg
                          aria-hidden="true"
                          class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5 "
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <span
                          class=" text-xs tracking-wide truncate text-black-900"
                        >
                          components.Sidebar.labels.user
                        </span>
                      </a>
                      <a
                        class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300"
                        href="/"
                      >
                        <svg
                          aria-hidden="true"
                          class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5 "
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <span
                          class=" text-xs tracking-wide truncate text-black-900"
                        >
                          components.Sidebar.labels.lockClose
                        </span>
                      </a>
                      <a
                        class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300"
                        href="/"
                      >
                        <svg
                          aria-hidden="true"
                          class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5 "
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <span
                          class=" text-xs tracking-wide truncate text-black-900"
                        >
                          components.Sidebar.labels.creditCar
                        </span>
                      </a>
                      <a
                        class="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300"
                        href="/"
                      >
                        <svg
                          aria-hidden="true"
                          class="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5 "
                          data-slot="icon"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="1.5"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                          <path
                            d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <span
                          class=" text-xs tracking-wide truncate text-black-900"
                        >
                          components.Sidebar.labels.cog6Tooth
                        </span>
                      </a>
                    </nav>
                  </div>
                </nav>
                <button
                  class="absolute bottom-4 right-4 flex justify-end w-full"
                  data-testid="left-arrow"
                >
                  <svg
                    aria-hidden="true"
                    class="w-4 h-4 transition ease-in-out delay-150 duration-300 hover:-translate-x-1 hover:text-black-900 text-yellow-900 cursor-pointer"
                    data-slot="icon"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="1.5"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </button>
              </div>
            </section>
          </div>
        </nav>
      </header>
      <section
        class="h-full w-full overflow-hidden transition-all"
      >
        <div
          class="relative h-full w-full overflow-x-hidden overflow-y-scroll"
        >
          <div
            class="flex flex-col xl:flex-row h-full w-full overflow-hidden"
          >
            <div
              class="w-full xh-full overflow-y-auto scrollbar-hide scrollbar-hide p-5 xl:mr-64"
            >
              <aside
                class="block xl:hidden w-full xl:w-64 xl:fixed xl:right-0 z-[5] xl:h-full xl:overflow-x-hidden xl:overflow-y-scroll bg-transparent xl:bg-white px-6 xl:px-4 pt-4 pb-20 scrollbar-hide"
              />
            </div>
            <aside
              class="hidden xl:block w-full xl:w-64 xl:fixed xl:right-0 z-[5] xl:h-full xl:overflow-x-hidden xl:overflow-y-scroll bg-transparent xl:bg-white px-6 xl:px-4 pt-4 pb-20 scrollbar-hide"
            />
          </div>
        </div>
      </section>
    </main>
  </div>
</div>
`;
