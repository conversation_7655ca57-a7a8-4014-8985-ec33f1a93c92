import { Sidebar } from "@/components/Sidebar";
import { LoggedInNavbar } from "@/molecules/LoggedlnNavbar";
import { HTMLAttributes, ReactNode } from "react";

export interface SecondaryLayoutProps extends HTMLAttributes<HTMLDivElement> {
  rightChildren: ReactNode;
}

function SecondaryLayout({
  children,
  rightChildren,
  ...rest
}: SecondaryLayoutProps) {
  return (
    <div
      data-testid="SecondaryLayout"
      className="h-full h-screen w-full overflow-hidden bg-gray-100"
      {...rest}
    >
      <div className="xl:relative flex h-screen w-full overflow-hidden">
        <aside className="hidden lg:flex z-20 flex h-full flex-shrink-0 flex-grow-0 flex-col transition-all">
          <Sidebar />
        </aside>

        <main className="relative flex xl:h-full w-full flex-col xl:overflow-hidden">
          <header className="z-[15]">
            <LoggedInNavbar hasLogo />
          </header>

          <section className="h-full w-full overflow-hidden transition-all">
            <div className="relative h-full w-full overflow-x-hidden overflow-y-scroll">
              <div className="flex flex-col xl:flex-row h-full w-full overflow-hidden">
                <div className="w-full xh-full overflow-y-auto scrollbar-hide scrollbar-hide p-5 xl:mr-64">
                  {children}

                  <aside className="block xl:hidden w-full xl:w-64 xl:fixed xl:right-0 z-[5] xl:h-full xl:overflow-x-hidden xl:overflow-y-scroll bg-transparent xl:bg-white px-6 xl:px-4 pt-4 pb-20 scrollbar-hide">
                    {rightChildren}
                  </aside>
                </div>

                <aside className="hidden xl:block w-full xl:w-64 xl:fixed xl:right-0 z-[5] xl:h-full xl:overflow-x-hidden xl:overflow-y-scroll bg-transparent xl:bg-white px-6 xl:px-4 pt-4 pb-20 scrollbar-hide">
                  {rightChildren}
                </aside>
              </div>
            </div>
          </section>
        </main>
      </div>
    </div>
  );
}

export default SecondaryLayout;
