import React from "react";
import ReactD<PERSON> from "react-dom/client";
import { create<PERSON><PERSON>er<PERSON><PERSON>er, RouterProvider } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import {
  CampaignListContainer,
  CheckoutContainer,
  ClientAccountsContainer,
  ConfirmAccountContainer,
  CreateCampaignContainer,
  CreateClientContainer,
  DashboardContainer,
  HomeContainer,
  ResetPasswordContainer,
  ResetPasswordConfirmContainer,
  SetNewPasswordContainer,
  SetNewPasswordSuccessContainer,
  SignInContainer,
  SignInWithEmailContainer,
  SignUpContainer,
  SignUpWithEmailContainer,
  SignUpWithEmail2Container,
} from "@/pages";

import "./i18n";
import "./index.css";

const router = createBrowserRouter([
  {
    path: "/",
    element: <HomeContainer />,
  },
  {
    path: "/sign-up",
    element: <SignUpContainer />,
  },
  {
    path: "/sign-up-with-email",
    element: <SignUpWithEmailContainer />,
  },
  {
    path: "/sign-up-with-email2",
    element: <SignUpWithEmail2Container />,
  },
  {
    path: "/confirm-account",
    element: <ConfirmAccountContainer />,
  },
  {
    path: "/sign-in",
    element: <SignInContainer />,
  },
  {
    path: "/sign-in-with-email",
    element: <SignInWithEmailContainer />,
  },
  {
    path: "/reset-password",
    element: <ResetPasswordContainer />,
  },
  {
    path: "/reset-password-confirm",
    element: <ResetPasswordConfirmContainer />,
  },
  {
    path: "/set-new-password",
    element: <SetNewPasswordContainer />,
  },
  {
    path: "/set-new-password-success",
    element: <SetNewPasswordSuccessContainer />,
  },
  {
    path: "/Dashboard",
    element: <DashboardContainer />,
  },
  {
    path: "/checkout",
    element: <CheckoutContainer />,
  },
  {
    path: "/clients",
    element: <ClientAccountsContainer />,
  },
  {
    path: "/campaign-list",
    element: <CampaignListContainer />,
  },
  {
    path: "/createclient",
    element: <CreateClientContainer />,
  },
  {
    path: "/create-campaign",
    element: <CreateCampaignContainer />,
  },
]);

const queryClient = new QueryClient();

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <RouterProvider router={router} />
    </QueryClientProvider>
  </React.StrictMode>,
);
