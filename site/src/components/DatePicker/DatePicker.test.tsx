import { render, screen, fireEvent } from "@tests/render";

import DatePicker from "./DatePicker";

describe("<DatePicker />", () => {
  it("should render the DatePicker input", () => {
    render(<DatePicker defaultDate={new Date(2023, 2, 1)} />);
    expect(screen.getByTestId("datepicker")).toMatchSnapshot();
  });

  it("should render the DatePicker with gray input", () => {
    render(
      <DatePicker variant="secondary" defaultDate={new Date(2023, 2, 1)} />,
    );
    expect(screen.getByTestId("datepicker")).toMatchSnapshot();
  });

  it("should render the DatePicker with gray input", () => {
    render(
      <DatePicker variant="tertiary" defaultDate={new Date(2024, 3, 10)} />,
    );
    expect(screen.getByTestId("datepicker")).toMatchSnapshot();
  });

  it("should render calendar when clincking on the DatePicker input", () => {
    const { container } = render(
      <DatePicker defaultDate={new Date(2023, 2, 1)} />,
    );
    fireEvent.focusIn(screen.getByTestId("datepicker"));
    expect(
      container.getElementsByClassName("absolute top-10"),
    ).toMatchSnapshot();
  });
});
