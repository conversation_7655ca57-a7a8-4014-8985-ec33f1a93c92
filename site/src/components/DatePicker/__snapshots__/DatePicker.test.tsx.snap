// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<DatePicker /> should render calendar when clincking on the DatePicker input 1`] = `
HTMLCollection [
  <div
    class="absolute top-10 z-50 block lg:pt-2 lg:min-w-[25rem] min-w-[20rem]"
  >
    <div
      class="inline-block rounded-xl bg-white lg:p-6 p-4 max-lg:pb-2 border border-gray-700"
    >
      <div
        class=""
      >
        <div
          class="flex justify-between lg:mb-6 mb-2"
        >
          <button
            class="text-sm rounded-lg text-black-900 bg-white font-semibold px-[1.11rem] hover:bg-amber-300 focus:outline-none view-switch"
            type="button"
          >
            <svg
              aria-hidden="true"
              fill="currentColor"
              height="1em"
              stroke="currentColor"
              stroke-width="0"
              viewBox="0 0 20 20"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
                fill-rule="evenodd"
              />
            </svg>
          </button>
          <button
            class="text-sm rounded-lg text-black-900 bg-white font-semibold px-[1.11rem] hover:bg-amber-300 focus:outline-none view-switch"
            type="button"
          >
            March 2023
          </button>
          <button
            class="text-sm rounded-lg text-black-900 bg-white font-semibold px-[1.11rem] hover:bg-amber-300 focus:outline-none view-switch"
            type="button"
          >
            <svg
              aria-hidden="true"
              fill="currentColor"
              height="1em"
              stroke="currentColor"
              stroke-width="0"
              viewBox="0 0 20 20"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                fill-rule="evenodd"
              />
            </svg>
          </button>
        </div>
      </div>
      <div
        class=""
      >
        <div
          class="grid grid-cols-7"
        >
          <span
            class="dow h-6 uppercase text-center text-[0.625rem] font-semibold leading-6 text-gray-800"
          >
            Sun
          </span>
          <span
            class="dow h-6 uppercase text-center text-[0.625rem] font-semibold leading-6 text-gray-800"
          >
            Mon
          </span>
          <span
            class="dow h-6 uppercase text-center text-[0.625rem] font-semibold leading-6 text-gray-800"
          >
            Tue
          </span>
          <span
            class="dow h-6 uppercase text-center text-[0.625rem] font-semibold leading-6 text-gray-800"
          >
            Wed
          </span>
          <span
            class="dow h-6 uppercase text-center text-[0.625rem] font-semibold leading-6 text-gray-800"
          >
            Thu
          </span>
          <span
            class="dow h-6 uppercase text-center text-[0.625rem] font-semibold leading-6 text-gray-800"
          >
            Fri
          </span>
          <span
            class="dow h-6 uppercase text-center text-[0.625rem] font-semibold leading-6 text-gray-800"
          >
            Sat
          </span>
        </div>
        <div
          class="grid grid-cols-7"
        >
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            26
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            27
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            28
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md text-center text-sm font-medium lg:leading-4 text-black-900 lg:m-1 bg-yellow-800 border border-black-900 hover:bg-amber-300"
            type="button"
          >
            1
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            2
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            3
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            4
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            5
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            6
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            7
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            8
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            9
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            10
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            11
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            12
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            13
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            14
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            15
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            16
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            17
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            18
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            19
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            20
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            21
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            22
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            23
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            24
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            25
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            26
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            27
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            28
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            29
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            30
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            31
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            1
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            2
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            3
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            4
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            5
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            6
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            7
          </button>
          <button
            class="block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1"
            type="button"
          >
            8
          </button>
        </div>
      </div>
    </div>
  </div>,
]
`;

exports[`<DatePicker /> should render the DatePicker input 1`] = `
<input
  class="border border-gray-700 w-full font-rubik tracking-[-0.035rem] focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none bg-white p-2.5 text-sm pl-10 rounded-lg"
  data-testid="datepicker"
  placeholder="Select a Date"
  readonly=""
  value="March 1, 2023"
/>
`;

exports[`<DatePicker /> should render the DatePicker with gray input 1`] = `
<input
  class="border-none w-full font-rubik tracking-[-0.035rem] focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none bg-gray-100 p-2.5 text-sm pl-10 rounded-lg"
  data-testid="datepicker"
  placeholder="Select a Date"
  readonly=""
  value="March 1, 2023"
/>
`;

exports[`<DatePicker /> should render the DatePicker with gray input 2`] = `
<input
  class="border border-gray-700 w-full font-rubik tracking-[-0.035rem] focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none bg-white p-2.5 text-sm pl-10 rounded-lg"
  data-testid="datepicker"
  placeholder="Select a Date"
  readonly=""
  value="April 10, 2024"
/>
`;
