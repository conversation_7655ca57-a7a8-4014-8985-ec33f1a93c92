import i18n from "@/i18n";
import { Datepicker, DatepickerProps } from "flowbite-react";

const theme: DatepickerProps["theme"] = {
  root: {
    base: "relative",
    input: {
      field: {
        input: {
          base: "bg-white border border-gray-700 w-full font-rubik tracking-[-0.035rem] focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none",
          colors: {
            gray: "bg-white",
          },
        },
      },
    },
  },
  popup: {
    root: {
      base: "absolute top-10 z-50 block lg:pt-2 lg:min-w-[25rem] min-w-[20rem]",
      inline: "relative top-0 z-auto",
      inner:
        "inline-block rounded-xl bg-white lg:p-6 p-4 max-lg:pb-2 border border-gray-700",
    },
    header: {
      base: "",
      title: "text-center font-semibold text-black-900",
      selectors: {
        base: "flex justify-between lg:mb-6 mb-2",
        button: {
          base: "text-sm rounded-lg text-black-900 bg-white font-semibold px-5 px-[1.11rem] hover:bg-amber-300 focus:outline-none view-switch",
          prev: "",
          next: "",
          view: "",
        },
      },
    },
    view: {
      base: "",
    },
  },
  views: {
    days: {
      header: {
        base: "grid grid-cols-7",
        title:
          "dow h-6 uppercase text-center text-[0.625rem] font-semibold leading-6 text-gray-800",
      },
      items: {
        base: "grid grid-cols-7",
        item: {
          base: "block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-1",
          selected: "bg-yellow-800 border border-black-900 hover:bg-amber-300",
          disabled: "text-gray-800",
        },
      },
    },
    months: {
      items: {
        base: "grid grid-cols-4",
        item: {
          base: "block flex-1 cursor-pointer rounded-md border border-transparent text-center text-sm font-semibold leading-9 text-black-900 hover:bg-amber-300 m-1",
          selected: "bg-yellow-800 border border-black-900 hover:bg-amber-300",
          disabled: "text-gray-800",
        },
      },
    },
    years: {
      items: {
        base: "grid w-64 grid-cols-4",
        item: {
          base: "block flex-1 cursor-pointer rounded-md border border-transparent text-center text-sm font-semibold lg:leading-9 leading-7 text-black-900 hover:bg-amber-300 lg:m-1",
          selected: "bg-yellow-800 border border-black-900 hover:bg-amber-300",
          disabled: "text-gray-800",
        },
      },
    },
    decades: {
      items: {
        base: "grid w-64 grid-cols-4",
        item: {
          base: "block flex-1 cursor-pointer rounded-md border border-transparent text-center text-sm font-semibold leading-9 text-black-900 hover:bg-amber-300 m-1",
          selected: "bg-yellow-800 border border-black-900 hover:bg-amber-300",
          disabled: "text-gray-800",
        },
      },
    },
  },
};

const grayInput: DatePickerProps["theme"] = {
  root: {
    base: "relative rounded-lg",
    input: {
      field: {
        input: {
          base: "bg-gray-100 border-none w-full font-rubik tracking-[-0.035rem] focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none",
          colors: {
            gray: "bg-gray-100",
          },
        },
      },
    },
  },
};

const CalendarMedium: DatePickerProps["theme"] = {
  root: {
    base: "relative",
    input: {
      field: {
        input: {
          base: "bg-white border border-gray-700 w-full font-rubik tracking-[-0.035rem] focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none",
          colors: {
            gray: "bg-white",
          },
        },
      },
    },
  },
  popup: {
    root: {
      base: "relative top-1 z-50 block lg:pt-2 lg:min-w-[16.75rem]",
      inline: "relative top-0 z-auto",
      inner:
        "inline-block rounded-xl bg-white p-6 p-4 max-lg:pb-2 border border-gray-700",
    },
    header: {
      base: "",
      title: "text-center font-semibold text-black-900",
      selectors: {
        base: "flex justify-between lg:mb-6 mb-2",
        button: {
          base: "text-sm rounded-lg text-black-900 bg-white font-semibold px-5 px-[1.11rem] hover:bg-amber-300 focus:outline-none view-switch",
          prev: "",
          next: "",
          view: "",
        },
      },
    },
    view: {
      base: "",
    },
  },
  views: {
    days: {
      header: {
        base: "grid grid-cols-7",
        title:
          "dow h-6 uppercase text-center text-[0.625rem] font-semibold leading-6 text-gray-800",
      },
      items: {
        base: "grid grid-cols-7",
        item: {
          base: "block flex-1 lg:px-2 p-2 cursor-pointer rounded-md border border-transparent text-center text-sm font-medium lg:leading-4 text-black-900 hover:bg-amber-300 lg:m-0",
          selected: "bg-yellow-800 border border-black-900 hover:bg-amber-300",
          disabled: "text-gray-800",
        },
      },
    },
    months: {
      items: {
        base: "grid grid-cols-4",
        item: {
          base: "block flex-1 cursor-pointer rounded-md border border-transparent text-center text-sm font-semibold leading-9 text-black-900 hover:bg-amber-300 m-1",
          selected: "bg-yellow-800 border border-black-900 hover:bg-amber-300",
          disabled: "text-gray-800",
        },
      },
    },
    years: {
      items: {
        base: "grid w-64 grid-cols-4",
        item: {
          base: "block flex-1 cursor-pointer rounded-md border border-transparent text-center text-sm font-semibold lg:leading-9 text-black-900 hover:bg-amber-300 lg:m-1",
          selected: "bg-yellow-800 border border-black-900 hover:bg-amber-300",
          disabled: "text-gray-800",
        },
      },
    },
    decades: {
      items: {
        base: "grid w-64 grid-cols-4",
        item: {
          base: "block flex-1 cursor-pointer rounded-md border border-transparent text-center text-sm font-semibold leading-9 text-black-900 hover:bg-amber-300 m-1",
          selected: "bg-yellow-800 border border-black-900 hover:bg-amber-300",
          disabled: "text-gray-800",
        },
      },
    },
  },
};

export interface DatePickerProps extends DatepickerProps {
  variant?: "primary" | "secondary" | "tertiary";
}

export function DatePicker({
  showClearButton = false,
  weekStart = 0,
  showTodayButton = false,
  placeholder = "Select a Date",
  className = "font-outfit",
  variant = "primary",
  ...rest
}: DatePickerProps) {
  return (
    <Datepicker
      data-testid="datepicker"
      weekStart={weekStart}
      showClearButton={showClearButton}
      showTodayButton={showTodayButton}
      placeholder={placeholder}
      className={className}
      language={i18n.language === "pt-PT" ? "pt-BR" : i18n.language}
      icon={() => (
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M8.00016 12.6667C8.13202 12.6667 8.26091 12.6276 8.37054 12.5543C8.48018 12.4811 8.56562 12.3769 8.61608 12.2551C8.66654 12.1333 8.67974 11.9993 8.65402 11.8699C8.6283 11.7406 8.5648 11.6218 8.47157 11.5286C8.37833 11.4354 8.25954 11.3719 8.13022 11.3461C8.0009 11.3204 7.86686 11.3336 7.74504 11.3841C7.62322 11.4345 7.5191 11.52 7.44585 11.6296C7.3726 11.7392 7.3335 11.8681 7.3335 12C7.3335 12.1768 7.40373 12.3464 7.52876 12.4714C7.65378 12.5964 7.82335 12.6667 8.00016 12.6667ZM11.3335 12.6667C11.4654 12.6667 11.5942 12.6276 11.7039 12.5543C11.8135 12.4811 11.899 12.3769 11.9494 12.2551C11.9999 12.1333 12.0131 11.9993 11.9874 11.8699C11.9616 11.7406 11.8981 11.6218 11.8049 11.5286C11.7117 11.4354 11.5929 11.3719 11.4636 11.3461C11.3342 11.3204 11.2002 11.3336 11.0784 11.3841C10.9566 11.4345 10.8524 11.52 10.7792 11.6296C10.7059 11.7392 10.6668 11.8681 10.6668 12C10.6668 12.1768 10.7371 12.3464 10.8621 12.4714C10.9871 12.5964 11.1567 12.6667 11.3335 12.6667ZM11.3335 10C11.4654 10 11.5942 9.9609 11.7039 9.88764C11.8135 9.81439 11.899 9.71027 11.9494 9.58845C11.9999 9.46664 12.0131 9.33259 11.9874 9.20327C11.9616 9.07395 11.8981 8.95516 11.8049 8.86193C11.7117 8.76869 11.5929 8.7052 11.4636 8.67947C11.3342 8.65375 11.2002 8.66695 11.0784 8.71741C10.9566 8.76787 10.8524 8.85332 10.7792 8.96295C10.7059 9.07258 10.6668 9.20148 10.6668 9.33333C10.6668 9.51014 10.7371 9.67971 10.8621 9.80473C10.9871 9.92976 11.1567 10 11.3335 10ZM8.00016 10C8.13202 10 8.26091 9.9609 8.37054 9.88764C8.48018 9.81439 8.56562 9.71027 8.61608 9.58845C8.66654 9.46664 8.67974 9.33259 8.65402 9.20327C8.6283 9.07395 8.5648 8.95516 8.47157 8.86193C8.37833 8.76869 8.25954 8.7052 8.13022 8.67947C8.0009 8.65375 7.86686 8.66695 7.74504 8.71741C7.62322 8.76787 7.5191 8.85332 7.44585 8.96295C7.3726 9.07258 7.3335 9.20148 7.3335 9.33333C7.3335 9.51014 7.40373 9.67971 7.52876 9.80473C7.65378 9.92976 7.82335 10 8.00016 10ZM12.6668 2H12.0002V1.33333C12.0002 1.15652 11.9299 0.98695 11.8049 0.861926C11.6799 0.736902 11.5103 0.666664 11.3335 0.666664C11.1567 0.666664 10.9871 0.736902 10.8621 0.861926C10.7371 0.98695 10.6668 1.15652 10.6668 1.33333V2H5.3335V1.33333C5.3335 1.15652 5.26326 0.98695 5.13823 0.861926C5.01321 0.736902 4.84364 0.666664 4.66683 0.666664C4.49002 0.666664 4.32045 0.736902 4.19543 0.861926C4.0704 0.98695 4.00016 1.15652 4.00016 1.33333V2H3.3335C2.80306 2 2.29436 2.21071 1.91928 2.58578C1.54421 2.96086 1.3335 3.46956 1.3335 4V13.3333C1.3335 13.8638 1.54421 14.3725 1.91928 14.7475C2.29436 15.1226 2.80306 15.3333 3.3335 15.3333H12.6668C13.1973 15.3333 13.706 15.1226 14.081 14.7475C14.4561 14.3725 14.6668 13.8638 14.6668 13.3333V4C14.6668 3.46956 14.4561 2.96086 14.081 2.58578C13.706 2.21071 13.1973 2 12.6668 2ZM13.3335 13.3333C13.3335 13.5101 13.2633 13.6797 13.1382 13.8047C13.0132 13.9298 12.8436 14 12.6668 14H3.3335C3.15669 14 2.98712 13.9298 2.86209 13.8047C2.73707 13.6797 2.66683 13.5101 2.66683 13.3333V7.33333H13.3335V13.3333ZM13.3335 6H2.66683V4C2.66683 3.82319 2.73707 3.65362 2.86209 3.52859C2.98712 3.40357 3.15669 3.33333 3.3335 3.33333H4.00016V4C4.00016 4.17681 4.0704 4.34638 4.19543 4.4714C4.32045 4.59643 4.49002 4.66666 4.66683 4.66666C4.84364 4.66666 5.01321 4.59643 5.13823 4.4714C5.26326 4.34638 5.3335 4.17681 5.3335 4V3.33333H10.6668V4C10.6668 4.17681 10.7371 4.34638 10.8621 4.4714C10.9871 4.59643 11.1567 4.66666 11.3335 4.66666C11.5103 4.66666 11.6799 4.59643 11.8049 4.4714C11.9299 4.34638 12.0002 4.17681 12.0002 4V3.33333H12.6668C12.8436 3.33333 13.0132 3.40357 13.1382 3.52859C13.2633 3.65362 13.3335 3.82319 13.3335 4V6ZM4.66683 10C4.79868 10 4.92758 9.9609 5.03721 9.88764C5.14684 9.81439 5.23229 9.71027 5.28275 9.58845C5.33321 9.46664 5.34641 9.33259 5.32069 9.20327C5.29496 9.07395 5.23147 8.95516 5.13823 8.86193C5.045 8.76869 4.92621 8.7052 4.79689 8.67947C4.66757 8.65375 4.53352 8.66695 4.41171 8.71741C4.28989 8.76787 4.18577 8.85332 4.11252 8.96295C4.03926 9.07258 4.00016 9.20148 4.00016 9.33333C4.00016 9.51014 4.0704 9.67971 4.19543 9.80473C4.32045 9.92976 4.49002 10 4.66683 10ZM4.66683 12.6667C4.79868 12.6667 4.92758 12.6276 5.03721 12.5543C5.14684 12.4811 5.23229 12.3769 5.28275 12.2551C5.33321 12.1333 5.34641 11.9993 5.32069 11.8699C5.29496 11.7406 5.23147 11.6218 5.13823 11.5286C5.045 11.4354 4.92621 11.3719 4.79689 11.3461C4.66757 11.3204 4.53352 11.3336 4.41171 11.3841C4.28989 11.4345 4.18577 11.52 4.11252 11.6296C4.03926 11.7392 4.00016 11.8681 4.00016 12C4.00016 12.1768 4.0704 12.3464 4.19543 12.4714C4.32045 12.5964 4.49002 12.6667 4.66683 12.6667Z"
            fill="#8B8B88"
          />
        </svg>
      )}
      theme={
        variant === "primary"
          ? theme
          : variant === "secondary"
            ? {
                root: grayInput?.root,
                popup: theme?.popup,
                views: theme?.views,
              }
            : {
                root: CalendarMedium?.root,
                popup: CalendarMedium?.popup,
                views: CalendarMedium?.views,
              }
      }
      {...rest}
    />
  );
}

export default DatePicker;
