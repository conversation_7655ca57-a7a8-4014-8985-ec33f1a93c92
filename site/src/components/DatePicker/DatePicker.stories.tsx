import { Meta, StoryObj } from "@storybook/react";
import { DatePicker, DatePickerProps } from "./DatePicker";

const story: Meta<DatePickerProps> = {
  component: DatePicker,
  title: "DatePicker",
} as Meta;

export default story;

type Story = StoryObj<DatePickerProps>;

export const Default: Story = {};

export const GrayInput: Story = {
  args: {
    variant: "secondary",
  },
};

export const CalendarMedium: Story = {
  args: {
    variant: "tertiary",
  },
};
