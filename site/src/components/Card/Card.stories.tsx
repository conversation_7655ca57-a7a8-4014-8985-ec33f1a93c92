import { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import Card, { CardProps } from "./Card";

const story: Meta<CardProps> = {
  component: Card,
  title: "Card",
} as Meta;

const CardFacebook = () => {
  return (
    <div className="flex flex-col text-center gap-2">
      <div className="flex bg-black-900 items-center justify-center rounded-full h-16 w-16">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
        >
          <path
            d="M15.12 5.32003H17V2.14003C16.0897 2.04538 15.1751 1.99865 14.26 2.00003C11.54 2.00003 9.67999 3.66003 9.67999 6.70003V9.32003H6.60999V12.88H9.67999V22H13.36V12.88H16.42L16.88 9.32003H13.36V7.05003C13.36 6.00003 13.64 5.32003 15.12 5.32003Z"
            fill="white"
          />
        </svg>
      </div>
      <p className="font-outfit text-[0.625rem] font-semibold"> Facebook </p>
    </div>
  );
};

export default story;

type Story = StoryObj<CardProps>;

export const Primary: Story = {
  args: {
    color: "primary",
    children: <CardFacebook />,
  },
};

export const Secondary: Story = {
  args: {
    color: "secondary",
    children: <CardFacebook />,
  },
};

export const Tertiary: Story = {
  args: {
    color: "tertiary",
    children: <CardFacebook />,
  },
};

export const Quaternary: Story = {
  args: {
    color: "quaternary",
    children: <CardFacebook />,
  },
};

export const Quinternary: Story = {
  args: {
    color: "quinternary",
    children: <CardFacebook />,
  },
};

export const ImageCard: Story = {
  args: {
    fullWidth: true,
    children: (
      <div className="flex flex-col w-full items-center justify-center">
        <img
          className="w-full rounded-t-[0.75rem]"
          src="https://via.placeholder.com/150"
          alt="Placeholder"
        />
        <div className="relative px-0 py-4 leading-[0.875rem] flex flex-col text-justify text-sm gap-2">
          <h2 className="top-0 font-outfit text-xs font-semibold">
            Want to Be Big
          </h2>
          <p className="absolute my-4 font-rubik font-normal text-[0.625rem]">
            Brunão Personal
          </p>
          <p className="my-4 font-rubik font-normal text-[0.625rem]">
            Disclosure of consultancy for bodybuilders
          </p>
        </div>
      </div>
    ),
  },
};

export const BadgeTopLeft: Story = {
  args: {
    children: <CardFacebook />,
    badge: {
      content: "Recomendado",
      position: "topLeft",
    },
  },
};

export const BadgeTopRight: Story = {
  args: {
    children: <CardFacebook />,
    badge: {
      content: "Recomendado",
      position: "topRight",
      variant: "primarySolid",
    },
  },
};

export const BadgeBottomRight: Story = {
  args: {
    children: <CardFacebook />,
    badge: {
      content: "Recomendado",
      position: "bottomRight",
      variant: "tertiary",
    },
  },
};

export const BadgeBottomLeft: Story = {
  args: {
    children: <CardFacebook />,
    badge: {
      content: "Recomendado",
      position: "bottomLeft",
      variant: "quaternary",
    },
  },
};
