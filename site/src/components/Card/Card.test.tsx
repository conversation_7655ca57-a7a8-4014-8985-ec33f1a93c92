import { render } from "@tests/render";
import "@testing-library/jest-dom";
import Card from "./Card";

describe("Card component", () => {
  it("renders with the default props", () => {
    const { getByText } = render(<Card>Hello, Test!</Card>);
    expect(getByText("Hello, Test!")).toBeInTheDocument();
  });

  it('renders with the "primary" color', () => {
    const { container } = render(<Card color="primary">Primary Card</Card>);
    expect(container.firstChild).toHaveClass("bg-transparent");
    expect(container.firstChild).toHaveClass("border");
    expect(container.firstChild).toHaveClass("border-gray-800");

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with the "secondary" color and checked', () => {
    const { container } = render(
      <Card color="secondary" checked>
        Secondary Card
      </Card>,
    );
    expect(container.firstChild).toHaveClass("bg-white");
    expect(container.firstChild).toHaveClass("border");

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with the "tertiary" color and checked', () => {
    const { container } = render(
      <Card color="tertiary" checked>
        Tertiary Card
      </Card>,
    );
    expect(container.firstChild).toHaveClass("bg-yellow-800");
    expect(container.firstChild).toHaveClass("border");

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with the "quaternary" color and checked', () => {
    const { container } = render(
      <Card color="quaternary" checked>
        Quarternary Card
      </Card>,
    );

    expect(container.firstChild).toHaveClass("bg-transparent");
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with the "quinternary" color and checked', () => {
    const { container } = render(
      <Card color="quinternary" checked>
        Quinternary Card
      </Card>,
    );

    expect(container.firstChild).toHaveClass("bg-white");
    expect(container.firstChild).toMatchSnapshot();
  });

  it("renders with default props", () => {
    const { container, getByAltText, getByText } = render(
      <Card>
        <div className="flex flex-col w-full items-center justify-center">
          <img
            className="w-full rounded-t-[0.75rem]"
            src="https://via.placeholder.com/150"
            alt="Placeholder"
          />
          <div className="relative px-0 py-4 leading-[0.875rem] flex flex-col text-justify text-sm gap-2">
            <h2 className="top-0 font-outfit text-xs font-semibold">
              Want to Be Big
            </h2>
            <p className="absolute my-4 font-rubik font-normal text-[0.625rem]">
              Brunão Personal
            </p>
            <p className="my-4 font-rubik font-normal text-[0.625rem]">
              Disclosure of consultancy for bodybuilders
            </p>
          </div>
        </div>
      </Card>,
    );

    expect(getByAltText("Placeholder")).toBeInTheDocument();
    expect(getByText("Want to Be Big")).toBeInTheDocument();
    expect(getByText("Brunão Personal")).toBeInTheDocument();
    expect(
      getByText("Disclosure of consultancy for bodybuilders"),
    ).toBeInTheDocument();

    expect(container.firstChild).toMatchSnapshot();
  });
});

it('renders with the "topLeft" badge', () => {
  const { container } = render(
    <Card badge={{ content: "Test", position: "topLeft" }}>Quinary Card</Card>,
  );
  expect(container.firstChild).toMatchSnapshot();
});

it('renders with the "topRight" badge', () => {
  const { container } = render(
    <Card badge={{ content: "Test", position: "topRight" }}>Quinary Card</Card>,
  );
  expect(container.firstChild).toMatchSnapshot();
});

it('renders with the "bottomRight" badge', () => {
  const { container } = render(
    <Card badge={{ content: "Test", position: "bottomRight" }}>
      Quinary Card
    </Card>,
  );
  expect(container.firstChild).toMatchSnapshot();
});

it('renders with the "bottomLeft" badge', () => {
  const { container } = render(
    <Card badge={{ content: "Test", position: "bottomLeft" }}>
      Quinary Card
    </Card>,
  );
  expect(container.firstChild).toMatchSnapshot();
});
