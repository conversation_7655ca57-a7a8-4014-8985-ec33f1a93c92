import { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>eader,
  ModalProps,
} from "./Modal";
import { useState } from "react";
import { But<PERSON> } from "..";

const story: Meta<ModalProps> = {
  component: Modal,
  title: "Modal",
} as Meta;

export default story;

type Story = StoryObj<ModalProps>;

const ModalWithHooks = (
  variant: "primary" | "secondary",
  fullWidth?: boolean,
) => {
  const [openModal, setOpenModal] = useState(false);

  return (
    <>
      <Button onClick={() => setOpenModal(true)}>Open Modal</Button>
      <Modal show={openModal} onClose={() => setOpenModal(false)} dismissible>
        <ModalHeader variant={variant} fullWidth={fullWidth}>
          Terms of Service
        </ModalHeader>
        <ModalBody>
          <p className="text-base leading-relaxed text-gray-500 dark:text-gray-400">
            With less than a month to go before the European Union enacts new
            consumer privacy laws for its citizens, companies around the world
            are updating their terms of service agreements to comply.
          </p>
        </ModalBody>
        <ModalFooter>
          <Button onClick={() => setOpenModal(false)}>I accept</Button>
        </ModalFooter>
      </Modal>
    </>
  );
};

export const Default: Story = {
  render: () => ModalWithHooks("primary"),
};

export const DifferentCloseIcon: Story = {
  render: () => ModalWithHooks("secondary"),
};

export const HeaderFullWidth: Story = {
  render: () => ModalWithHooks("secondary", true),
};
