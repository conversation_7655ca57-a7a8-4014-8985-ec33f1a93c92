import {
  Modal as FlowModal,
  ModalProps as FlowModalProps,
  ModalHeaderProps as FlowModalHeaderProps,
  ModalBodyProps as FlowModalBodyProps,
  ModalFooterProps as FlowModalFooterProps,
} from "flowbite-react";

export interface ModalProps extends FlowModalProps {}

export interface ModalHeaderProps extends FlowModalHeaderProps {
  variant?: "primary" | "secondary";
  fullWidth?: boolean;
}

export interface ModalBodyProps extends FlowModalBodyProps {}

export interface ModalFooterProps extends FlowModalFooterProps {}

const theme: FlowModalProps["theme"] = {
  root: {
    base: "fixed top-0 right-0 left-0 z-50 h-modal h-screen overflow-y-auto overflow-x-hidden md:inset-0 md:h-full",
    show: {
      on: "flex bg-gray-900 bg-opacity-50 dark:bg-opacity-80",
      off: "hidden",
    },
    sizes: {
      sm: "max-w-sm",
      md: "max-w-md",
      lg: "max-w-lg",
      xl: "max-w-xl",
    },
    positions: {
      center: "items-center justify-center",
    },
  },
  content: {
    base: "relative h-full w-full p-4 md:h-auto",
    inner:
      "relative rounded-xl bg-white dark:bg-gray-700 flex flex-col max-h-[90vh]",
  },
  body: {
    base: "p-6 flex-1 overflow-auto font-rubik",
    popup: "pt-0",
  },
  header: {
    base: "mt-1 flex flex-col justify-between p-5",
    popup: "p-2 border-b-0",
    title: "text-2xl font-bold text-black-900 font-outfit text-center w-full",
    close: {
      base: "absolute right-4 top-4 ml-auto inline-flex items-center rounded-md bg-transparent p-1.5 text-sm text-black-900",
      icon: "h-5 w-5",
    },
  },
  footer: {
    base: "flex items-center space-x-2 p-6",
    popup: "",
  },
};

const reverseColorIcon: ModalHeaderProps["theme"] = {
  base: "mt-1 flex flex-col justify-between p-5",
  popup: "p-2 border-b-0",
  title: "text-2xl font-bold text-black-900 font-outfit text-center w-full",
  close: {
    base: "absolute right-4 top-4 ml-auto inline-flex items-center rounded-md bg-black-900 p-1.5 text-sm text-white",
    icon: "h-5 w-5",
  },
};

export function Modal({ children, ...rest }: ModalProps) {
  return (
    <FlowModal data-testid="modal" theme={theme} {...rest}>
      {children}
    </FlowModal>
  );
}

export function ModalHeader({
  children,
  variant = "primary",
  fullWidth = false,
}: ModalHeaderProps) {
  return (
    <FlowModal.Header
      theme={variant === "secondary" ? reverseColorIcon : undefined}
      className={fullWidth ? "p-0 mt-0" : ""}
    >
      {children}
    </FlowModal.Header>
  );
}

export function ModalBody({ children }: ModalBodyProps) {
  return <FlowModal.Body>{children}</FlowModal.Body>;
}

export function ModalFooter({ children }: ModalFooterProps) {
  return <FlowModal.Footer>{children}</FlowModal.Footer>;
}

export default Modal;
