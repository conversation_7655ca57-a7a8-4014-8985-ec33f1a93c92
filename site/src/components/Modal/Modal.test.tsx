import { render, screen } from "@tests/render";

import Modal, { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, ModalHeader } from "./Modal";

describe("<Modal />", () => {
  it("should render the Modal component", () => {
    render(
      <Modal show>
        <ModalHeader>Title</ModalHeader>
        <ModalBody>Body Content</ModalBody>
        <ModalFooter>Footer</ModalFooter>
      </Modal>,
    );
    expect(screen.getAllByTestId("modal")).toMatchSnapshot();
  });

  it("should render the Modal with a different close icon color", () => {
    render(
      <Modal show>
        <ModalHeader variant="secondary">Title</ModalHeader>
      </Modal>,
    );
    expect(screen.getByRole("button")).toMatchSnapshot();
  });

  it("should render the Modal with no padding in Header", () => {
    render(
      <Modal show>
        <ModalHeader fullWidth>Title</ModalHeader>
      </Modal>,
    );
    expect(screen.getAllByTestId("modal")).toMatchSnapshot();
  });
});
