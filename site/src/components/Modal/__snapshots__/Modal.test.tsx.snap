// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Modal /> should render the Modal component 1`] = `
[
  <div
    class="fixed top-0 right-0 left-0 z-50 h-modal h-screen overflow-y-auto overflow-x-hidden md:inset-0 md:h-full items-center justify-center flex bg-gray-900 bg-opacity-50 dark:bg-opacity-80"
    data-testid="modal"
    style="position: fixed; overflow: auto; top: 0px; right: 0px; bottom: 0px; left: 0px;"
  >
    <span
      aria-hidden="true"
      data-floating-ui-focus-guard=""
      data-floating-ui-inert=""
      data-type="inside"
      role="button"
      style="border: 0px; height: 1px; margin: -1px; overflow: hidden; padding: 0px; position: fixed; white-space: nowrap; width: 1px; top: 0px; left: 0px;"
      tabindex="0"
    />
    <div
      aria-labelledby=":r4:"
      class="relative h-full w-full p-4 md:h-auto max-w-2xl"
      data-testid="modal"
      id=":r0:"
      role="dialog"
      tabindex="-1"
    >
      <div
        class="relative rounded-xl bg-white dark:bg-gray-700 flex flex-col max-h-[90vh]"
      >
        <div
          class="mt-1 flex flex-col justify-between p-5"
        >
          <h3
            class="text-2xl font-bold text-black-900 font-outfit text-center w-full"
            id=":r4:"
          >
            Title
          </h3>
          <button
            aria-label="Close"
            class="absolute right-4 top-4 ml-auto inline-flex items-center rounded-md bg-transparent p-1.5 text-sm text-black-900"
            type="button"
          >
            <svg
              aria-hidden="true"
              class="h-5 w-5"
              fill="none"
              height="1em"
              stroke="currentColor"
              stroke-width="2"
              viewBox="0 0 24 24"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M6 18L18 6M6 6l12 12"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>
        <div
          class="p-6 flex-1 overflow-auto font-rubik"
        >
          Body Content
        </div>
        <div
          class="flex items-center space-x-2 p-6"
        >
          Footer
        </div>
      </div>
    </div>
    <span
      aria-hidden="true"
      data-floating-ui-focus-guard=""
      data-floating-ui-inert=""
      data-type="inside"
      role="button"
      style="border: 0px; height: 1px; margin: -1px; overflow: hidden; padding: 0px; position: fixed; white-space: nowrap; width: 1px; top: 0px; left: 0px;"
      tabindex="0"
    />
  </div>,
  <div
    aria-labelledby=":r4:"
    class="relative h-full w-full p-4 md:h-auto max-w-2xl"
    data-testid="modal"
    id=":r0:"
    role="dialog"
    tabindex="-1"
  >
    <div
      class="relative rounded-xl bg-white dark:bg-gray-700 flex flex-col max-h-[90vh]"
    >
      <div
        class="mt-1 flex flex-col justify-between p-5"
      >
        <h3
          class="text-2xl font-bold text-black-900 font-outfit text-center w-full"
          id=":r4:"
        >
          Title
        </h3>
        <button
          aria-label="Close"
          class="absolute right-4 top-4 ml-auto inline-flex items-center rounded-md bg-transparent p-1.5 text-sm text-black-900"
          type="button"
        >
          <svg
            aria-hidden="true"
            class="h-5 w-5"
            fill="none"
            height="1em"
            stroke="currentColor"
            stroke-width="2"
            viewBox="0 0 24 24"
            width="1em"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6 18L18 6M6 6l12 12"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
      <div
        class="p-6 flex-1 overflow-auto font-rubik"
      >
        Body Content
      </div>
      <div
        class="flex items-center space-x-2 p-6"
      >
        Footer
      </div>
    </div>
  </div>,
]
`;

exports[`<Modal /> should render the Modal with a different close icon color 1`] = `
<button
  aria-label="Close"
  class="absolute right-4 top-4 ml-auto inline-flex items-center rounded-md bg-black-900 p-1.5 text-sm text-white"
  type="button"
>
  <svg
    aria-hidden="true"
    class="h-5 w-5"
    fill="none"
    height="1em"
    stroke="currentColor"
    stroke-width="2"
    viewBox="0 0 24 24"
    width="1em"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6 18L18 6M6 6l12 12"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
</button>
`;

exports[`<Modal /> should render the Modal with no padding in Header 1`] = `
[
  <div
    class="fixed top-0 right-0 left-0 z-50 h-modal h-screen overflow-y-auto overflow-x-hidden md:inset-0 md:h-full items-center justify-center flex bg-gray-900 bg-opacity-50 dark:bg-opacity-80"
    data-testid="modal"
    style="position: fixed; overflow: auto; top: 0px; right: 0px; bottom: 0px; left: 0px;"
  >
    <span
      aria-hidden="true"
      data-floating-ui-focus-guard=""
      data-floating-ui-inert=""
      data-type="inside"
      role="button"
      style="border: 0px; height: 1px; margin: -1px; overflow: hidden; padding: 0px; position: fixed; white-space: nowrap; width: 1px; top: 0px; left: 0px;"
      tabindex="0"
    />
    <div
      aria-labelledby=":re:"
      class="relative h-full w-full p-4 md:h-auto max-w-2xl"
      data-testid="modal"
      id=":ra:"
      role="dialog"
      tabindex="-1"
    >
      <div
        class="relative rounded-xl bg-white dark:bg-gray-700 flex flex-col max-h-[90vh]"
      >
        <div
          class="flex flex-col justify-between p-0 mt-0"
        >
          <h3
            class="text-2xl font-bold text-black-900 font-outfit text-center w-full"
            id=":re:"
          >
            Title
          </h3>
          <button
            aria-label="Close"
            class="absolute right-4 top-4 ml-auto inline-flex items-center rounded-md bg-transparent p-1.5 text-sm text-black-900"
            type="button"
          >
            <svg
              aria-hidden="true"
              class="h-5 w-5"
              fill="none"
              height="1em"
              stroke="currentColor"
              stroke-width="2"
              viewBox="0 0 24 24"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M6 18L18 6M6 6l12 12"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
    <span
      aria-hidden="true"
      data-floating-ui-focus-guard=""
      data-floating-ui-inert=""
      data-type="inside"
      role="button"
      style="border: 0px; height: 1px; margin: -1px; overflow: hidden; padding: 0px; position: fixed; white-space: nowrap; width: 1px; top: 0px; left: 0px;"
      tabindex="0"
    />
  </div>,
  <div
    aria-labelledby=":re:"
    class="relative h-full w-full p-4 md:h-auto max-w-2xl"
    data-testid="modal"
    id=":ra:"
    role="dialog"
    tabindex="-1"
  >
    <div
      class="relative rounded-xl bg-white dark:bg-gray-700 flex flex-col max-h-[90vh]"
    >
      <div
        class="flex flex-col justify-between p-0 mt-0"
      >
        <h3
          class="text-2xl font-bold text-black-900 font-outfit text-center w-full"
          id=":re:"
        >
          Title
        </h3>
        <button
          aria-label="Close"
          class="absolute right-4 top-4 ml-auto inline-flex items-center rounded-md bg-transparent p-1.5 text-sm text-black-900"
          type="button"
        >
          <svg
            aria-hidden="true"
            class="h-5 w-5"
            fill="none"
            height="1em"
            stroke="currentColor"
            stroke-width="2"
            viewBox="0 0 24 24"
            width="1em"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6 18L18 6M6 6l12 12"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>,
]
`;
