import { tv } from "tailwind-variants";

export interface TagProps extends React.HTMLAttributes<HTMLSpanElement> {
  variant?:
    | "primary"
    | "primarySolid"
    | "secondary"
    | "tertiary"
    | "quaternary"
    | "quinternary";
  size?: "small" | "medium" | "large";
  checked?: boolean;
}

const tagStyle = tv({
  base: "flex flex-row gap-1 border-0 rounded-[1.1875rem] font-outfit w-max font-medium items-center",
  variants: {
    variant: {
      primary: "bg-transparent border-gray-800 border",
      primarySolid: "bg-white border-gray-800 border",
      secondary: "bg-white border-yellow-900",
      tertiary: "bg-yellow-800 border-black-900",
      quaternary: "bg-black-900 text-white",
      quinternary: "bg-black-600 text-white",
    },
    size: {
      small: "px-1 h-[1.125rem] text-[0.5625rem]",
      medium: "px-2 h-7 text-xs",
      large: "px-3 h-10 text-sm",
    },
    checked: {
      true: "border",
    },
  },
  compoundVariants: [
    {
      variant: [
        "primary",
        "primarySolid",
        "secondary",
        "tertiary",
        "quaternary",
        "quinternary",
      ],
      size: ["small", "medium", "large"],
      checked: [true, false],
    },
  ],
  defaultVariants: {
    variant: "primary",
    size: "large",
    checked: false,
  },
});

const circleStyle = tv({
  variants: {
    variant: {
      primary: "fill-gray-800",
      primarySolid: "fill-gray-800",
      secondary: "fill-yellow-900",
      tertiary: "fill-black-900",
      quaternary: "fill-black-900",
      quinternary: "fill-black-600",
    },
    size: {
      small: "h-3 w-3",
      medium: "h-4 w-4",
      large: "h-4 w-4",
    },
  },
});

const iconStyle = tv({
  base: "",
  variants: {
    size: {
      small: "h-2.5 w-2.5",
      medium: "h-3 w-3",
      large: "h-3 w-3",
    },
  },
});

export function Tag({
  children,
  variant = "primary",
  size = "large",
  checked = false,
  className,
  ...rest
}: TagProps) {
  return (
    <span className={tagStyle({ variant, size, checked, className })} {...rest}>
      {children}
      {checked && (
        <svg
          className={circleStyle({ size })}
          viewBox={size === "small" ? "0 0 12 12" : "0 0 16 16"}
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle
            className={circleStyle({ variant })}
            cx={size === "small" ? "6" : "8"}
            cy={size === "small" ? "6" : "8"}
            r={size === "small" ? "6" : "8"}
          />
          <svg
            className={iconStyle({ size })}
            viewBox="0 0 12 12"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6.70497 6L8.85497 3.855C8.94912 3.76085 9.00202 3.63315 9.00202 3.5C9.00202 3.36685 8.94912 3.23915 8.85497 3.145C8.76082 3.05085 8.63312 2.99795 8.49997 2.99795C8.36682 2.99795 8.23912 3.05085 8.14497 3.145L5.99997 5.295L3.85497 3.145C3.76082 3.05085 3.63312 2.99795 3.49997 2.99795C3.36682 2.99795 3.23912 3.05085 3.14497 3.145C3.05082 3.23915 2.99792 3.36685 2.99792 3.5C2.99792 3.63315 3.05082 3.76085 3.14497 3.855L5.29497 6L3.14497 8.145C3.09811 8.19148 3.06091 8.24678 3.03552 8.30771C3.01014 8.36864 2.99707 8.43399 2.99707 8.5C2.99707 8.566 3.01014 8.63136 3.03552 8.69229C3.06091 8.75322 3.09811 8.80852 3.14497 8.855C3.19145 8.90186 3.24675 8.93906 3.30768 8.96445C3.36861 8.98983 3.43396 9.0029 3.49997 9.0029C3.56598 9.0029 3.63133 8.98983 3.69226 8.96445C3.75319 8.93906 3.80849 8.90186 3.85497 8.855L5.99997 6.705L8.14497 8.855C8.19145 8.90186 8.24675 8.93906 8.30768 8.96445C8.36861 8.98983 8.43396 9.0029 8.49997 9.0029C8.56598 9.0029 8.63133 8.98983 8.69226 8.96445C8.75319 8.93906 8.80849 8.90186 8.85497 8.855C8.90183 8.80852 8.93903 8.75322 8.96442 8.69229C8.9898 8.63136 9.00287 8.566 9.00287 8.5C9.00287 8.43399 8.9898 8.36864 8.96442 8.30771C8.93903 8.24678 8.90183 8.19148 8.85497 8.145L6.70497 6Z"
              fill="white"
            />
          </svg>
        </svg>
      )}
    </span>
  );
}

export default Tag;
