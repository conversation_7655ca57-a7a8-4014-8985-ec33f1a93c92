import { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { Tag, TagProps } from "./Tag";

const story: Meta<TagProps> = {
  component: Tag,
  title: "Tag",
  render: ({ children = "Tag", ...args }) => <Tag {...args}>{children}</Tag>,
} as Meta;

export default story;

type Story = StoryObj<TagProps>;

export const Primary: Story = {
  args: {
    variant: "primary",
    children: "Tag Badge-lg",
  },
};

export const PrimarySolid: Story = {
  args: {
    variant: "primarySolid",
    children: "Tag Badge-lg",
  },
};

export const Secondary: Story = {
  args: {
    variant: "secondary",
    children: "Tag Badge-lg",
  },
};

export const Tertiary: Story = {
  args: {
    variant: "tertiary",
    children: "Tag Badge-lg",
  },
};

export const Quaternary: Story = {
  args: {
    variant: "quaternary",
    children: "Tag Badge-lg",
  },
};

export const Quinary: Story = {
  args: {
    variant: "quinternary",
    children: "Tag Badge-lg",
  },
};

export const Checked: Story = {
  args: {
    checked: true,
    children: "Tag Badge-lg",
  },
};

export const Medium: Story = {
  args: {
    size: "medium",
    children: "Tag Badge-Md",
  },
};

export const Small: Story = {
  args: {
    size: "small",
    children: "Tag Badge-Sm",
  },
};
