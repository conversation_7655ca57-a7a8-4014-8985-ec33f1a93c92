// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Tag /> should render checked Tag 1`] = `
<span
  class="flex flex-row gap-1 rounded-[1.1875rem] font-outfit w-max font-medium items-center bg-transparent border-gray-800 px-3 h-10 text-sm border"
>
  Tag
  <svg
    class="h-4 w-4"
    viewBox="0 0 16 16"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle
      class="fill-gray-800"
      cx="8"
      cy="8"
      r="8"
    />
    <svg
      class="h-3 w-3"
      viewBox="0 0 12 12"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.70497 6L8.85497 3.855C8.94912 3.76085 9.00202 3.63315 9.00202 3.5C9.00202 3.36685 8.94912 3.23915 8.85497 3.145C8.76082 3.05085 8.63312 2.99795 8.49997 2.99795C8.36682 2.99795 8.23912 3.05085 8.14497 3.145L5.99997 5.295L3.85497 3.145C3.76082 3.05085 3.63312 2.99795 3.49997 2.99795C3.36682 2.99795 3.23912 3.05085 3.14497 3.145C3.05082 3.23915 2.99792 3.36685 2.99792 3.5C2.99792 3.63315 3.05082 3.76085 3.14497 3.855L5.29497 6L3.14497 8.145C3.09811 8.19148 3.06091 8.24678 3.03552 8.30771C3.01014 8.36864 2.99707 8.43399 2.99707 8.5C2.99707 8.566 3.01014 8.63136 3.03552 8.69229C3.06091 8.75322 3.09811 8.80852 3.14497 8.855C3.19145 8.90186 3.24675 8.93906 3.30768 8.96445C3.36861 8.98983 3.43396 9.0029 3.49997 9.0029C3.56598 9.0029 3.63133 8.98983 3.69226 8.96445C3.75319 8.93906 3.80849 8.90186 3.85497 8.855L5.99997 6.705L8.14497 8.855C8.19145 8.90186 8.24675 8.93906 8.30768 8.96445C8.36861 8.98983 8.43396 9.0029 8.49997 9.0029C8.56598 9.0029 8.63133 8.98983 8.69226 8.96445C8.75319 8.93906 8.80849 8.90186 8.85497 8.855C8.90183 8.80852 8.93903 8.75322 8.96442 8.69229C8.9898 8.63136 9.00287 8.566 9.00287 8.5C9.00287 8.43399 8.9898 8.36864 8.96442 8.30771C8.93903 8.24678 8.90183 8.19148 8.85497 8.145L6.70497 6Z"
        fill="white"
      />
    </svg>
  </svg>
</span>
`;

exports[`<Tag /> should render medium size Tag 1`] = `
<span
  class="flex flex-row gap-1 rounded-[1.1875rem] font-outfit w-max font-medium items-center bg-transparent border-gray-800 border px-2 h-7 text-xs"
>
  Tag
</span>
`;

exports[`<Tag /> should render primarySolid Tag 1`] = `
<span
  class="flex flex-row gap-1 rounded-[1.1875rem] font-outfit w-max font-medium items-center bg-white border-gray-800 border px-3 h-10 text-sm"
>
  Tag
</span>
`;

exports[`<Tag /> should render quaternary Tag 1`] = `
<span
  class="flex flex-row gap-1 border-0 rounded-[1.1875rem] font-outfit w-max font-medium items-center bg-black-900 text-white px-3 h-10 text-sm"
>
  Tag
</span>
`;

exports[`<Tag /> should render quinternary Tag 1`] = `
<span
  class="flex flex-row gap-1 border-0 rounded-[1.1875rem] font-outfit w-max font-medium items-center bg-black-600 text-white px-3 h-10 text-sm"
>
  Tag
</span>
`;

exports[`<Tag /> should render secondary Tag 1`] = `
<span
  class="flex flex-row gap-1 border-0 rounded-[1.1875rem] font-outfit w-max font-medium items-center bg-white border-yellow-900 px-3 h-10 text-sm"
>
  Tag
</span>
`;

exports[`<Tag /> should render small size Tag 1`] = `
<span
  class="flex flex-row gap-1 rounded-[1.1875rem] font-outfit w-max font-medium items-center bg-transparent border-gray-800 border px-1 h-[1.125rem] text-[0.5625rem]"
>
  Tag
</span>
`;

exports[`<Tag /> should render tertiary Tag 1`] = `
<span
  class="flex flex-row gap-1 border-0 rounded-[1.1875rem] font-outfit w-max font-medium items-center bg-yellow-800 border-black-900 px-3 h-10 text-sm"
>
  Tag
</span>
`;

exports[`<Tag /> should render the Tag 1`] = `
<span
  class="flex flex-row gap-1 rounded-[1.1875rem] font-outfit w-max font-medium items-center bg-transparent border-gray-800 border px-3 h-10 text-sm"
>
  Tag
</span>
`;
