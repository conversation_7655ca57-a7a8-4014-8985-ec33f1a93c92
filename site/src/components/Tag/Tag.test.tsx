import { render, screen } from "@tests/render";

import Tag from "./Tag";
describe("<Tag />", () => {
  it("should render the Tag", () => {
    render(<Tag>Tag</Tag>);
    expect(screen.getByText("Tag")).toMatchSnapshot();
  });

  it("should render medium size Tag", () => {
    render(<Tag size="medium">Tag</Tag>);
    expect(screen.getByText("Tag")).toMatchSnapshot();
  });

  it("should render small size Tag", () => {
    render(<Tag size="small">Tag</Tag>);
    expect(screen.getByText("Tag")).toMatchSnapshot();
  });

  it("should render primarySolid Tag", () => {
    render(<Tag variant="primarySolid">Tag</Tag>);
    expect(screen.getByText("Tag")).toMatchSnapshot();
  });

  it("should render secondary Tag", () => {
    render(<Tag variant="secondary">Tag</Tag>);
    expect(screen.getByText("Tag")).toMatchSnapshot();
  });

  it("should render tertiary Tag", () => {
    render(<Tag variant="tertiary">Tag</Tag>);
    expect(screen.getByText("Tag")).toMatchSnapshot();
  });

  it("should render quaternary Tag", () => {
    render(<Tag variant="quaternary">Tag</Tag>);
    expect(screen.getByText("Tag")).toMatchSnapshot();
  });

  it("should render quinternary Tag", () => {
    render(<Tag variant="quinternary">Tag</Tag>);
    expect(screen.getByText("Tag")).toMatchSnapshot();
  });

  it("should render checked Tag", () => {
    render(<Tag checked>Tag</Tag>);
    expect(screen.getByText("Tag")).toMatchSnapshot();
  });
});
