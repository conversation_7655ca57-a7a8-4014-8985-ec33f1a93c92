import { EyeIcon } from "@heroicons/react/24/outline";
import { ReactNode, SVGProps } from "react";

export interface IconProps extends SVGProps<SVGSVGElement> {
  children?: ReactNode;
  grayscale?: boolean;
}

export const FacebookIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="-1 -1 10 8"
    className="text-white"
  >
    <path
      className="fill-current"
      d="M4.75958 1.33001H5.22958V0.535007C5.00202 0.511344 4.77337 0.499661 4.54458 0.500007C3.86458 0.500007 3.39958 0.915007 3.39958 1.67501V2.33001H2.63208V3.22001H3.39958V5.50001H4.31958V3.22001H5.08458L5.19958 2.33001H4.31958V1.76251C4.31958 1.50001 4.38958 1.33001 4.75958 1.33001Z"
    />
  </svg>
);

export const RoundedFacebookIcon = (props: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    {...props}
  >
    <circle cx="8" cy="8" r="6" fill="#2476F1" />
    <FacebookIcon />
  </svg>
);

export const InstagramIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="13"
    height="13"
    viewBox="-3 -2 10 8"
    className="text-white"
  >
    <defs>
      <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" />
        <stop offset="100%" />
      </linearGradient>
    </defs>
    <g clipPath="url(#clip0_3774_843)">
      <path
        className="fill-current"
        d="M4.335 1.365C4.27567 1.365 4.21766 1.38259 4.16833 1.41556C4.11899 1.44852 4.08054 1.49538 4.05784 1.55019C4.03513 1.60501 4.02919 1.66533 4.04076 1.72353C4.05234 1.78172 4.08091 1.83518 4.12287 1.87713C4.16482 1.91909 4.21828 1.94766 4.27647 1.95924C4.33467 1.97081 4.39499 1.96487 4.44981 1.94216C4.50462 1.91946 4.55148 1.88101 4.58444 1.83167C4.6174 1.78234 4.635 1.72433 4.635 1.665C4.635 1.58544 4.60339 1.50913 4.54713 1.45287C4.49087 1.39661 4.41457 1.365 4.335 1.365ZM5.485 1.97C5.48014 1.76257 5.44129 1.55735 5.37 1.3625C5.30643 1.19578 5.2075 1.04482 5.08 0.92C4.95621 0.791856 4.80489 0.693544 4.6375 0.6325C4.44316 0.55904 4.23771 0.519302 4.03 0.515C3.765 0.5 3.68 0.5 3 0.5C2.32 0.5 2.235 0.5 1.97 0.515C1.76229 0.519302 1.55684 0.55904 1.3625 0.6325C1.19542 0.694162 1.04423 0.79239 0.92 0.92C0.791856 1.04379 0.693544 1.19511 0.6325 1.3625C0.55904 1.55684 0.519302 1.76229 0.515 1.97C0.5 2.235 0.5 2.32 0.5 3C0.5 3.68 0.5 3.765 0.515 4.03C0.519302 4.23771 0.55904 4.44316 0.6325 4.6375C0.693544 4.80489 0.791856 4.95621 0.92 5.08C1.04423 5.20761 1.19542 5.30584 1.3625 5.3675C1.55684 5.44096 1.76229 5.4807 1.97 5.485C2.235 5.5 2.32 5.5 3 5.5C3.68 5.5 3.765 5.5 4.03 5.485C4.23771 5.4807 4.44316 5.44096 4.6375 5.3675C4.80489 5.30646 4.95621 5.20814 5.08 5.08C5.20806 4.95565 5.30708 4.80455 5.37 4.6375C5.44129 4.44265 5.48014 4.23743 5.485 4.03C5.485 3.765 5.5 3.68 5.5 3C5.5 2.32 5.5 2.235 5.485 1.97ZM5.035 4C5.03318 4.15869 5.00444 4.31593 4.95 4.465C4.91008 4.5738 4.84597 4.67211 4.7625 4.7525C4.68141 4.83513 4.58331 4.89911 4.475 4.94C4.32593 4.99444 4.16869 5.02318 4.01 5.025C3.76 5.0375 3.6675 5.04 3.01 5.04C2.3525 5.04 2.26 5.04 2.01 5.025C1.84522 5.02809 1.68115 5.00271 1.525 4.95C1.42145 4.90702 1.32784 4.8432 1.25 4.7625C1.16702 4.68219 1.10371 4.5838 1.065 4.475C1.00396 4.32379 0.970111 4.16299 0.965 4C0.965 3.75 0.95 3.6575 0.95 3C0.95 2.3425 0.95 2.25 0.965 2C0.966121 1.83776 0.995738 1.67699 1.0525 1.525C1.09651 1.41948 1.16407 1.32541 1.25 1.25C1.32595 1.16404 1.41982 1.09577 1.525 1.05C1.67739 0.995011 1.838 0.96627 2 0.965C2.25 0.965 2.3425 0.95 3 0.95C3.6575 0.95 3.75 0.95 4 0.965C4.15869 0.96682 4.31593 0.995562 4.465 1.05C4.57861 1.09216 4.68057 1.16071 4.7625 1.25C4.84443 1.32679 4.90844 1.42068 4.95 1.525C5.00556 1.67723 5.03432 1.83794 5.035 2C5.0475 2.25 5.05 2.3425 5.05 3C5.05 3.6575 5.0475 3.75 5.035 4ZM3 1.7175C2.74645 1.71799 2.49874 1.79363 2.28816 1.93485C2.07759 2.07608 1.91359 2.27655 1.81691 2.51094C1.72022 2.74533 1.69518 3.00312 1.74495 3.25173C1.79472 3.50035 1.91706 3.72863 2.09652 3.90774C2.27598 4.08686 2.5045 4.20875 2.75321 4.25804C3.00193 4.30732 3.25967 4.28178 3.49387 4.18463C3.72807 4.08749 3.92822 3.92311 4.06903 3.71226C4.20984 3.50141 4.285 3.25355 4.285 3C4.28533 2.83128 4.2523 2.66415 4.18781 2.50824C4.12332 2.35233 4.02864 2.2107 3.90922 2.09151C3.7898 1.97232 3.64799 1.87792 3.49195 1.81373C3.33591 1.74954 3.16872 1.71684 3 1.7175ZM3 3.8325C2.83535 3.8325 2.67439 3.78367 2.53749 3.6922C2.40058 3.60072 2.29388 3.4707 2.23087 3.31858C2.16786 3.16646 2.15137 2.99908 2.1835 2.83759C2.21562 2.6761 2.29491 2.52776 2.41133 2.41133C2.52776 2.29491 2.6761 2.21562 2.83759 2.1835C2.99908 2.15137 3.16646 2.16786 3.31858 2.23087C3.4707 2.29388 3.60072 2.40058 3.6922 2.53749C3.78367 2.67439 3.8325 2.83535 3.8325 3C3.8325 3.10933 3.81097 3.21758 3.76913 3.31858C3.72729 3.41959 3.66597 3.51136 3.58867 3.58867C3.51136 3.66597 3.41959 3.72729 3.31858 3.76913C3.21758 3.81097 3.10933 3.8325 3 3.8325Z"
        fill="url(#grad1)"
      />
    </g>
    <defs>
      <clipPath id="clip0_3774_843">
        <rect width="6" height="6" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const RoundedInstagramIcon = (props: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    {...props}
  >
    <defs>
      <linearGradient id="b" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0" stop-color="#3771c8" />
        <stop offset=".128" stop-color="#3771c8" />
        <stop offset="1" stop-color="#60f" stop-opacity="0" />
      </linearGradient>

      <linearGradient id="a" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0" stop-color="#fd5" />
        <stop offset=".1" stop-color="#fd5" />
        <stop offset=".5" stop-color="#ff543e" />
        <stop offset="1" stop-color="#c837ab" />
      </linearGradient>
    </defs>
    <circle cx="8" cy="8" r="6" fill="url(#b)" />
    <circle cx="8" cy="8" r="6" fill="url(#a)" />
    <InstagramIcon />
  </svg>
);

export const LinkedInIcon = (props: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="13"
    height="13"
    viewBox="-2 -3 8 10"
    fill="none"
    {...props}
  >
    <g clipPath="url(#clip0_3748_803)">
      <path
        d="M5.11739 0.499974H0.882393C0.834787 0.499313 0.787518 0.508036 0.743284 0.525645C0.69905 0.543254 0.658718 0.569404 0.624592 0.602602C0.590466 0.6358 0.563213 0.675395 0.544391 0.719126C0.525568 0.762858 0.515545 0.809868 0.514893 0.857474V5.14247C0.515545 5.19008 0.525568 5.23709 0.544391 5.28082C0.563213 5.32455 0.590466 5.36415 0.624592 5.39735C0.658718 5.43054 0.69905 5.45669 0.743284 5.4743C0.787518 5.49191 0.834787 5.50064 0.882393 5.49997H5.11739C5.165 5.50064 5.21227 5.49191 5.2565 5.4743C5.30073 5.45669 5.34107 5.43054 5.37519 5.39735C5.40932 5.36415 5.43657 5.32455 5.45539 5.28082C5.47422 5.23709 5.48424 5.19008 5.48489 5.14247V0.857474C5.48424 0.809868 5.47422 0.762858 5.45539 0.719126C5.43657 0.675395 5.40932 0.6358 5.37519 0.602602C5.34107 0.569404 5.30073 0.543254 5.2565 0.525645C5.21227 0.508036 5.165 0.499313 5.11739 0.499974ZM2.02239 4.68497H1.27239V2.43497H2.02239V4.68497ZM1.64739 2.11997C1.54396 2.11997 1.44476 2.07888 1.37162 2.00575C1.29848 1.93261 1.25739 1.83341 1.25739 1.72997C1.25739 1.62654 1.29848 1.52734 1.37162 1.4542C1.44476 1.38106 1.54396 1.33997 1.64739 1.33997C1.70232 1.33375 1.75794 1.33919 1.81061 1.35594C1.86329 1.3727 1.91183 1.4004 1.95306 1.43721C1.99429 1.47403 2.02727 1.51914 2.04986 1.56959C2.07245 1.62005 2.08412 1.6747 2.08412 1.72997C2.08412 1.78525 2.07245 1.8399 2.04986 1.89035C2.02727 1.9408 1.99429 1.98592 1.95306 2.02273C1.91183 2.05955 1.86329 2.08725 1.81061 2.104C1.75794 2.12076 1.70232 2.1262 1.64739 2.11997ZM4.72739 4.68497H3.97739V3.47747C3.97739 3.17497 3.86989 2.97747 3.59739 2.97747C3.51306 2.97809 3.43094 3.00454 3.3621 3.05327C3.29326 3.10199 3.24101 3.17064 3.21239 3.24997C3.19283 3.30873 3.18435 3.37062 3.18739 3.43247V4.68247H2.43739V2.43247H3.18739V2.74997C3.25553 2.63175 3.35462 2.53435 3.474 2.46827C3.59339 2.4022 3.72853 2.36994 3.86489 2.37497C4.36489 2.37497 4.72739 2.69747 4.72739 3.38997V4.68497Z"
        fill="white"
      />
    </g>
    <defs>
      <clipPath id="clip0_3748_803">
        <rect width="6" height="6" className=" fill-current text-white" />
      </clipPath>
    </defs>
  </svg>
);

export const RoundedLinkedinIcon = (props: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    {...props}
  >
    <circle cx="8" cy="8" r="6" fill="#2476F1" />
    <LinkedInIcon />
  </svg>
);

export const TIcon = (props: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="40"
    height="40"
    viewBox="0 0 40 40"
    fill="none"
    {...props}
  >
    <path
      d="M6.6665 11.6666V6.66663H33.3332V11.6666"
      stroke="black"
      stroke-width="3"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M15 33.3334H25"
      stroke="black"
      stroke-width="3"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M20 6.66663V33.3333"
      stroke="black"
      stroke-width="3"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);

export const RoundedIcon: React.FC<IconProps> = ({ children }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="100"
    height="100"
    viewBox="0 0 112 112"
  >
    <circle cx="56" cy="56" r="56" fill="#FFCC33" />
    <g transform="translate(36, 36)">{children}</g>
  </svg>
);

export const FacebookIconLarge = (props: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="25"
    height="25"
    viewBox="0 0 25 25"
    fill="none"
    {...props}
  >
    <path
      d="M15.6199 6.22039H17.4999V3.04039C16.5896 2.94574 15.675 2.89901 14.7599 2.90039C12.0399 2.90039 10.1799 4.56039 10.1799 7.60039V10.2204H7.10986V13.7804H10.1799V22.9004H13.8599V13.7804H16.9199L17.3799 10.2204H13.8599V7.95039C13.8599 6.90039 14.1399 6.22039 15.6199 6.22039Z"
      fill="white"
    />
  </svg>
);

export const RoundedFacebookIconLarge = (props: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="65"
    height="65"
    viewBox="0 0 65 65"
    fill="none"
    {...props}
  >
    <circle
      cx="32.5"
      cy="32.9004"
      r="32"
      fill={props.grayscale ? "#8B8B88" : "#2476F1"}
    />
    <FacebookIconLarge x="20" y="20" />
  </svg>
);

export const InstagramIconLarge = (props: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="25"
    height="25"
    viewBox="0 0 25 25"
    fill="none"
    {...props}
  >
    <path
      d="M17.84 6.36039C17.6027 6.36039 17.3707 6.43077 17.1733 6.56263C16.976 6.69448 16.8222 6.8819 16.7313 7.10117C16.6405 7.32044 16.6168 7.56172 16.6631 7.7945C16.7094 8.02728 16.8236 8.2411 16.9915 8.40892C17.1593 8.57674 17.3731 8.69103 17.6059 8.73733C17.8387 8.78364 18.0799 8.75987 18.2992 8.66905C18.5185 8.57822 18.7059 8.42441 18.8378 8.22708C18.9696 8.02974 19.04 7.79773 19.04 7.56039C19.04 7.24213 18.9136 6.93691 18.6885 6.71186C18.4635 6.48682 18.1583 6.36039 17.84 6.36039ZM22.44 8.78039C22.4206 7.95069 22.2652 7.12979 21.98 6.35039C21.7257 5.68352 21.33 5.07967 20.82 4.58039C20.3248 4.06782 19.7196 3.67457 19.05 3.43039C18.2727 3.13655 17.4508 2.9776 16.62 2.96039C15.56 2.90039 15.22 2.90039 12.5 2.90039C9.78 2.90039 9.44 2.90039 8.38 2.96039C7.54915 2.9776 6.72734 3.13655 5.95 3.43039C5.28168 3.67704 4.67693 4.06995 4.18 4.58039C3.66743 5.07557 3.27418 5.68083 3.03 6.35039C2.73616 7.12773 2.57721 7.94954 2.56 8.78039C2.5 9.84039 2.5 10.1804 2.5 12.9004C2.5 15.6204 2.5 15.9604 2.56 17.0204C2.57721 17.8512 2.73616 18.673 3.03 19.4504C3.27418 20.1199 3.66743 20.7252 4.18 21.2204C4.67693 21.7308 5.28168 22.1237 5.95 22.3704C6.72734 22.6642 7.54915 22.8232 8.38 22.8404C9.44 22.9004 9.78 22.9004 12.5 22.9004C15.22 22.9004 15.56 22.9004 16.62 22.8404C17.4508 22.8232 18.2727 22.6642 19.05 22.3704C19.7196 22.1262 20.3248 21.733 20.82 21.2204C21.3322 20.723 21.7283 20.1186 21.98 19.4504C22.2652 18.671 22.4206 17.8501 22.44 17.0204C22.44 15.9604 22.5 15.6204 22.5 12.9004C22.5 10.1804 22.5 9.84039 22.44 8.78039ZM20.64 16.9004C20.6327 17.5352 20.5178 18.1641 20.3 18.7604C20.1403 19.1956 19.8839 19.5888 19.55 19.9104C19.2256 20.2409 18.8332 20.4968 18.4 20.6604C17.8037 20.8781 17.1748 20.9931 16.54 21.0004C15.54 21.0504 15.17 21.0604 12.54 21.0604C9.91 21.0604 9.54 21.0604 8.54 21.0004C7.88089 21.0127 7.22459 20.9112 6.6 20.7004C6.18578 20.5285 5.81136 20.2732 5.5 19.9504C5.16809 19.6291 4.91484 19.2356 4.76 18.8004C4.51586 18.1955 4.38044 17.5523 4.36 16.9004C4.36 15.9004 4.3 15.5304 4.3 12.9004C4.3 10.2704 4.3 9.90039 4.36 8.90039C4.36448 8.25145 4.48295 7.60834 4.71 7.00039C4.88605 6.5783 5.15627 6.20205 5.5 5.90039C5.80381 5.55656 6.17929 5.28349 6.6 5.10039C7.20955 4.88043 7.852 4.76547 8.5 4.76039C9.5 4.76039 9.87 4.70039 12.5 4.70039C15.13 4.70039 15.5 4.70039 16.5 4.76039C17.1348 4.76767 17.7637 4.88264 18.36 5.10039C18.8144 5.26904 19.2223 5.54324 19.55 5.90039C19.8777 6.20757 20.1338 6.58312 20.3 7.00039C20.5223 7.60932 20.6373 8.25217 20.64 8.90039C20.69 9.90039 20.7 10.2704 20.7 12.9004C20.7 15.5304 20.69 15.9004 20.64 16.9004ZM12.5 7.77039C11.4858 7.77237 10.495 8.07492 9.65265 8.63981C8.81035 9.2047 8.15438 10.0066 7.76763 10.9441C7.38089 11.8817 7.28072 12.9129 7.47979 13.9073C7.67886 14.9018 8.16824 15.8149 8.88608 16.5314C9.60392 17.2478 10.518 17.7354 11.5129 17.9325C12.5077 18.1297 13.5387 18.0275 14.4755 17.6389C15.4123 17.2504 16.2129 16.5928 16.7761 15.7494C17.3394 14.906 17.64 13.9146 17.64 12.9004C17.6413 12.2255 17.5092 11.557 17.2512 10.9333C16.9933 10.3097 16.6146 9.7432 16.1369 9.26644C15.6592 8.78968 15.0919 8.41207 14.4678 8.15532C13.8436 7.89857 13.1749 7.76775 12.5 7.77039ZM12.5 16.2304C11.8414 16.2304 11.1976 16.0351 10.65 15.6692C10.1023 15.3033 9.67552 14.7832 9.42348 14.1747C9.17144 13.5662 9.1055 12.8967 9.23398 12.2507C9.36247 11.6048 9.67963 11.0114 10.1453 10.5457C10.611 10.08 11.2044 9.76286 11.8503 9.63438C12.4963 9.50589 13.1659 9.57183 13.7743 9.82387C14.3828 10.0759 14.9029 10.5027 15.2688 11.0503C15.6347 11.598 15.83 12.2418 15.83 12.9004C15.83 13.3377 15.7439 13.7707 15.5765 14.1747C15.4092 14.5787 15.1639 14.9458 14.8547 15.2551C14.5454 15.5643 14.1784 15.8096 13.7743 15.9769C13.3703 16.1443 12.9373 16.2304 12.5 16.2304Z"
      fill="white"
    />
  </svg>
);

export const RoundedInstagramIconLarge = (props: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="65"
    height="65"
    viewBox="0 0 65 65"
    {...props}
  >
    <defs>
      <linearGradient id="b" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0" stopColor="#3771c8" />
        <stop offset=".128" stopColor="#3771c8" />
        <stop offset="1" stopColor="#60f" stopOpacity="0" />
      </linearGradient>

      <linearGradient id="a" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0" stopColor="#fd5" />
        <stop offset=".1" stopColor="#fd5" />
        <stop offset=".5" stopColor="#ff543e" />
        <stop offset="1" stopColor="#c837ab" />
      </linearGradient>
    </defs>
    {props.grayscale ? (
      <circle cx="32.5" cy="32.9004" r="32" fill="#8B8B88" />
    ) : (
      <>
        <circle cx="32.5" cy="32.5" r="30" fill="url(#b)" />
        <circle cx="32.5" cy="32.5" r="30" fill="url(#a)" />
      </>
    )}
    <InstagramIconLarge x="20" y="20" />
  </svg>
);

export const LinkedInIconLarge = (props: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="25"
    height="25"
    viewBox="0 0 25 25"
    fill="none"
    {...props}
  >
    <path
      d="M20.9701 2.90039H4.03006C3.83964 2.89775 3.65056 2.93264 3.47362 3.00308C3.29669 3.07351 3.13536 3.17811 2.99886 3.31091C2.86235 3.4437 2.75334 3.60208 2.67805 3.777C2.60276 3.95193 2.56267 4.13997 2.56006 4.33039V21.4704C2.56267 21.6608 2.60276 21.8489 2.67805 22.0238C2.75334 22.1987 2.86235 22.3571 2.99886 22.4899C3.13536 22.6227 3.29669 22.7273 3.47362 22.7977C3.65056 22.8681 3.83964 22.903 4.03006 22.9004H20.9701C21.1605 22.903 21.3496 22.8681 21.5265 22.7977C21.7034 22.7273 21.8648 22.6227 22.0013 22.4899C22.1378 22.3571 22.2468 22.1987 22.3221 22.0238C22.3974 21.8489 22.4375 21.6608 22.4401 21.4704V4.33039C22.4375 4.13997 22.3974 3.95193 22.3221 3.777C22.2468 3.60208 22.1378 3.4437 22.0013 3.31091C21.8648 3.17811 21.7034 3.07351 21.5265 3.00308C21.3496 2.93264 21.1605 2.89775 20.9701 2.90039ZM8.59006 19.6404H5.59006V10.6404H8.59006V19.6404ZM7.09006 9.38039C6.67632 9.38039 6.27953 9.21604 5.98697 8.92348C5.69442 8.63092 5.53006 8.23413 5.53006 7.82039C5.53006 7.40666 5.69442 7.00986 5.98697 6.71731C6.27953 6.42475 6.67632 6.26039 7.09006 6.26039C7.30975 6.23548 7.53224 6.25725 7.74293 6.32428C7.95363 6.39131 8.1478 6.50208 8.31272 6.64935C8.47763 6.79663 8.60958 6.97707 8.69993 7.17887C8.79028 7.38068 8.83698 7.59929 8.83698 7.82039C8.83698 8.0415 8.79028 8.26011 8.69993 8.46191C8.60958 8.66371 8.47763 8.84416 8.31272 8.99143C8.1478 9.1387 7.95363 9.24948 7.74293 9.31651C7.53224 9.38354 7.30975 9.40531 7.09006 9.38039ZM19.4101 19.6404H16.4101V14.8104C16.4101 13.6004 15.9801 12.8104 14.8901 12.8104C14.5527 12.8129 14.2242 12.9187 13.9489 13.1136C13.6735 13.3085 13.4645 13.5831 13.3501 13.9004C13.2718 14.1354 13.2379 14.383 13.2501 14.6304V19.6304H10.2501V10.6304H13.2501V11.9004C13.5226 11.4275 13.919 11.0379 14.3965 10.7736C14.874 10.5093 15.4146 10.3802 15.9601 10.4004C17.9601 10.4004 19.4101 11.6904 19.4101 14.4604V19.6404Z"
      fill="white"
    />
  </svg>
);

export const RoundedLinkedinIconLarge = (props: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="65"
    height="65"
    viewBox="0 0 65 65"
    fill="none"
    {...props}
  >
    <circle
      cx="32.5"
      cy="32.9004"
      r="32"
      fill={props.grayscale ? "#8B8B88" : "#2967BC"}
    />
    <LinkedInIconLarge x="20" y="20" />
  </svg>
);

export const XIconLarge = (props: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="25"
    height="25"
    viewBox="0 0 25 25"
    fill="none"
    {...props}
  >
    <path
      d="M18.251 3.90039H21.318L14.618 11.5254L22.5 21.9004H16.328L11.495 15.6074L5.964 21.9004H2.894L10.061 13.7454L2.5 3.90039H8.828L13.198 9.65239L18.251 3.90039ZM17.175 20.0724H18.875L7.904 5.63239H6.08L17.175 20.0724Z"
      fill="white"
    />
  </svg>
);

export const RoundedXIconLarge = (props: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="65"
    height="65"
    viewBox="0 0 65 65"
    fill="none"
    {...props}
  >
    <circle
      cx="32.5"
      cy="32.9004"
      r="32"
      fill={props.grayscale ? "#8B8B88" : "#000117"}
    />
    <XIconLarge x="20" y="20" />
  </svg>
);

export const TikTokIconLarge = (props: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="25"
    height="25"
    viewBox="0 0 25 25"
    fill="none"
    {...props}
  >
    <path
      d="M17.0999 6.72039C16.4163 5.94001 16.0396 4.93782 16.0399 3.90039H12.9499V16.3004C12.9261 16.9714 12.6428 17.607 12.1597 18.0733C11.6766 18.5396 11.0314 18.8003 10.3599 18.8004C8.93991 18.8004 7.75991 17.6404 7.75991 16.2004C7.75991 14.4804 9.41991 13.1904 11.1299 13.7204V10.5604C7.67991 10.1004 4.65991 12.7804 4.65991 16.2004C4.65991 19.5304 7.41991 21.9004 10.3499 21.9004C13.4899 21.9004 16.0399 19.3504 16.0399 16.2004V9.91039C17.2929 10.8102 18.7973 11.293 20.3399 11.2904V8.20039C20.3399 8.20039 18.4599 8.29039 17.0999 6.72039Z"
      fill="white"
    />
  </svg>
);

export const RoundedTikTokIconLarge = (props: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="65"
    height="65"
    viewBox="0 0 65 65"
    fill="none"
    {...props}
  >
    <circle
      cx="32.5"
      cy="32.9004"
      r="32"
      fill={props.grayscale ? "#8B8B88" : "#000117"}
    />
    <TikTokIconLarge x="20" y="20" />
  </svg>
);

export const YouTubeIconLarge = (props: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="25"
    height="25"
    viewBox="0 0 25 25"
    fill="none"
    {...props}
  >
    <path
      d="M23.5 10.6104C23.5495 9.17904 23.2365 7.75841 22.59 6.48039C22.1514 5.95599 21.5427 5.6021 20.87 5.48039C18.0875 5.22792 15.2936 5.12443 12.5 5.17039C9.71667 5.12235 6.93274 5.22249 4.16003 5.47039C3.61185 5.57011 3.10454 5.82723 2.70003 6.21039C1.80003 7.04039 1.70003 8.46039 1.60003 9.66039C1.45494 11.818 1.45494 13.9828 1.60003 16.1404C1.62896 16.8158 1.72952 17.4862 1.90003 18.1404C2.0206 18.6455 2.26455 19.1127 2.61003 19.5004C3.01729 19.9038 3.53641 20.1756 4.10003 20.2804C6.25594 20.5465 8.42824 20.6568 10.6 20.6104C14.1 20.6604 17.17 20.6104 20.8 20.3304C21.3775 20.232 21.9112 19.9599 22.33 19.5504C22.61 19.2703 22.8191 18.9275 22.94 18.5504C23.2977 17.453 23.4733 16.3045 23.46 15.1504C23.5 14.5904 23.5 11.2104 23.5 10.6104ZM10.24 15.7504V9.56039L16.16 12.6704C14.5 13.5904 12.31 14.6304 10.24 15.7504Z"
      fill="white"
    />
  </svg>
);

export const RoundedYoutubeIconLarge = (props: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="65"
    height="65"
    viewBox="0 0 65 65"
    fill="none"
    {...props}
  >
    <circle
      cx="32.5"
      cy="32.9004"
      r="32"
      fill={props.grayscale ? "#8B8B88" : "#FF0000"}
    />
    <YouTubeIconLarge x="20" y="20" />
  </svg>
);

export const WhatsAppIconLarge = (props: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="25"
    height="25"
    viewBox="0 0 25 25"
    fill="none"
    {...props}
  >
    <path
      d="M17.1 14.9004C16.9 14.8004 15.6 14.2004 15.4 14.1004C15.2 14.0004 15 14.0004 14.8 14.2004C14.6 14.4004 14.2 15.0004 14 15.2004C13.9 15.4004 13.7 15.4004 13.5 15.3004C12.8 15.0004 12.1 14.6004 11.5 14.1004C11 13.6004 10.5 13.0004 10.1 12.4004C10 12.2004 10.1 12.0004 10.2 11.9004C10.3 11.8004 10.4 11.6004 10.6 11.5004C10.7 11.4004 10.8 11.2004 10.8 11.1004C10.9 11.0004 10.9 10.8004 10.8 10.7004C10.7 10.6004 10.2 9.40039 10 8.90039C9.9 8.20039 9.7 8.20039 9.5 8.20039H9C8.8 8.20039 8.5 8.40039 8.4 8.50039C7.8 9.10039 7.5 9.80039 7.5 10.6004C7.6 11.5004 7.9 12.4004 8.5 13.2004C9.6 14.8004 11 16.1004 12.7 16.9004C13.2 17.1004 13.6 17.3004 14.1 17.4004C14.6 17.6004 15.1 17.6004 15.7 17.5004C16.4 17.4004 17 16.9004 17.4 16.3004C17.6 15.9004 17.6 15.5004 17.5 15.1004L17.1 14.9004ZM19.6 5.80039C15.7 1.90039 9.4 1.90039 5.5 5.80039C2.3 9.00039 1.7 13.9004 3.9 17.8004L2.5 22.9004L7.8 21.5004C9.3 22.3004 10.9 22.7004 12.5 22.7004C18 22.7004 22.4 18.3004 22.4 12.8004C22.5 10.2004 21.4 7.70039 19.6 5.80039ZM16.9 19.8004C15.6 20.6004 14.1 21.1004 12.5 21.1004C11 21.1004 9.6 20.7004 8.3 20.0004L8 19.8004L4.9 20.6004L5.7 17.6004L5.5 17.3004C3.1 13.3004 4.3 8.30039 8.2 5.80039C12.1 3.30039 17.1 4.60039 19.5 8.40039C21.9 12.3004 20.8 17.4004 16.9 19.8004Z"
      fill="white"
    />
  </svg>
);

export const RoundedWhatsAppIconLarge = (props: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="65"
    height="65"
    viewBox="0 0 65 65"
    fill="none"
    {...props}
  >
    <circle
      cx="32.5"
      cy="32.9004"
      r="32"
      fill={props.grayscale ? "#8B8B88" : "#25D366"}
    />
    <WhatsAppIconLarge x="20" y="20" />
  </svg>
);

export const RoundedEyeIcon = (props: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="112"
    height="112"
    viewBox="0 0 112 112"
    fill="none"
    {...props}
  >
    <circle cx="56" cy="56" r="56" fill="#FFCC33" />
    <EyeIcon y="36" height="40px" className="stroke-[0.15rem]" />
  </svg>
);
