import "@testing-library/jest-dom";
import { render, screen } from "@tests/render";
import Link from "./Link";

describe("Link", () => {
  it("renders the correct text", () => {
    render(<Link color="primary">Click Here</Link>);
    const linkElement = screen.getByText(/Click Here/i);
    expect(linkElement).toBeInTheDocument();
  });

  it(`renders correctly in primary variant`, () => {
    const { asFragment } = render(<Link variant="primary">Test</Link>);
    expect(asFragment()).toMatchSnapshot();
  });

  it(`renders correctly in secondary variant`, () => {
    const { asFragment } = render(<Link variant="secondary">Test</Link>);
    expect(asFragment()).toMatchSnapshot();
  });

  it(`renders correctly in tertiary variant`, () => {
    const { asFragment } = render(<Link variant="tertiary">Test</Link>);
    expect(asFragment()).toMatchSnapshot();
  });
});
