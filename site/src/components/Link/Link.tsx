import React from "react";
import { HTMLAttributes } from "react";
import { tv } from "tailwind-variants";

export interface LinkProps extends HTMLAttributes<HTMLAnchorElement> {
  variant?: "primary" | "secondary" | "tertiary";
  href?: string;
}

const linkStyle = tv({
  base: "hover:underline",
  variants: {
    variant: {
      primary: "text-yellow-900 hover:text-black-900",
      secondary: "text-yellow-800 hover:text-white",
      tertiary: "text-gray-800 hover:text-yellow-900 hover:no-underline",
    },
  },
  defaultVariants: {
    variant: "primary",
  },
});

const Link: React.FC<LinkProps> = ({
  children,
  className,
  href,
  variant,
  ...rest
}: LinkProps) => {
  return (
    <a href={href} className={linkStyle({ variant, className })} {...rest}>
      {children}
    </a>
  );
};

export default Link;
