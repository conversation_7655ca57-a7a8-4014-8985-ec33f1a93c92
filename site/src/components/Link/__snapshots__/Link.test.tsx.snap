// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Link renders correctly in primary variant 1`] = `
<DocumentFragment>
  <a
    class="hover:underline text-yellow-900 hover:text-black-900"
  >
    Test
  </a>
</DocumentFragment>
`;

exports[`<PERSON> renders correctly in secondary variant 1`] = `
<DocumentFragment>
  <a
    class="hover:underline text-yellow-800 hover:text-white"
  >
    Test
  </a>
</DocumentFragment>
`;

exports[`<PERSON> renders correctly in tertiary variant 1`] = `
<DocumentFragment>
  <a
    class="text-gray-800 hover:text-yellow-900 hover:no-underline"
  >
    Test
  </a>
</DocumentFragment>
`;
