import { <PERSON><PERSON>, <PERSON> } from "@storybook/react";
import Link, { LinkProps } from "./Link";

const meta: Meta<LinkProps> = {
  title: "Components/Link",
  component: Link,
  argTypes: {
    children: { control: "text" },
    className: { control: "text" },
    variant: {
      control: "radio",
      options: ["primary", "secondary"],
    },
  },
} as Meta;

export default meta;

const Template: Story<LinkProps> = (args) => (
  <Link className="text-xs" {...args} />
);

export const Primary = Template.bind({});
Primary.args = {
  children: "Click Here",
  variant: "primary",
  href: "/",
};

export const Secondary = Template.bind({});
Secondary.args = {
  children: "Click Here",
  variant: "secondary",
  href: "/",
};

export const Tertiary = Template.bind({});
Tertiary.args = {
  children: "Click Here",
  variant: "tertiary",
  href: "/",
};
