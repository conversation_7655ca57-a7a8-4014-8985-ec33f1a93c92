import { render, screen, waitFor } from "@tests/render";
import userEvent from "@testing-library/user-event";
import Dropdown from "./Dropdown";

test("Dropdown component renders correctly", async () => {
  const onChangeMock = jest.fn();
  const loadOptionsMock = jest.fn();
  render(<Dropdown loadOptions={loadOptionsMock} onChange={onChangeMock} />);
  const dropdown = screen.getByRole("combobox");
  userEvent.click(dropdown);
  const inputField = screen.getByRole("combobox");
  userEvent.type(inputField, "Course");
  await waitFor(() => {
    const optionElement = screen.getByRole("combobox") as HTMLInputElement;
    expect(optionElement.value).toMatch(/Course/i);
  });
});

test("Dropdown component renders with error variant", async () => {
  const onChangeMock = jest.fn();
  const loadOptionsMock = jest.fn();
  render(
    <Dropdown
      loadOptions={loadOptionsMock}
      onChange={onChangeMock}
      variant="error"
    />,
  );
  expect(screen.getByRole("combobox")).toMatchSnapshot();
});
