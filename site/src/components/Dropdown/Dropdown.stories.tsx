import React from "react";
import Dropdown, { DropdownProps } from "./Dropdown";

export default {
  title: "Components/Dropdown",
  component: Dropdown,
  argTypes: {
    onChange: { action: "selected" },
  },
};

const dropdownOptions = [
  { value: "options", label: "Options" },
  { value: "service_of", label: "Service of" },
  { value: "course_of", label: "Course of" },
  { value: "management_of", label: "Management of" },
  { value: "consultancy", label: "Consultancy" },
  { value: "disclosure_of", label: "Disclosure of" },
];

const filterdropdown = (inputValue: string) => {
  return dropdownOptions.filter((i) =>
    i.label.toLowerCase().includes(inputValue.toLowerCase()),
  );
};

const loadOptions = (
  inputValue: string,
  callback: (options: { value: string; label: string }[]) => void,
) => {
  setTimeout(() => {
    callback(filterdropdown(inputValue));
  }, 1000);
};

export const DropdownAutocomplete: React.FC<DropdownProps> = () => (
  <Dropdown
    loadOptions={loadOptions}
    onChange={(value) => console.log(`Selected: ${value}`)}
  />
);

export const DropdownErrorVariant: React.FC<DropdownProps> = () => (
  <Dropdown
    loadOptions={loadOptions}
    onChange={(value) => console.log(`Selected: ${value}`)}
    variant="error"
  />
);
