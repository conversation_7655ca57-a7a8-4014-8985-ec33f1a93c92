import React from "react";
import { useTranslation } from "react-i18next";
import AsyncSelect from "react-select/async";

export interface DropdownProps {
  loadOptions: (
    inputValue: string,
    callback: (options: { value: string; label: string }[]) => void,
  ) => Promise<{ value: string; label: string }[]> | void;
  onChange: (selectedOption: { value: string; label: string } | null) => void;
  variant?: "default" | "error";
}

export const Dropdown: React.FC<DropdownProps> = ({
  loadOptions,
  onChange,
  variant = "default",
}) => {
  const { t } = useTranslation();
  return (
    <AsyncSelect
      data-testid="selected-text"
      loadOptions={loadOptions}
      onChange={onChange}
      placeholder={t("components.DropDown.placeholder")}
      styles={{
        option: (baseStyles, state) => ({
          ...baseStyles,
          background: state.isFocused ? "#FFF6DB" : "white",
          borderRadius: "0.5rem",
          padding: "0.5rem 0.5rem",
          fontFamily: "Rubik",
          fontSize: "0.75rem",
          fontStyle: "normal",
          fontWeight: "400",
          lineHeight: "1rem",
        }),
        control: (baseStyles, state) => ({
          ...baseStyles,
          borderColor: state.isFocused
            ? "#000117"
            : variant === "error"
              ? "#e02424"
              : "#DDD9D3F2",
          display: "flex",
          width: "100%",
          height: "2.5rem",
          borderRadius: "0.5rem",
          background: "#FFF",
          fontFamily: "Rubik",
          fontSize: "0.75rem",
          fontStyle: "normal",
          fontWeight: "400",
          lineHeight: "1rem",
          boxShadow: state.isFocused
            ? "0px 0px 4px 0px rgba(0, 0, 0, 0.25)"
            : "none",
          ":hover": {
            borderColor: state.isFocused
              ? "#000117"
              : variant === "error"
                ? "#e02424"
                : "#DDD9D3F2",
          },
          outline: "none",
        }),
        indicatorSeparator: (baseStyles) => ({
          ...baseStyles,
          display: "none",
        }),
        menu: (baseStyles) => ({
          ...baseStyles,
          width: "100%",
          borderRadius: "0.5rem",
          boxShadow: "none",
        }),
        menuList: (baseStyles) => ({
          ...baseStyles,
          padding: "1rem 0.5rem",
          borderRadius: "0.5rem",
        }),
      }}
    />
  );
};

export default Dropdown;
