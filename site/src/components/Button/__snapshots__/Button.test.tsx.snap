// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Button /> should render X-Large button 1`] = `
<button
  class="w-full font-outfit font-medium text-black-900 duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-base tracking-[0.0312rem] rounded-lg px-7 py-4"
>
  Button
</button>
`;

exports[`<Button /> should render disabled button 1`] = `
<button
  class="w-full py-3 px-4 rounded-md font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-xs"
  disabled=""
>
  Button
</button>
`;

exports[`<Button /> should render large button 1`] = `
<button
  class="w-full font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-sm rounded-md px-5 py-[0.875rem]"
>
  Button
</button>
`;

exports[`<Button /> should render normal button 1`] = `
<button
  class="w-full py-3 px-4 rounded-md font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-xs"
>
  Button
</button>
`;

exports[`<Button /> should render quartenary button 1`] = `
<button
  class="w-full py-3 px-4 rounded-md font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-white text-xs"
>
  Button
</button>
`;

exports[`<Button /> should render quartenary button 2`] = `
<button
  class="w-full py-3 px-4 rounded-md font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-blue-100 hover:bg-yellow-900 hover:text-white text-xs"
>
  Button
</button>
`;

exports[`<Button /> should render rounded button 1`] = `
<button
  class="w-full font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white rounded-full p-3 text-xs"
>
  Button
</button>
`;

exports[`<Button /> should render secondary button 1`] = `
<button
  class="w-full py-3 px-4 rounded-md font-outfit font-medium tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-xs"
>
  Button
</button>
`;

exports[`<Button /> should render small button 1`] = `
<button
  class="w-full font-outfit text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-[0.5625rem] font-semibold py-[0.4532rem] px-3 rounded"
>
  Button
</button>
`;

exports[`<Button /> should render tertiary button 1`] = `
<button
  class="w-full py-3 px-4 rounded-md font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-amber-300 text-xs"
>
  Button
</button>
`;

exports[`<Button /> should render the button 1`] = `
<button
  class="w-full py-3 px-4 rounded-md font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-xs"
>
  Button
</button>
`;
