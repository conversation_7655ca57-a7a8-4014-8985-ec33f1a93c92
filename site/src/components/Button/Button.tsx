import { ButtonHTMLAttributes } from "react";
import { tv } from "tailwind-variants";

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  color?: "primary" | "secondary" | "tertiary" | "quartenary" | "quintenary";
  rounded?: boolean;
  size?: "xLarge" | "large" | "normal" | "small";
}

const style = tv({
  base: "w-full py-3 px-4 rounded-md font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed",
  variants: {
    color: {
      primary: "bg-yellow-800 hover:bg-yellow-900 hover:text-white",
      secondary: "bg-black-900 text-white hover:bg-black-800",
      tertiary: "bg-amber-300",
      quartenary: "bg-white",
      quintenary: "bg-blue-100 hover:bg-yellow-900 hover:text-white",
    },
    rounded: {
      true: "rounded-full p-3",
      false: "",
    },
    size: {
      xLarge: "text-base tracking-[0.0312rem] rounded-lg px-7 py-4",
      large: " text-sm rounded-md px-5 py-[0.875rem]",
      normal: "text-xs",
      small: "text-[0.5625rem] font-semibold py-[0.4532rem] px-3 rounded",
    },
  },
  compoundVariants: [
    {
      rounded: [true, false],
      color: ["primary", "secondary", "tertiary", "quartenary", "quintenary"],
      size: ["xLarge", "large", "normal", "small"],
    },
  ],
  defaultVariants: {
    rounded: false,
    color: "primary",
    size: "normal",
  },
});

export function Button({
  children,
  className,
  color = "primary",
  rounded = false,
  size = "normal",
  ...rest
}: ButtonProps) {
  return (
    <button className={style({ rounded, color, className, size })} {...rest}>
      {children}
    </button>
  );
}

export default Button;
