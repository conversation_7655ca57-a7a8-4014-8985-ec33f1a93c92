import { render, screen } from "@tests/render";

import Button from "./Button";
describe("<Button />", () => {
  it("should render the button", () => {
    render(<Button>Button</Button>);
    expect(screen.getByRole("button")).toMatchSnapshot();
  });

  it("should render disabled button", () => {
    render(<Button disabled>Button</Button>);
    expect(screen.getByRole("button")).toMatchSnapshot();
  });

  it("should render secondary button", () => {
    render(<Button color="secondary">Button</Button>);
    expect(screen.getByRole("button")).toMatchSnapshot();
  });

  it("should render rounded button", () => {
    render(<Button rounded>Button</Button>);
    expect(screen.getByRole("button")).toMatchSnapshot();
  });

  it("should render tertiary button", () => {
    render(<Button color="tertiary">Button</Button>);
    expect(screen.getByRole("button")).toMatchSnapshot();
  });

  it("should render quartenary button", () => {
    render(<Button color="quartenary">Button</Button>);
    expect(screen.getByRole("button")).toMatchSnapshot();
  });

  it("should render quartenary button", () => {
    render(<Button color="quintenary">Button</Button>);
    expect(screen.getByRole("button")).toMatchSnapshot();
  });

  it("should render small button", () => {
    render(<Button size="small">Button</Button>);
    expect(screen.getByRole("button")).toMatchSnapshot();
  });

  it("should render normal button", () => {
    render(<Button size="normal">Button</Button>);
    expect(screen.getByRole("button")).toMatchSnapshot();
  });

  it("should render large button", () => {
    render(<Button size="large">Button</Button>);
    expect(screen.getByRole("button")).toMatchSnapshot();
  });

  it("should render X-Large button", () => {
    render(<Button size="xLarge">Button</Button>);
    expect(screen.getByRole("button")).toMatchSnapshot();
  });
});
