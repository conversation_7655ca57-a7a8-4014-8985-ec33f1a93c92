import { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Button, ButtonProps } from "./Button";

import instagram from "../../assets/instagram.svg";

const story: Meta<ButtonProps> = {
  component: Button,
  title: "Button",
  render: ({ children = "Button", ...args }) => (
    <Button {...args}>{children}</Button>
  ),
} as Meta;

export default story;

type Story = StoryObj<ButtonProps>;

export const Primary: Story = {
  args: {
    color: "primary",
  },
};

export const Secondary: Story = {
  args: {
    color: "secondary",
  },
};

export const Tertiary: Story = {
  args: {
    color: "tertiary",
  },
};

export const Quartenary: Story = {
  args: {
    color: "quartenary",
  },
};
export const Quintenary: Story = {
  args: {
    color: "quintenary",
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
  },
};

export const Rounded: Story = {
  args: {
    rounded: true,
    children: <img src={instagram} alt="Instagram" />,
  },
};

export const Small: Story = {
  args: {
    size: "small",
  },
};

export const Large: Story = {
  args: {
    size: "large",
  },
};

export const XLarge: Story = {
  args: {
    size: "xLarge",
  },
};
