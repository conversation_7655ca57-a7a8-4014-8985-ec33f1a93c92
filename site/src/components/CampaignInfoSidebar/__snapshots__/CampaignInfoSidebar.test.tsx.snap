// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CampaignInfoSidebar component matches the snapshot 1`] = `
<DocumentFragment>
  <nav
    class="flex flex-col flex-auto items-start p-4 h-[59.5625rem] w-[18.75rem] shrink-0 bg-[#FFFFFF]"
  >
    <h1
      class="font-outfit font-[700] text-[1.5rem]"
    >
       components.CampaignInfoSidebar.labels.heading
    </h1>
    <div
      class="flex flex-col items-start w-[16.75rem]"
    >
      <p
        class="self-stretch mb-[0.44rem] mt-4 font-outfit font-semibold text-[0.625rem]"
      >
        components.CampaignInfoSidebar.labels.text
      </p>
      <div
        class="relative flex flex-row items-center w-full"
        data-testid="input"
      >
        <input
          aria-label="Nome da Campanha"
          class="text-xs border rounded-lg bg-white disabled:border-gray-200 focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none border-gray-700 flex h-10 w-[16.75rem] p-3 items-stretch self-stretch"
          placeholder="Nome da Campanha"
        />
      </div>
    </div>
    <div
      class="flex flex-col justify-between"
    >
      <p
        class="self-stretch mb-[0.44rem] mt-4 font-outfit font-semibold text-[0.625rem]"
      >
        components.CampaignInfoSidebar.labels.data1
      </p>
      <div
        class="relative h-10 w-[16.75rem]"
      >
        <div
          class="flex"
        >
          <div
            class="relative w-full"
          >
            <div
              class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"
            >
              <svg
                fill="none"
                height="16"
                viewBox="0 0 16 16"
                width="16"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M8.00016 12.6667C8.13202 12.6667 8.26091 12.6276 8.37054 12.5543C8.48018 12.4811 8.56562 12.3769 8.61608 12.2551C8.66654 12.1333 8.67974 11.9993 8.65402 11.8699C8.6283 11.7406 8.5648 11.6218 8.47157 11.5286C8.37833 11.4354 8.25954 11.3719 8.13022 11.3461C8.0009 11.3204 7.86686 11.3336 7.74504 11.3841C7.62322 11.4345 7.5191 11.52 7.44585 11.6296C7.3726 11.7392 7.3335 11.8681 7.3335 12C7.3335 12.1768 7.40373 12.3464 7.52876 12.4714C7.65378 12.5964 7.82335 12.6667 8.00016 12.6667ZM11.3335 12.6667C11.4654 12.6667 11.5942 12.6276 11.7039 12.5543C11.8135 12.4811 11.899 12.3769 11.9494 12.2551C11.9999 12.1333 12.0131 11.9993 11.9874 11.8699C11.9616 11.7406 11.8981 11.6218 11.8049 11.5286C11.7117 11.4354 11.5929 11.3719 11.4636 11.3461C11.3342 11.3204 11.2002 11.3336 11.0784 11.3841C10.9566 11.4345 10.8524 11.52 10.7792 11.6296C10.7059 11.7392 10.6668 11.8681 10.6668 12C10.6668 12.1768 10.7371 12.3464 10.8621 12.4714C10.9871 12.5964 11.1567 12.6667 11.3335 12.6667ZM11.3335 10C11.4654 10 11.5942 9.9609 11.7039 9.88764C11.8135 9.81439 11.899 9.71027 11.9494 9.58845C11.9999 9.46664 12.0131 9.33259 11.9874 9.20327C11.9616 9.07395 11.8981 8.95516 11.8049 8.86193C11.7117 8.76869 11.5929 8.7052 11.4636 8.67947C11.3342 8.65375 11.2002 8.66695 11.0784 8.71741C10.9566 8.76787 10.8524 8.85332 10.7792 8.96295C10.7059 9.07258 10.6668 9.20148 10.6668 9.33333C10.6668 9.51014 10.7371 9.67971 10.8621 9.80473C10.9871 9.92976 11.1567 10 11.3335 10ZM8.00016 10C8.13202 10 8.26091 9.9609 8.37054 9.88764C8.48018 9.81439 8.56562 9.71027 8.61608 9.58845C8.66654 9.46664 8.67974 9.33259 8.65402 9.20327C8.6283 9.07395 8.5648 8.95516 8.47157 8.86193C8.37833 8.76869 8.25954 8.7052 8.13022 8.67947C8.0009 8.65375 7.86686 8.66695 7.74504 8.71741C7.62322 8.76787 7.5191 8.85332 7.44585 8.96295C7.3726 9.07258 7.3335 9.20148 7.3335 9.33333C7.3335 9.51014 7.40373 9.67971 7.52876 9.80473C7.65378 9.92976 7.82335 10 8.00016 10ZM12.6668 2H12.0002V1.33333C12.0002 1.15652 11.9299 0.98695 11.8049 0.861926C11.6799 0.736902 11.5103 0.666664 11.3335 0.666664C11.1567 0.666664 10.9871 0.736902 10.8621 0.861926C10.7371 0.98695 10.6668 1.15652 10.6668 1.33333V2H5.3335V1.33333C5.3335 1.15652 5.26326 0.98695 5.13823 0.861926C5.01321 0.736902 4.84364 0.666664 4.66683 0.666664C4.49002 0.666664 4.32045 0.736902 4.19543 0.861926C4.0704 0.98695 4.00016 1.15652 4.00016 1.33333V2H3.3335C2.80306 2 2.29436 2.21071 1.91928 2.58578C1.54421 2.96086 1.3335 3.46956 1.3335 4V13.3333C1.3335 13.8638 1.54421 14.3725 1.91928 14.7475C2.29436 15.1226 2.80306 15.3333 3.3335 15.3333H12.6668C13.1973 15.3333 13.706 15.1226 14.081 14.7475C14.4561 14.3725 14.6668 13.8638 14.6668 13.3333V4C14.6668 3.46956 14.4561 2.96086 14.081 2.58578C13.706 2.21071 13.1973 2 12.6668 2ZM13.3335 13.3333C13.3335 13.5101 13.2633 13.6797 13.1382 13.8047C13.0132 13.9298 12.8436 14 12.6668 14H3.3335C3.15669 14 2.98712 13.9298 2.86209 13.8047C2.73707 13.6797 2.66683 13.5101 2.66683 13.3333V7.33333H13.3335V13.3333ZM13.3335 6H2.66683V4C2.66683 3.82319 2.73707 3.65362 2.86209 3.52859C2.98712 3.40357 3.15669 3.33333 3.3335 3.33333H4.00016V4C4.00016 4.17681 4.0704 4.34638 4.19543 4.4714C4.32045 4.59643 4.49002 4.66666 4.66683 4.66666C4.84364 4.66666 5.01321 4.59643 5.13823 4.4714C5.26326 4.34638 5.3335 4.17681 5.3335 4V3.33333H10.6668V4C10.6668 4.17681 10.7371 4.34638 10.8621 4.4714C10.9871 4.59643 11.1567 4.66666 11.3335 4.66666C11.5103 4.66666 11.6799 4.59643 11.8049 4.4714C11.9299 4.34638 12.0002 4.17681 12.0002 4V3.33333H12.6668C12.8436 3.33333 13.0132 3.40357 13.1382 3.52859C13.2633 3.65362 13.3335 3.82319 13.3335 4V6ZM4.66683 10C4.79868 10 4.92758 9.9609 5.03721 9.88764C5.14684 9.81439 5.23229 9.71027 5.28275 9.58845C5.33321 9.46664 5.34641 9.33259 5.32069 9.20327C5.29496 9.07395 5.23147 8.95516 5.13823 8.86193C5.045 8.76869 4.92621 8.7052 4.79689 8.67947C4.66757 8.65375 4.53352 8.66695 4.41171 8.71741C4.28989 8.76787 4.18577 8.85332 4.11252 8.96295C4.03926 9.07258 4.00016 9.20148 4.00016 9.33333C4.00016 9.51014 4.0704 9.67971 4.19543 9.80473C4.32045 9.92976 4.49002 10 4.66683 10ZM4.66683 12.6667C4.79868 12.6667 4.92758 12.6276 5.03721 12.5543C5.14684 12.4811 5.23229 12.3769 5.28275 12.2551C5.33321 12.1333 5.34641 11.9993 5.32069 11.8699C5.29496 11.7406 5.23147 11.6218 5.13823 11.5286C5.045 11.4354 4.92621 11.3719 4.79689 11.3461C4.66757 11.3204 4.53352 11.3336 4.41171 11.3841C4.28989 11.4345 4.18577 11.52 4.11252 11.6296C4.03926 11.7392 4.00016 11.8681 4.00016 12C4.00016 12.1768 4.0704 12.3464 4.19543 12.4714C4.32045 12.5964 4.49002 12.6667 4.66683 12.6667Z"
                  fill="#8B8B88"
                />
              </svg>
            </div>
            <input
              class="border border-gray-700 w-full font-rubik tracking-[-0.035rem] focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none bg-white p-2.5 text-sm pl-10 rounded-lg"
              data-testid="datepicker"
              placeholder="Select a Date"
              readonly=""
              value=""
            />
          </div>
        </div>
      </div>
      <p
        class="flex w-[0.375rem] mb-[0.44rem] mt-4 shrink-0 self-stretch font-outfit font-semibold text-[0.625rem]"
      >
        components.CampaignInfoSidebar.labels.data2
      </p>
      <div
        class="relative h-10 w-[16.75rem]"
      >
        <div
          class="flex"
        >
          <div
            class="relative w-full"
          >
            <div
              class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"
            >
              <svg
                fill="none"
                height="16"
                viewBox="0 0 16 16"
                width="16"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M8.00016 12.6667C8.13202 12.6667 8.26091 12.6276 8.37054 12.5543C8.48018 12.4811 8.56562 12.3769 8.61608 12.2551C8.66654 12.1333 8.67974 11.9993 8.65402 11.8699C8.6283 11.7406 8.5648 11.6218 8.47157 11.5286C8.37833 11.4354 8.25954 11.3719 8.13022 11.3461C8.0009 11.3204 7.86686 11.3336 7.74504 11.3841C7.62322 11.4345 7.5191 11.52 7.44585 11.6296C7.3726 11.7392 7.3335 11.8681 7.3335 12C7.3335 12.1768 7.40373 12.3464 7.52876 12.4714C7.65378 12.5964 7.82335 12.6667 8.00016 12.6667ZM11.3335 12.6667C11.4654 12.6667 11.5942 12.6276 11.7039 12.5543C11.8135 12.4811 11.899 12.3769 11.9494 12.2551C11.9999 12.1333 12.0131 11.9993 11.9874 11.8699C11.9616 11.7406 11.8981 11.6218 11.8049 11.5286C11.7117 11.4354 11.5929 11.3719 11.4636 11.3461C11.3342 11.3204 11.2002 11.3336 11.0784 11.3841C10.9566 11.4345 10.8524 11.52 10.7792 11.6296C10.7059 11.7392 10.6668 11.8681 10.6668 12C10.6668 12.1768 10.7371 12.3464 10.8621 12.4714C10.9871 12.5964 11.1567 12.6667 11.3335 12.6667ZM11.3335 10C11.4654 10 11.5942 9.9609 11.7039 9.88764C11.8135 9.81439 11.899 9.71027 11.9494 9.58845C11.9999 9.46664 12.0131 9.33259 11.9874 9.20327C11.9616 9.07395 11.8981 8.95516 11.8049 8.86193C11.7117 8.76869 11.5929 8.7052 11.4636 8.67947C11.3342 8.65375 11.2002 8.66695 11.0784 8.71741C10.9566 8.76787 10.8524 8.85332 10.7792 8.96295C10.7059 9.07258 10.6668 9.20148 10.6668 9.33333C10.6668 9.51014 10.7371 9.67971 10.8621 9.80473C10.9871 9.92976 11.1567 10 11.3335 10ZM8.00016 10C8.13202 10 8.26091 9.9609 8.37054 9.88764C8.48018 9.81439 8.56562 9.71027 8.61608 9.58845C8.66654 9.46664 8.67974 9.33259 8.65402 9.20327C8.6283 9.07395 8.5648 8.95516 8.47157 8.86193C8.37833 8.76869 8.25954 8.7052 8.13022 8.67947C8.0009 8.65375 7.86686 8.66695 7.74504 8.71741C7.62322 8.76787 7.5191 8.85332 7.44585 8.96295C7.3726 9.07258 7.3335 9.20148 7.3335 9.33333C7.3335 9.51014 7.40373 9.67971 7.52876 9.80473C7.65378 9.92976 7.82335 10 8.00016 10ZM12.6668 2H12.0002V1.33333C12.0002 1.15652 11.9299 0.98695 11.8049 0.861926C11.6799 0.736902 11.5103 0.666664 11.3335 0.666664C11.1567 0.666664 10.9871 0.736902 10.8621 0.861926C10.7371 0.98695 10.6668 1.15652 10.6668 1.33333V2H5.3335V1.33333C5.3335 1.15652 5.26326 0.98695 5.13823 0.861926C5.01321 0.736902 4.84364 0.666664 4.66683 0.666664C4.49002 0.666664 4.32045 0.736902 4.19543 0.861926C4.0704 0.98695 4.00016 1.15652 4.00016 1.33333V2H3.3335C2.80306 2 2.29436 2.21071 1.91928 2.58578C1.54421 2.96086 1.3335 3.46956 1.3335 4V13.3333C1.3335 13.8638 1.54421 14.3725 1.91928 14.7475C2.29436 15.1226 2.80306 15.3333 3.3335 15.3333H12.6668C13.1973 15.3333 13.706 15.1226 14.081 14.7475C14.4561 14.3725 14.6668 13.8638 14.6668 13.3333V4C14.6668 3.46956 14.4561 2.96086 14.081 2.58578C13.706 2.21071 13.1973 2 12.6668 2ZM13.3335 13.3333C13.3335 13.5101 13.2633 13.6797 13.1382 13.8047C13.0132 13.9298 12.8436 14 12.6668 14H3.3335C3.15669 14 2.98712 13.9298 2.86209 13.8047C2.73707 13.6797 2.66683 13.5101 2.66683 13.3333V7.33333H13.3335V13.3333ZM13.3335 6H2.66683V4C2.66683 3.82319 2.73707 3.65362 2.86209 3.52859C2.98712 3.40357 3.15669 3.33333 3.3335 3.33333H4.00016V4C4.00016 4.17681 4.0704 4.34638 4.19543 4.4714C4.32045 4.59643 4.49002 4.66666 4.66683 4.66666C4.84364 4.66666 5.01321 4.59643 5.13823 4.4714C5.26326 4.34638 5.3335 4.17681 5.3335 4V3.33333H10.6668V4C10.6668 4.17681 10.7371 4.34638 10.8621 4.4714C10.9871 4.59643 11.1567 4.66666 11.3335 4.66666C11.5103 4.66666 11.6799 4.59643 11.8049 4.4714C11.9299 4.34638 12.0002 4.17681 12.0002 4V3.33333H12.6668C12.8436 3.33333 13.0132 3.40357 13.1382 3.52859C13.2633 3.65362 13.3335 3.82319 13.3335 4V6ZM4.66683 10C4.79868 10 4.92758 9.9609 5.03721 9.88764C5.14684 9.81439 5.23229 9.71027 5.28275 9.58845C5.33321 9.46664 5.34641 9.33259 5.32069 9.20327C5.29496 9.07395 5.23147 8.95516 5.13823 8.86193C5.045 8.76869 4.92621 8.7052 4.79689 8.67947C4.66757 8.65375 4.53352 8.66695 4.41171 8.71741C4.28989 8.76787 4.18577 8.85332 4.11252 8.96295C4.03926 9.07258 4.00016 9.20148 4.00016 9.33333C4.00016 9.51014 4.0704 9.67971 4.19543 9.80473C4.32045 9.92976 4.49002 10 4.66683 10ZM4.66683 12.6667C4.79868 12.6667 4.92758 12.6276 5.03721 12.5543C5.14684 12.4811 5.23229 12.3769 5.28275 12.2551C5.33321 12.1333 5.34641 11.9993 5.32069 11.8699C5.29496 11.7406 5.23147 11.6218 5.13823 11.5286C5.045 11.4354 4.92621 11.3719 4.79689 11.3461C4.66757 11.3204 4.53352 11.3336 4.41171 11.3841C4.28989 11.4345 4.18577 11.52 4.11252 11.6296C4.03926 11.7392 4.00016 11.8681 4.00016 12C4.00016 12.1768 4.0704 12.3464 4.19543 12.4714C4.32045 12.5964 4.49002 12.6667 4.66683 12.6667Z"
                  fill="#8B8B88"
                />
              </svg>
            </div>
            <input
              class="border border-gray-700 w-full font-rubik tracking-[-0.035rem] focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none bg-white p-2.5 text-sm pl-10 rounded-lg"
              data-testid="datepicker"
              placeholder="Select a Date"
              readonly=""
              value=""
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="flex flex-col items-start justify-center self-stretch"
      data-testid="drag-and-drop"
    >
      <p
        class="self-stretch mb-[0.44rem] mt-4 font-outfit font-semibold text-[0.625rem]"
      >
        components.CampaignInfoSidebar.labels.imagens
      </p>
      <div
        class="w-full h-auto drop-zone border-2 mt-1 flex flex-col justify-center items-center gap-2 p-2 py-4 rounded-[0.75rem] border-dashed border-gray-300 hover:border-yellow-900 active:border-yellow-900 bg-[#FCFCFC] hover:bg-[#FFCC3333] hover:text-yellow-900 text-gray-300"
      >
        <input
          accept="*/*"
          aria-label="components.drag-drop.input"
          class="hidden"
          data-testid="input-dragdrop"
          multiple=""
          type="file"
        />
        <svg
          class="fill-current text-inherit"
          height="24"
          viewBox="0 0 25 24"
          width="25"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M9.5 13H11.5V15C11.5 15.2652 11.6054 15.5196 11.7929 15.7071C11.9804 15.8946 12.2348 16 12.5 16C12.7652 16 13.0196 15.8946 13.2071 15.7071C13.3946 15.5196 13.5 15.2652 13.5 15V13H15.5C15.7652 13 16.0196 12.8946 16.2071 12.7071C16.3946 12.5196 16.5 12.2652 16.5 12C16.5 11.7348 16.3946 11.4804 16.2071 11.2929C16.0196 11.1054 15.7652 11 15.5 11H13.5V9C13.5 8.73478 13.3946 8.48043 13.2071 8.29289C13.0196 8.10536 12.7652 8 12.5 8C12.2348 8 11.9804 8.10536 11.7929 8.29289C11.6054 8.48043 11.5 8.73478 11.5 9V11H9.5C9.23478 11 8.98043 11.1054 8.79289 11.2929C8.60536 11.4804 8.5 11.7348 8.5 12C8.5 12.2652 8.60536 12.5196 8.79289 12.7071C8.98043 12.8946 9.23478 13 9.5 13ZM21.5 2H3.5C3.23478 2 2.98043 2.10536 2.79289 2.29289C2.60536 2.48043 2.5 2.73478 2.5 3V21C2.5 21.2652 2.60536 21.5196 2.79289 21.7071C2.98043 21.8946 3.23478 22 3.5 22H21.5C21.7652 22 22.0196 21.8946 22.2071 21.7071C22.3946 21.5196 22.5 21.2652 22.5 21V3C22.5 2.73478 22.3946 2.48043 22.2071 2.29289C22.0196 2.10536 21.7652 2 21.5 2ZM20.5 20H4.5V4H20.5V20Z"
            fill-opacity="0.95"
          />
        </svg>
        <p
          aria-label="components.drag-drop.p1"
          class="text-gray-800 font-outfit text-[0.75rem] not-italic font-semibold leading-[1rem]"
        >
          components.drag-drop.p1
        </p>
      </div>
    </div>
    <div
      class="flex flex-col justify-between w-[16.75rem]"
    >
      <p
        class="self-stretch mb-[0.44rem] mt-4 font-outfit font-semibold text-[0.625rem]"
      >
        components.CampaignInfoSidebar.labels.cor1
      </p>
      <div
        class="py-0 px-2 text-xs border rounded-lg flex bg-white w-full h-10 disabled:border-gray-200"
        data-testid="colorPicker"
      >
        <div
          class="top-2 left-0 rounded-full overflow-hidden w-6 h-6 border border-gray-200 bg-transparent p-0 appearance-none relative"
        >
          <input
            class="absolute -top-4 -left-4 w-12 h-12 p-0 appearance-none"
            data-testid="colorSample"
            type="color"
            value=""
          />
        </div>
        <input
          class="border-none h-9.25 border-transparent focus:border-transparent focus:ring-0 pl-2 appearance-none w-full"
          data-testid="colorCode"
          placeholder="#000000"
          type="text"
          value=""
        />
      </div>
      <p
        class="self-stretch mt-4 mb-[0.44rem] font-outfit font-semibold text-[0.625rem]"
      >
        components.CampaignInfoSidebar.labels.cor2
      </p>
      <div
        class="py-0 px-2 text-xs border rounded-lg flex bg-white w-full h-10 disabled:border-gray-200"
        data-testid="colorPicker"
      >
        <div
          class="top-2 left-0 rounded-full overflow-hidden w-6 h-6 border border-gray-200 bg-transparent p-0 appearance-none relative"
        >
          <input
            class="absolute -top-4 -left-4 w-12 h-12 p-0 appearance-none"
            data-testid="colorSample"
            type="color"
            value=""
          />
        </div>
        <input
          class="border-none h-9.25 border-transparent focus:border-transparent focus:ring-0 pl-2 appearance-none w-full"
          data-testid="colorCode"
          placeholder="#000000"
          type="text"
          value=""
        />
      </div>
    </div>
    <div
      class="flex flex-col mt-8 w-[16.75rem] items-start gap-8 justify-center shrink-0 px-[1.0305rem ]"
    >
      <button
        class="w-full py-3 px-4 rounded-md font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-blue-100 hover:bg-yellow-900 hover:text-white text-xs flex flex-row justify-center items-center h-12"
      >
        <p
          class="flex font-outfit font-medium text-center text-[0.78125rem]"
        >
          components.CampaignInfoSidebar.labels.posts1
        </p>
        <svg
          aria-hidden="true"
          class="flex w-[0.938rem] h-[0.938rem] shrink-0 stroke-[0.2rem] ml-[0.63rem]"
          data-slot="icon"
          fill="none"
          stroke="currentColor"
          stroke-width="1.5"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
      <button
        class="w-full py-3 px-4 rounded-md font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-xs flex flex-row h-12 justify-center items-center"
      >
        <p
          class="flex font-outfit font-medium text-center text-[0.78125rem]"
        >
          components.CampaignInfoSidebar.labels.posts2
        </p>
        <svg
          aria-hidden="true"
          class="flex w-[0.938rem] h-[0.938rem] shrink-0 stroke-[0.2rem] ml-[0.63rem]"
          data-slot="icon"
          fill="none"
          stroke="currentColor"
          stroke-width="1.5"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </div>
  </nav>
</DocumentFragment>
`;
