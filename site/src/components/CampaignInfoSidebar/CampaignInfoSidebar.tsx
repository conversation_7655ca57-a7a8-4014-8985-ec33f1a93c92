import { Heading } from "../Heading";
import { Text } from "../Text";
import { Input } from "../Input";
import { DatePicker } from "../DatePicker";
import { DragAndDrop } from "../DragAndDrop";
import { Button } from "../Button";
import { ColorPicker } from "../ColorPicker";
import { useTranslation } from "react-i18next";
import { HTMLAttributes, useState } from "react";
import { ArrowRightIcon } from "@heroicons/react/24/outline";

export interface CampaignInfoSidebarProps
  extends HTMLAttributes<HTMLDivElement> {}

export const CampaignInfoSidebar: React.FC<CampaignInfoSidebarProps> = (
  params: CampaignInfoSidebarProps,
) => {
  const { t } = useTranslation();
  const [selectedDate1, setSelectedDate1] = useState<Date | null>(null);
  const [selectedDate2, setSelectedDate2] = useState<Date | null>(null);

  function formatDate(date: Date) {
    let dia = String(date.getDate());
    let mes = String(date.getMonth() + 1);
    const ano = date.getFullYear();

    if (Number(dia) < 10) {
      dia = "0" + dia;
    }
    if (Number(mes) < 10) {
      mes = "0" + mes;
    }

    return dia + "/" + mes + "/" + ano;
  }

  function handleDateChange1(date: Date) {
    const formattedDate = formatDate(date);
    setSelectedDate1(date);
    return formattedDate;
  }

  function handleDateChange2(date: Date) {
    const formattedDate = formatDate(date);
    setSelectedDate2(date);
    return formattedDate;
  }

  function setLocale() {
    switch (t("components.CampaignInfoSidebar.datepicker.placeholders")) {
      case "US":
        return "en-US";
      case "BR":
        return "pt-BR";
      case "PT":
        return "pt-PT";
      default:
        return "en";
    }
  }

  return (
    <nav
      {...params}
      className="flex flex-col flex-auto items-start p-4 h-[59.5625rem] w-[18.75rem] shrink-0 bg-[#FFFFFF]"
    >
      <Heading size="3">
        {" "}
        {t("components.CampaignInfoSidebar.labels.heading")}
      </Heading>
      <div className="flex flex-col items-start w-[16.75rem]">
        <Text className="self-stretch mb-[0.44rem] mt-4 font-outfit font-semibold leading-4 text-[0.625rem]">
          {t("components.CampaignInfoSidebar.labels.text")}
        </Text>
        <Input
          aria-label="Nome da Campanha"
          className="flex h-10 w-[16.75rem] p-3 items-stretch self-stretch"
          placeholder="Nome da Campanha"
        />
      </div>
      <div className="flex flex-col justify-between">
        <Text className="self-stretch mb-[0.44rem] mt-4 font-outfit font-semibold leading-4 text-[0.625rem]">
          {t("components.CampaignInfoSidebar.labels.data1")}
        </Text>
        <DatePicker
          onSelectedDateChanged={handleDateChange1}
          language={setLocale()}
          variant="tertiary"
          className="h-10 w-[16.75rem]"
          value={selectedDate1 ? formatDate(selectedDate1) : ""}
        />
        <Text className="flex w-[0.375rem] mb-[0.44rem] mt-4 shrink-0 self-stretch font-outfit font-semibold leading-4 text-[0.625rem]">
          {t("components.CampaignInfoSidebar.labels.data2")}
        </Text>
        <DatePicker
          onSelectedDateChanged={handleDateChange2}
          language={setLocale()}
          variant="tertiary"
          className="h-10 w-[16.75rem]"
          value={selectedDate2 ? formatDate(selectedDate2) : ""}
        />
      </div>
      <div
        data-testid="drag-and-drop"
        className="flex flex-col items-start justify-center self-stretch"
      >
        <Text className="self-stretch mb-[0.44rem] mt-4 font-outfit font-semibold leading-4 text-[0.625rem]">
          {t("components.CampaignInfoSidebar.labels.imagens")}
        </Text>
        <DragAndDrop
          data-testid="drag-and-drop"
          onFilesDrop={function (): void {}}
        />
      </div>
      <div className="flex flex-col justify-between w-[16.75rem]">
        <Text className="self-stretch mb-[0.44rem] mt-4 font-outfit font-semibold leading-4 text-[0.625rem]">
          {t("components.CampaignInfoSidebar.labels.cor1")}
        </Text>
        <ColorPicker />
        <Text className="self-stretch mt-4 mb-[0.44rem] font-outfit font-semibold leading-4 text-[0.625rem]">
          {t("components.CampaignInfoSidebar.labels.cor2")}
        </Text>
        <ColorPicker />
      </div>
      <div className="flex flex-col mt-8 w-[16.75rem] items-start gap-8 justify-center shrink-0 px-[1.0305rem ]">
        <Button
          color="quintenary"
          className="flex flex-row justify-center items-center h-12"
        >
          <Text className="flex font-outfit font-medium text-center leading-4 text-[0.78125rem]">
            {t("components.CampaignInfoSidebar.labels.posts1")}
          </Text>
          <ArrowRightIcon className="flex w-[0.938rem] h-[0.938rem] shrink-0 stroke-[0.2rem] ml-[0.63rem]" />
        </Button>
        <Button className="flex flex-row h-12 justify-center items-center">
          <Text className="flex font-outfit font-medium text-center leading-4 text-[0.78125rem]">
            {t("components.CampaignInfoSidebar.labels.posts2")}
          </Text>
          <ArrowRightIcon className="flex w-[0.938rem] h-[0.938rem] shrink-0 stroke-[0.2rem] ml-[0.63rem]" />
        </Button>
      </div>
    </nav>
  );
};

export default CampaignInfoSidebar;
