import { BaseHTMLAttributes } from "react";
import { tv } from "tailwind-variants";

export interface HeadingProps extends BaseHTMLAttributes<HTMLHeadingElement> {
  size?: "1" | "2" | "3" | "4" | "5";
}

const style = tv({
  base: "font-outfit font-[700]",
  variants: {
    size: {
      "1": "text-[2.5rem]",
      "2": "text-[2rem]",
      "3": "text-[1.5rem]",
      "4": "text-[1rem]",
      "5": "text-[0.75rem]",
    },
  },
  compoundVariants: [
    {
      size: ["1", "2", "3", "4", "5"],
    },
  ],
  defaultVariants: {
    size: "1",
  },
});

export function Heading({
  children,
  size = "1",
  className,
  ...rest
}: HeadingProps) {
  return (
    <h1 className={style({ className, size })} {...rest}>
      {children}
    </h1>
  );
}

export default Heading;
