import { render, screen } from "@tests/render";

import Heading from "./Heading";

describe("<Heading />", () => {
  it("should render the heading", () => {
    render(<Heading>Heading</Heading>);
    expect(screen.getByText("Heading")).toMatchSnapshot();
  });

  it("should render the heading 2", () => {
    render(<Heading size="2">Heading</Heading>);
    expect(screen.getByText("Heading")).toMatchSnapshot();
  });

  it("should render the heading 3", () => {
    render(<Heading size="3">Heading</Heading>);
    expect(screen.getByText("Heading")).toMatchSnapshot();
  });

  it("should render the heading 4", () => {
    render(<Heading size="4">Heading</Heading>);
    expect(screen.getByText("Heading")).toMatchSnapshot();
  });

  it("should render the heading 5", () => {
    render(<Heading size="5">Heading</Heading>);
    expect(screen.getByText("Heading")).toMatchSnapshot();
  });
});
