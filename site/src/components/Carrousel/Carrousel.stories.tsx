import { <PERSON><PERSON>, <PERSON><PERSON>bj } from "@storybook/react";
import { Carrousel, CarrouselProps } from "./Carrousel";

const story: Meta<CarrouselProps> = {
  component: Carrousel,
  title: "Carrousel",
} as Meta;

export default story;

type Story = StoryObj<CarrouselProps>;

function createcontentList(amount: number) {
  const numbers = [];
  for (let i = 0; i < amount; i++) {
    numbers.push(
      <div className="bg-black-600 w-full h-full flex flex-col items-center justify-center text-white text-center px-5 py-28 rounded-lg">
        <p>Card {i + 1}</p>
        <p>Text of the card {i + 1}</p>
        <p>Much more text of the card {i + 1}</p>
      </div>,
    );
  }
  return numbers;
}

export const Default: Story = {
  args: {
    options: {
      show: {
        sm: 1,
        md: 2,
        lg: 3,
      },
      items: createcontentList(5),
    },
  },
};

export const PaginationLeft: Story = {
  args: {
    options: {
      show: {
        sm: 1,
        md: 2,
        lg: 3,
      },
      items: createcontentList(6),
    },
    position: "left",
  },
};

export const PaginationRight: Story = {
  args: {
    options: {
      show: {
        sm: 1,
        md: 2,
        lg: 3,
      },
      items: createcontentList(3),
    },
    position: "right",
  },
};

export const Small: Story = {
  args: {
    options: {
      show: {
        sm: 1,
        md: 2,
        lg: 3,
      },
      items: createcontentList(8),
    },
    paginationSize: "small",
  },
};

export const Large: Story = {
  args: {
    options: {
      show: {
        sm: 1,
        md: 2,
        lg: 3,
      },
      items: createcontentList(4),
    },
    paginationSize: "large",
  },
};
