import { ReactNode, useState } from "react";
import { tv } from "tailwind-variants";
import { Card } from "../Card";
import { Pagination } from "..";
import Carousel from "nuka-carousel";

export interface CarrouselProps {
  options: {
    show: {
      sm: number;
      md: number;
      lg: number;
    };
    items: ReactNode[];
  };
  paginationSize?: "small" | "normal" | "large";
  position?: "left" | "right" | "center";
  variant?: "primary" | "secondary";
  className?: string;
}

const style = tv({
  base: "flex flex-col gap-6 w-full",
});

const paginationStyle = tv({
  base: "flex flex-row w-full h-fit",
  variants: {
    position: {
      left: "justify-start",
      right: "justify-end",
      center: "justify-center",
    },
  },
  compoundVariants: [
    {
      position: ["center", "left", "right"],
    },
  ],
  defaultVariants: {
    position: "center",
  },
});

export function Carrousel({
  options,
  paginationSize = "normal",
  position = "center",
  variant = "primary",
  className,
  ...rest
}: CarrouselProps) {
  const [value, setValue] = useState(1);

  function handleChangeValue(value: number) {
    if (value < 1) value = 1;
    if (value > options.items.length) value = options.items.length;
    setValue(value);
    return value;
  }

  function sliderOptions() {
    if (innerWidth > 1023) {
      return {
        amount: options.show.lg,
        gap: 24,
        sliderPosition: options.show.lg === 2 ? "left" : position,
      };
    } else if (innerWidth > 767) {
      return {
        amount: options.show.md,
        gap: 16,
        sliderPosition: options.show.md === 2 ? "left" : position,
      };
    }
    return {
      amount: options.show.sm,
      gap: 8,
      sliderPosition: options.show.sm === 2 ? "left" : position,
    };
  }

  return (
    <div className={style({ className })} {...rest}>
      <Carousel
        withoutControls
        slideIndex={value - 1}
        slidesToShow={sliderOptions().amount}
        cellAlign={sliderOptions().sliderPosition}
        cellSpacing={sliderOptions().gap}
        afterSlide={(index) =>
          index + 1 !== value ? handleChangeValue(index + 1) : undefined
        }
      >
        {options.items.map((item, index) => (
          <Card key={index} color="quaternary" fullWidth>
            {item}
          </Card>
        ))}
      </Carousel>
      <div className={paginationStyle({ position })} data-testid="pagination">
        <Pagination
          amount={options.items.length}
          onChange={handleChangeValue}
          value={value}
          size={paginationSize}
          variant={variant}
        />
      </div>
    </div>
  );
}

export default Carrousel;
