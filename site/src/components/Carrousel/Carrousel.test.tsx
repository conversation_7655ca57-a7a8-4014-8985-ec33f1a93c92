import { render, screen } from "@tests/render";
import { Carrousel } from ".";
import { ReactNode } from "react";

describe("Carrousel component", () => {
  const content: ReactNode[] = [];

  for (let i = 0; i < 3; i++) {
    content.push(
      <div>
        <h2>Card Title {i}</h2>
        <p>Card Content {i}</p>
      </div>,
    );
  }

  it("should render the carrousel", () => {
    render(
      <Carrousel
        options={{ show: { sm: 1, md: 1, lg: 1 }, items: content }}
        data-testid="carrousel"
      />,
    );
    expect(screen.getByTestId("carrousel")).toMatchSnapshot();
  });

  it("should render the carrousel with pagination on left", () => {
    render(
      <Carrousel
        options={{ show: { sm: 1, md: 1, lg: 1 }, items: content }}
        position="left"
        data-testid="carrousel"
      />,
    );
    expect(screen.getByTestId("pagination")).toMatchSnapshot();
  });

  it("should render the carrousel with pagination on right", () => {
    render(
      <Carrousel
        options={{ show: { sm: 1, md: 1, lg: 1 }, items: content }}
        position="right"
        data-testid="carrousel"
      />,
    );
    expect(screen.getByTestId("pagination")).toMatchSnapshot();
  });

  it("should render the carrousel with small size pagination", () => {
    render(
      <Carrousel
        options={{ show: { sm: 1, md: 1, lg: 1 }, items: content }}
        paginationSize="small"
        data-testid="carrousel"
      />,
    );
    expect(screen.getByTestId("pagination")).toMatchSnapshot();
  });

  it("should render the carrousel with large size pagination", () => {
    render(
      <Carrousel
        options={{ show: { sm: 1, md: 1, lg: 1 }, items: content }}
        paginationSize="large"
        data-testid="carrousel"
      />,
    );
    expect(screen.getByTestId("pagination")).toMatchSnapshot();
  });
});
