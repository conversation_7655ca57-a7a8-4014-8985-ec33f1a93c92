// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Carrousel component should render the carrousel 1`] = `
<div
  class="flex flex-col gap-6 w-full"
  data-testid="carrousel"
>
  <div
    aria-label="Slider"
    aria-roledescription="carousel"
    class="slider-container"
    data-testid=":r0:"
    id=":r0:"
    role="group"
    style="position: relative;"
  >
    <div
      aria-atomic="true"
      aria-live="polite"
      style="position: absolute; width: 1px; height: 1px; overflow: hidden; padding: 0px; margin: -1px; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border: 0px;"
      tabindex="-1"
    >
      Slide 1 of 3
    </div>
    <div
      class="slider-frame"
      data-testid=":r0:-slider-frame"
      id=":r0:-slider-frame"
      style="overflow: hidden; width: 100%; position: relative; outline: none; height: auto; transition: height 300ms ease-in-out; will-change: height; user-select: none;"
      tabindex="-1"
    >
      <div
        class="slider-list"
        style="width: 300%; text-align: left; user-select: auto; display: flex;"
      >
        <div
          class="slide slide-current slide-visible"
          id=":r0:-slide-1"
          role="tabpanel"
          style="width: 33.333333333333336%; height: auto; padding: 0px 12px; opacity: 1;"
        >
          <div
            class="relative rounded-xl border-0 bg-transparent p-0"
          >
            <div>
              <h2>
                Card Title 
                0
              </h2>
              <p>
                Card Content 
                0
              </p>
            </div>
            <div
              class="absolute -top-[0.525rem] -right-[0.675rem] shrink h-42 w-32"
            />
          </div>
        </div>
        <div
          class="slide slide-visible"
          id=":r0:-slide-2"
          role="tabpanel"
          style="width: 33.333333333333336%; height: auto; padding: 0px 12px; opacity: 1;"
        >
          <div
            class="relative rounded-xl border-0 bg-transparent p-0"
          >
            <div>
              <h2>
                Card Title 
                1
              </h2>
              <p>
                Card Content 
                1
              </p>
            </div>
            <div
              class="absolute -top-[0.525rem] -right-[0.675rem] shrink h-42 w-32"
            />
          </div>
        </div>
        <div
          class="slide slide-visible"
          id=":r0:-slide-3"
          role="tabpanel"
          style="width: 33.333333333333336%; height: auto; padding: 0px 12px; opacity: 1;"
        >
          <div
            class="relative rounded-xl border-0 bg-transparent p-0"
          >
            <div>
              <h2>
                Card Title 
                2
              </h2>
              <p>
                Card Content 
                2
              </p>
            </div>
            <div
              class="absolute -top-[0.525rem] -right-[0.675rem] shrink h-42 w-32"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="flex flex-row w-full h-fit justify-center"
    data-testid="pagination"
  >
    <div
      class="flex flex-row gap-2 items-center w-fit"
    >
      <div
        class="stroke-[0.25rem] cursor-pointer min-w-[1.75rem]"
      />
      <div
        class="w-fit"
      >
        <button
          class="py-3 px-4 font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-xs rounded-full text-center flex items-center justify-center w-10 h-10"
          data-testid="button-1"
        >
          <p
            class="select-none tracking-normal"
          >
            1
          </p>
        </button>
      </div>
      <div
        class="w-fit"
      >
        <button
          class="py-3 px-4 font-outfit font-medium tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-xs rounded-full text-center flex items-center justify-center w-10 h-10"
          data-testid="button-2"
        >
          <p
            class="select-none tracking-normal"
          >
            2
          </p>
        </button>
      </div>
      <div
        class="w-fit"
      >
        <button
          class="py-3 px-4 font-outfit font-medium tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-xs rounded-full text-center flex items-center justify-center w-10 h-10"
          data-testid="button-3"
        >
          <p
            class="select-none tracking-normal"
          >
            3
          </p>
        </button>
      </div>
      <div
        class="stroke-[0.25rem] cursor-pointer min-w-[1.75rem]"
      >
        <svg
          aria-hidden="true"
          class="stroke-[0.25rem] cursor-pointer min-w-[1.75rem]"
          data-slot="icon"
          data-testid="right-icon"
          fill="none"
          stroke="currentColor"
          stroke-width="1.5"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="m8.25 4.5 7.5 7.5-7.5 7.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
    </div>
  </div>
</div>
`;

exports[`Carrousel component should render the carrousel with large size pagination 1`] = `
<div
  class="flex flex-row w-full h-fit justify-center"
  data-testid="pagination"
>
  <div
    class="flex flex-row gap-2 items-center w-fit"
  >
    <div
      class="stroke-[0.25rem] cursor-pointer min-w-[2rem]"
    />
    <div
      class="w-fit"
    >
      <button
        class="font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-sm px-5 py-[0.875rem] rounded-full text-center flex items-center justify-center w-12 h-12"
        data-testid="button-1"
      >
        <p
          class="select-none tracking-normal"
        >
          1
        </p>
      </button>
    </div>
    <div
      class="w-fit"
    >
      <button
        class="font-outfit font-medium tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-sm px-5 py-[0.875rem] rounded-full text-center flex items-center justify-center w-12 h-12"
        data-testid="button-2"
      >
        <p
          class="select-none tracking-normal"
        >
          2
        </p>
      </button>
    </div>
    <div
      class="w-fit"
    >
      <button
        class="font-outfit font-medium tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-sm px-5 py-[0.875rem] rounded-full text-center flex items-center justify-center w-12 h-12"
        data-testid="button-3"
      >
        <p
          class="select-none tracking-normal"
        >
          3
        </p>
      </button>
    </div>
    <div
      class="stroke-[0.25rem] cursor-pointer min-w-[2rem]"
    >
      <svg
        aria-hidden="true"
        class="stroke-[0.25rem] cursor-pointer min-w-[2rem]"
        data-slot="icon"
        data-testid="right-icon"
        fill="none"
        stroke="currentColor"
        stroke-width="1.5"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="m8.25 4.5 7.5 7.5-7.5 7.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </div>
  </div>
</div>
`;

exports[`Carrousel component should render the carrousel with pagination on left 1`] = `
<div
  class="flex flex-row w-full h-fit justify-start"
  data-testid="pagination"
>
  <div
    class="flex flex-row gap-2 items-center w-fit"
  >
    <div
      class="stroke-[0.25rem] cursor-pointer min-w-[1.75rem]"
    />
    <div
      class="w-fit"
    >
      <button
        class="py-3 px-4 font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-xs rounded-full text-center flex items-center justify-center w-10 h-10"
        data-testid="button-1"
      >
        <p
          class="select-none tracking-normal"
        >
          1
        </p>
      </button>
    </div>
    <div
      class="w-fit"
    >
      <button
        class="py-3 px-4 font-outfit font-medium tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-xs rounded-full text-center flex items-center justify-center w-10 h-10"
        data-testid="button-2"
      >
        <p
          class="select-none tracking-normal"
        >
          2
        </p>
      </button>
    </div>
    <div
      class="w-fit"
    >
      <button
        class="py-3 px-4 font-outfit font-medium tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-xs rounded-full text-center flex items-center justify-center w-10 h-10"
        data-testid="button-3"
      >
        <p
          class="select-none tracking-normal"
        >
          3
        </p>
      </button>
    </div>
    <div
      class="stroke-[0.25rem] cursor-pointer min-w-[1.75rem]"
    >
      <svg
        aria-hidden="true"
        class="stroke-[0.25rem] cursor-pointer min-w-[1.75rem]"
        data-slot="icon"
        data-testid="right-icon"
        fill="none"
        stroke="currentColor"
        stroke-width="1.5"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="m8.25 4.5 7.5 7.5-7.5 7.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </div>
  </div>
</div>
`;

exports[`Carrousel component should render the carrousel with pagination on right 1`] = `
<div
  class="flex flex-row w-full h-fit justify-end"
  data-testid="pagination"
>
  <div
    class="flex flex-row gap-2 items-center w-fit"
  >
    <div
      class="stroke-[0.25rem] cursor-pointer min-w-[1.75rem]"
    />
    <div
      class="w-fit"
    >
      <button
        class="py-3 px-4 font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-xs rounded-full text-center flex items-center justify-center w-10 h-10"
        data-testid="button-1"
      >
        <p
          class="select-none tracking-normal"
        >
          1
        </p>
      </button>
    </div>
    <div
      class="w-fit"
    >
      <button
        class="py-3 px-4 font-outfit font-medium tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-xs rounded-full text-center flex items-center justify-center w-10 h-10"
        data-testid="button-2"
      >
        <p
          class="select-none tracking-normal"
        >
          2
        </p>
      </button>
    </div>
    <div
      class="w-fit"
    >
      <button
        class="py-3 px-4 font-outfit font-medium tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-xs rounded-full text-center flex items-center justify-center w-10 h-10"
        data-testid="button-3"
      >
        <p
          class="select-none tracking-normal"
        >
          3
        </p>
      </button>
    </div>
    <div
      class="stroke-[0.25rem] cursor-pointer min-w-[1.75rem]"
    >
      <svg
        aria-hidden="true"
        class="stroke-[0.25rem] cursor-pointer min-w-[1.75rem]"
        data-slot="icon"
        data-testid="right-icon"
        fill="none"
        stroke="currentColor"
        stroke-width="1.5"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="m8.25 4.5 7.5 7.5-7.5 7.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </div>
  </div>
</div>
`;

exports[`Carrousel component should render the carrousel with small size pagination 1`] = `
<div
  class="flex flex-row w-full h-fit justify-center"
  data-testid="pagination"
>
  <div
    class="flex flex-row gap-2 items-center w-fit"
  >
    <div
      class="stroke-[0.25rem] cursor-pointer min-w-[1rem]"
    />
    <div
      class="w-fit"
    >
      <button
        class="font-outfit text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-[0.5625rem] font-semibold py-[0.4532rem] px-3 rounded-full text-center flex items-center justify-center w-7 h-7"
        data-testid="button-1"
      >
        <p
          class="select-none tracking-normal"
        >
          1
        </p>
      </button>
    </div>
    <div
      class="w-fit"
    >
      <button
        class="font-outfit tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-[0.5625rem] font-semibold py-[0.4532rem] px-3 rounded-full text-center flex items-center justify-center w-7 h-7"
        data-testid="button-2"
      >
        <p
          class="select-none tracking-normal"
        >
          2
        </p>
      </button>
    </div>
    <div
      class="w-fit"
    >
      <button
        class="font-outfit tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-[0.5625rem] font-semibold py-[0.4532rem] px-3 rounded-full text-center flex items-center justify-center w-7 h-7"
        data-testid="button-3"
      >
        <p
          class="select-none tracking-normal"
        >
          3
        </p>
      </button>
    </div>
    <div
      class="stroke-[0.25rem] cursor-pointer min-w-[1rem]"
    >
      <svg
        aria-hidden="true"
        class="stroke-[0.25rem] cursor-pointer min-w-[1rem]"
        data-slot="icon"
        data-testid="right-icon"
        fill="none"
        stroke="currentColor"
        stroke-width="1.5"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="m8.25 4.5 7.5 7.5-7.5 7.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </div>
  </div>
</div>
`;
