import { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { Calendar, CalendarProps, CalendarItem } from "./Calendar";

const story: Meta<CalendarProps> = {
  component: Calendar,
  title: "Calendar",
  render: ({ ...args }) => <Calendar {...args} />,
} as Meta;

export default story;

type Story = StoryObj<CalendarProps>;

const colors = {
  twitter: "#56CCF2",
  facebook: "#2476F1",
  linkedin: "#2967BC",
  youtube: "#D63649",
  whatsapp: "#27AE60",
  instagram: "#C13584",
  tiktok: "#000",
};

export const Default: Story = {};

const today = new Date();
const CalendarItems: CalendarItem[] = [];
for (let i = 1; i < 29; i++) {
  if (i % 7 === 0) {
    CalendarItems.push({
      date: new Date(today.getFullYear(), today.getMonth(), i),
      colors: [colors.twitter, colors.tiktok, colors.youtube, colors.instagram],
      content: (
        <ul>
          <li>Twitter</li>
          <li>TikTok</li>
          <li>Youtube</li>
          <li>Instagram</li>
        </ul>
      ),
    });
  } else if (i % 3 === 0) {
    CalendarItems.push({
      date: new Date(today.getFullYear(), today.getMonth(), i),
      colors: [
        colors.twitter,
        colors.whatsapp,
        colors.facebook,
        colors.linkedin,
        colors.youtube,
        colors.instagram,
      ],
      content: (
        <ul>
          <li>Twitter</li>
          <li>WhatsApp</li>
          <li>Facebook</li>
          <li>Linkedin</li>
          <li>Youtube</li>
          <li>Instagram</li>
        </ul>
      ),
    });
  } else if (i % 2 === 0) {
    CalendarItems.push({
      date: new Date(today.getFullYear(), today.getMonth(), i),
      colors: [colors.tiktok, colors.facebook, colors.linkedin],
      content: (
        <ul>
          <li>TikTok</li>
          <li>Facebook</li>
          <li>Linkedin</li>
        </ul>
      ),
    });
  } else {
    CalendarItems.push({
      date: new Date(today.getFullYear(), today.getMonth(), i),
      colors: [colors.twitter, colors.linkedin],
      content: (
        <ul>
          <li>Twitter</li>
          <li>Linkedin</li>
        </ul>
      ),
    });
  }
}

export const WithItems: Story = {
  args: {
    items: CalendarItems,
  },
};
