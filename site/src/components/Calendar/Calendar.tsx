import { Calendar as BigCalendar, dateFnsLocalizer } from "react-big-calendar";
import format from "date-fns/format";
import parse from "date-fns/parse";
import startOfWeek from "date-fns/startOfWeek";
import getDay from "date-fns/getDay";
import { enUS } from "date-fns/locale";
import { Text } from "..";
import "./index.css";
import { useTranslation } from "react-i18next";
import { ReactNode } from "react";
import { CustomToolbar } from "./components/CustomToolbar";
import { CustomCalendarTile } from "./components/CustomCalendarTile";

export type CalendarItem = {
  date: Date;
  colors: string[];
  content: ReactNode;
};

export interface CalendarProps {
  items?: CalendarItem[];
  defaultDate?: Date | undefined;
}

export function Calendar({ items, defaultDate = undefined }: CalendarProps) {
  const { t } = useTranslation();

  const locales = {
    "en-US": enUS,
  };

  const localizer = dateFnsLocalizer({
    format,
    parse,
    startOfWeek,
    getDay,
    locales,
  });

  return (
    <div
      className="h-fit w-[20.90281rem] p-6 border rounded-xl border-gray-700"
      data-testid="calendar"
    >
      <BigCalendar
        localizer={localizer}
        defaultView="month"
        views={{ agenda: false, day: false, month: true }}
        defaultDate={defaultDate}
        components={{
          toolbar: CustomToolbar,
          month: {
            dateHeader: ({ label, isOffRange, date }) =>
              CustomCalendarTile({ label, isOffRange, date, items }),
            header: ({ date }) => (
              <Text className="text-center text-[0.625rem] font-semibold font-outfit leading-4 uppercase text-gray-800 mt-4">
                {t(`components.Calendar.week.${date.getDay()}`)}
              </Text>
            ),
          },
        }}
      />
    </div>
  );
}

export default Calendar;
