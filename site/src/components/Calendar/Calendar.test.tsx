import { render, screen, fireEvent } from "@tests/render";

import Calendar from "./Calendar";

describe("<Calendar />", () => {
  it("should render the Calendar", () => {
    render(<Calendar defaultDate={new Date(2023, 2, 5)} />);
    expect(screen.getByTestId("calendar")).toMatchSnapshot();
  });

  it("should change to anterior month clicking left arrow", () => {
    render(<Calendar defaultDate={new Date(2023, 2, 5)} />);
    fireEvent.click(screen.getByTestId("calendar-left-arrow"));
    expect(screen.getByTestId("calendar")).toMatchSnapshot();
  });

  it("should change to next month clicking rigth arrow", () => {
    render(<Calendar defaultDate={new Date(2023, 2, 5)} />);
    fireEvent.click(screen.getByTestId("calendar-rigth-arrow"));
    expect(screen.getByTestId("calendar")).toMatchSnapshot();
  });
});
