import { Popover, Text } from "@/components";
import { CalendarItem } from "../Calendar";
import { tv } from "tailwind-variants";

export interface CustomToolbarProps {
  label: string;
  isOffRange: boolean;
  date: Date;
  items: CalendarItem[] | undefined;
}

const tileStyle = tv({
  base: "flex flex-col h-9 justify-center items-center border rounded-md",
  variants: {
    color: {
      primary: "border-black-900 bg-yellow-800",
      secondary: "border-transparent hover:bg-amber-300",
    },
  },
  compoundVariants: [
    {
      color: ["primary", "secondary"],
    },
  ],
  defaultVariants: {
    color: "primary",
  },
});

export function CustomCalendarTile({
  label,
  isOffRange,
  date,
  items,
}: CustomToolbarProps) {
  const today = new Date();
  if (items) {
    for (let i = 0; i < items.length; i++) {
      if (
        items[i].date.getDate() === date.getDate() &&
        items[i].date.getMonth() === date.getMonth() &&
        items[i].date.getFullYear() === date.getFullYear()
      ) {
        return (
          <Popover content={items[i].content}>
            <div
              className={tileStyle({
                color:
                  items[i].date.getDate() === today.getDate() &&
                  !isOffRange &&
                  items[i].date.getMonth() === today.getMonth() &&
                  items[i].date.getFullYear() === today.getFullYear()
                    ? "primary"
                    : "secondary",
              })}
            >
              <Text className="font-outfit font-normal text-center justify-center cursor-default">
                {label}
              </Text>
              <div className="flex flex-row justify-center items-center w-[1.48205rem] gap-[1px] flex-wrap">
                {items[i].colors.map((col) => {
                  return (
                    <svg className="h-[0.30rem] w-[0.30rem]">
                      <ellipse cx="2" cy="2" rx="2" ry="2" fill={col} />
                    </svg>
                  );
                })}
              </div>
            </div>
          </Popover>
        );
      }
    }
  }
  return (
    <div
      className={tileStyle({
        color:
          date.getDate() === today.getDate() &&
          !isOffRange &&
          date.getMonth() === today.getMonth() &&
          date.getFullYear() === today.getFullYear()
            ? "primary"
            : "secondary",
      })}
    >
      <Text className="font-outfit font-normal text-center justify-center cursor-default">
        {label}
      </Text>
    </div>
  );
}
