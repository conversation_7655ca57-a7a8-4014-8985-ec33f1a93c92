import { NavigateAction } from "react-big-calendar";
import { ArrowLeftIcon, ArrowRightIcon } from "@heroicons/react/24/outline";
import { Heading } from "@/components";
import { useTranslation } from "react-i18next";

export interface CustomToolbarProps {
  onNavigate: (navigate: NavigateAction, date?: Date | undefined) => void;
  date: Date;
}

export function CustomToolbar({ onNavigate, date }: CustomToolbarProps) {
  const { t } = useTranslation();
  return (
    <div
      className="flex flex-row justify-between items-center"
      data-testid="calendar-header"
    >
      <ArrowLeftIcon
        className="cursor-pointer h-3 stroke-[0.25rem] pr-4"
        data-testid="calendar-left-arrow"
        onClick={() => onNavigate("PREV")}
      />
      <Heading size="4" className="font-semibold">
        {t(`components.Calendar.month.${date.getMonth()}`)}
        {date.getFullYear()}
      </Heading>
      <ArrowRightIcon
        className="cursor-pointer h-3 stroke-[0.25rem] pl-4"
        data-testid="calendar-rigth-arrow"
        onClick={() => onNavigate("NEXT")}
      />
    </div>
  );
}
