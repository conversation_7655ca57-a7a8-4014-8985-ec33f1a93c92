import { ChangeEvent, FocusEvent, useState } from "react";
import { tv } from "tailwind-variants";

export interface ColorPickerProps {
  onChange?: (color: string) => void;
  onFocus?: FocusEvent;
  onBlur?: FocusEvent;
  value?: string;
}

const style = tv({
  slots: {
    base: "py-0 px-2 text-xs border rounded-lg flex bg-white w-full h-10 disabled:border-gray-200",
    baseFocused:
      "py-0 px-2 text-xs border rounded-lg flex bg-white w-full h-10 disabled:border-gray-200 border border-black-900 shadow-[0_0_4px_0_rgba(0,0,0,0.3)]",
    colorString:
      "border-none h-9.25 border-transparent focus:border-transparent focus:ring-0 pl-2  appearance-none w-full",
    colorContainer:
      "top-2 left-0 rounded-full overflow-hidden w-6 h-6 border border-gray-200 bg-transparent p-0 appearance-none relative",
    colorSample: "absolute -top-4 -left-4 w-12 h-12 p-0 appearance-none",
  },
});

const { base, colorContainer, colorSample, colorString, baseFocused } = style();

export default function ColorPicker({
  onChange,
  value = "",
}: ColorPickerProps) {
  const [colorCode, setColorCode] = useState(value);
  const [focused, setFocused] = useState(false);

  const handleFocused = (e: FocusEvent<HTMLInputElement>) => {
    if (e.type === "blur") {
      setFocused(false);
    }
    if (e.type === "focus") {
      setFocused(true);
    }
  };

  const handleColor = (e: ChangeEvent<HTMLInputElement>) => {
    onChange && onChange(e.target.value);
    setColorCode(e.target.value);
  };

  return (
    <div className={focused ? baseFocused() : base()} data-testid="colorPicker">
      <div className={colorContainer()}>
        <input
          className={colorSample()}
          onChange={handleColor}
          type="color"
          data-testid="colorSample"
          value={colorCode}
          onFocus={handleFocused}
          onBlur={handleFocused}
        />
      </div>
      <input
        className={colorString()}
        type="text"
        onChange={handleColor}
        placeholder="#000000"
        value={colorCode}
        data-testid="colorCode"
        onFocus={handleFocused}
        onBlur={handleFocused}
      />
    </div>
  );
}
