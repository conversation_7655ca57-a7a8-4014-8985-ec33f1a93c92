import { fireEvent, render, screen } from "@tests/render";
import ColorPicker from "./ColorPicker";

describe("<ColorPicker />", () => {
  it("should render the ColorPicker", () => {
    render(<ColorPicker />);
    expect(screen.getByTestId("colorPicker")).toMatchSnapshot();
  });

  it("should call onChange when select a color", () => {
    const handleChangeColor = jest.fn();

    render(<ColorPicker onChange={handleChangeColor} />);
    const colorInputElement = screen.getByTestId("colorSample");
    fireEvent.input(colorInputElement, { target: { value: "#333333" } });
    expect(handleChangeColor).toHaveBeenCalledWith("#333333");
  });

  it("should call onChange when type a color hexdecimal code", () => {
    const handleChangeColorHexCode = jest.fn();

    render(<ColorPicker onChange={handleChangeColorHexCode} />);
    const colorCodeInputElement = screen.getByTestId("colorCode");
    fireEvent.input(colorCodeInputElement, { target: { value: "#333333" } });
    expect(handleChangeColorHexCode).toHaveBeenCalledWith("#333333");
  });
});
