// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<ColorPicker /> should render the ColorPicker 1`] = `
<div
  class="py-0 px-2 text-xs border rounded-lg flex bg-white w-full h-10 disabled:border-gray-200"
  data-testid="colorPicker"
>
  <div
    class="top-2 left-0 rounded-full overflow-hidden w-6 h-6 border border-gray-200 bg-transparent p-0 appearance-none relative"
  >
    <input
      class="absolute -top-4 -left-4 w-12 h-12 p-0 appearance-none"
      data-testid="colorSample"
      type="color"
      value=""
    />
  </div>
  <input
    class="border-none h-9.25 border-transparent focus:border-transparent focus:ring-0 pl-2 appearance-none w-full"
    data-testid="colorCode"
    placeholder="#000000"
    type="text"
    value=""
  />
</div>
`;
