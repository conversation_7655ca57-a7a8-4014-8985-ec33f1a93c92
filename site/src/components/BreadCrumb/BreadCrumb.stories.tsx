import { Story, Meta } from "@storybook/react";
import { BreadCrumb, BreadCrumbProps } from "./BreadCrumb";
import { HomeIcon } from "@heroicons/react/24/solid";

export default {
  title: "Components/BreadCrumb",
  component: BreadCrumb,
} as Meta;

const Template: Story<BreadCrumbProps> = (args) => <BreadCrumb {...args} />;

export const Breadcrumb = Template.bind({});
Breadcrumb.args = {
  items: [
    {
      label: "Home",
      link: "/",
      icon: <HomeIcon className="mr-1 opacity-80 fill-gray-800 w-5 h-5" />,
    },
    { label: "Campaigns", link: "/" },
    { label: "Campaign Details", link: "/" },
  ],
};
