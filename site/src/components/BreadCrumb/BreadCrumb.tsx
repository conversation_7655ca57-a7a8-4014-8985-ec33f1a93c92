import { ReactNode } from "react";
import { Link } from "..";
import { Text } from "..";

export interface BreadCrumbProps {
  items: { label: string; link: string; icon?: ReactNode }[];
}

export function BreadCrumb({ items }: BreadCrumbProps) {
  return (
    <nav aria-label="Breadcrumb">
      <ol className="flex flex-row gap-1 items-center">
        {items.map((item, index) => (
          <li key={index} className="flex items-center">
            {item.icon && <span>{item.icon}</span>}
            {index === items.length - 1 ? (
              <Text className="font-outfit text-[0.875rem] font-[500] capitalize">
                {item.label}
              </Text>
            ) : (
              <Link
                variant="tertiary"
                className="mr-1.5 leading-[0.875rem] capitalize tracking-[0.0625rem] font-medium text-sm font-outfit not-italic"
                href={item.link}
              >
                {item.label}
              </Link>
            )}
            {index < items.length - 1 && (
              <span className="mr-[0.875rem} opacity-80 fill-gray-800 w-2 text-[0.813rem]">
                /
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}

export default BreadCrumb;
