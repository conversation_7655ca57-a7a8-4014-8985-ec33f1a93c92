import "@testing-library/jest-dom";
import { render, screen } from "@tests/render";
import BreadCrumb from "./BreadCrumb";

describe("BreadCrumb Component", () => {
  const breadcrumbItems = [
    { label: "Home", link: "/" },
    { label: "Campaigns", link: "/" },
    { label: "Campaign Details", link: "/" },
  ];

  it("renders the correct number of breadcrumb items", () => {
    render(<BreadCrumb items={breadcrumbItems} />);

    const breadcrumbList = screen.getAllByRole("listitem");
    expect(breadcrumbList).toHaveLength(breadcrumbItems.length);
  });

  it("renders the last item as the active item", () => {
    render(<BreadCrumb items={breadcrumbItems} />);

    const lastItem = screen.getByText("Campaign Details");
    expect(lastItem).toHaveClass(
      "font-outfit text-[0.875rem] font-[500] capitalize",
    );
  });
});
