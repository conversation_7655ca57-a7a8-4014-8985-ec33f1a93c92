import React, { useState, useRef, MouseEvent } from "react";
import { useTranslation } from "react-i18next";
import { tv } from "tailwind-variants";
import { Button } from "../Button";
import { XMarkIcon } from "@heroicons/react/16/solid";

export interface DragAndDropProps {
  onFilesDrop: (files: { file: File; objectURL: string }[]) => void;
}

const styleBase = tv({
  base: "w-full h-auto drop-zone border-2 mt-1 flex flex-col justify-center items-center gap-2 p-2 py-4 rounded-[0.75rem] border-dashed border-gray-300 hover:border-yellow-900 active:border-yellow-900 bg-[#FCFCFC] hover:bg-[#FFCC3333] hover:text-yellow-900 text-gray-300 ",
  variants: {
    hasContent: {
      true: "border-yellow-900",
      false: "",
    },
  },
});

const styleList = tv({
  variants: {
    states: {
      true: "flex relative h-6 left-1 whitespace-nowrap",
      false: "flex flex-col items-center justify-center",
    },
  },
});

const styleImagem = tv({
  variants: {
    statesImage: {
      true: "rounded-lg w-6 h-6",
      false: "rounded-lg w-12 h-12",
    },
  },
});

const styleBordaCinzaIcone = tv({
  variants: {
    statesBordaIcone: {
      true: "flex relative bg-gray-100 items-center justify-center rounded-[0.5rem] p-0 h-1.5rem w-1.5rem",
      false:
        "flex flex-row bg-gray-100 items-center justify-center rounded-[0.5rem] p-1 h-12 w-12",
    },
  },
});

const styleButtom = tv({
  variants: {
    statesButtom: {
      true: "flex relative items-center justify-center rounded-full p-0 w-5 h-5 bg-gray-800 mt-1 text-white hover:text-white hover:bg-red-700",
      false:
        "flex relative items-center justify-center left-1 p-1 w-8 h-8 bg-gray-800 rounded-full text-white hover:text-white hover:bg-red-700",
    },
  },
});

const DragAndDrop: React.FC<DragAndDropProps> = () => {
  const [selectedFiles, setSelectedFiles] = useState<
    { file: File; objectURL: string }[]
  >([]);
  const [showMore, setShowMore] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFilesDrop = async (droppedFiles: File[]) => {
    const newFiles = droppedFiles.map((file) => ({
      file,
      objectURL: URL.createObjectURL(file),
    }));

    setSelectedFiles((prevFiles) => prevFiles.concat(newFiles));
  };

  const handleFilesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const droppedFiles = Array.from(e.target.files || []);
    if (droppedFiles.length > 0) {
      handleFilesDrop(droppedFiles);
    }
  };

  const handleClick = (event: MouseEvent) => {
    if (
      !["BUTTON", "A", "SPAN", "svg"].includes(
        (event.target as HTMLElement).tagName,
      )
    ) {
      fileInputRef.current?.click();
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (e.dataTransfer.items) {
      const droppedFiles = Array.from(e.dataTransfer.items)
        .filter((item) => item.kind === "file")
        .map((item) => item.getAsFile())
        .filter((item): item is File => item !== null);
      handleFilesDrop(droppedFiles);
    }
  };

  const handleRemoveFile = (index: number) => {
    const updatedFiles = [...selectedFiles];
    updatedFiles.splice(index, 1);
    setSelectedFiles(updatedFiles);
  };

  const handleShowMoreFiles = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    setShowMore(!showMore);
  };

  const { t } = useTranslation();
  return (
    <div
      onClick={handleClick}
      onDrop={handleDrop}
      className={styleBase({
        hasContent: selectedFiles.length > 0,
      })}
    >
      <input
        data-testid="input-dragdrop"
        ref={fileInputRef}
        type="file"
        accept="*/*"
        onChange={handleFilesChange}
        multiple
        className="hidden"
        aria-label={t("components.drag-drop.input")}
      />
      {selectedFiles.length === 0 ? (
        <>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="25"
            height="24"
            viewBox="0 0 25 24"
            className="fill-current text-inherit"
          >
            <path
              d="M9.5 13H11.5V15C11.5 15.2652 11.6054 15.5196 11.7929 15.7071C11.9804 15.8946 12.2348 16 12.5 16C12.7652 16 13.0196 15.8946 13.2071 15.7071C13.3946 15.5196 13.5 15.2652 13.5 15V13H15.5C15.7652 13 16.0196 12.8946 16.2071 12.7071C16.3946 12.5196 16.5 12.2652 16.5 12C16.5 11.7348 16.3946 11.4804 16.2071 11.2929C16.0196 11.1054 15.7652 11 15.5 11H13.5V9C13.5 8.73478 13.3946 8.48043 13.2071 8.29289C13.0196 8.10536 12.7652 8 12.5 8C12.2348 8 11.9804 8.10536 11.7929 8.29289C11.6054 8.48043 11.5 8.73478 11.5 9V11H9.5C9.23478 11 8.98043 11.1054 8.79289 11.2929C8.60536 11.4804 8.5 11.7348 8.5 12C8.5 12.2652 8.60536 12.5196 8.79289 12.7071C8.98043 12.8946 9.23478 13 9.5 13ZM21.5 2H3.5C3.23478 2 2.98043 2.10536 2.79289 2.29289C2.60536 2.48043 2.5 2.73478 2.5 3V21C2.5 21.2652 2.60536 21.5196 2.79289 21.7071C2.98043 21.8946 3.23478 22 3.5 22H21.5C21.7652 22 22.0196 21.8946 22.2071 21.7071C22.3946 21.5196 22.5 21.2652 22.5 21V3C22.5 2.73478 22.3946 2.48043 22.2071 2.29289C22.0196 2.10536 21.7652 2 21.5 2ZM20.5 20H4.5V4H20.5V20Z"
              fillOpacity="0.95"
            />
          </svg>
          <p
            aria-label={t("components.drag-drop.p1")}
            className="text-gray-800 font-outfit text-[0.75rem] not-italic font-semibold leading-[1rem]"
          >
            {t("components.drag-drop.p1")}
          </p>
        </>
      ) : (
        <>
          {selectedFiles.slice(0, 3).map((fileObj, index) => (
            <div
              key={index}
              className={styleList({ states: selectedFiles.length >= 2 })}
            >
              <div className="flex overflow-hidden justify-center items-center">
                {fileObj.file.type.startsWith("image/") ? (
                  <img
                    data-testid="selected-text"
                    src={fileObj.objectURL}
                    alt={fileObj.file.name}
                    className={styleImagem({
                      statesImage: selectedFiles.length >= 2,
                    })}
                  />
                ) : (
                  <div className="flex flex-row overflow-hidden justify-center items-center">
                    <div
                      className={styleBordaCinzaIcone({
                        statesBordaIcone: selectedFiles.length >= 2,
                      })}
                    >
                      <svg
                        className={styleImagem({
                          statesImage: selectedFiles.length >= 2,
                        })}
                        xmlns="http://www.w3.org/2000/svg"
                        width="2"
                        height="2"
                        viewBox="0 0 25 24"
                        fill="none"
                      >
                        <path
                          d="M20.5 8.94C20.4896 8.84813 20.4695 8.75763 20.44 8.67V8.58C20.3919 8.47718 20.3278 8.38267 20.25 8.3L14.25 2.3C14.1673 2.22222 14.0728 2.15808 13.97 2.11H13.88L13.56 2H7.5C6.70435 2 5.94129 2.31607 5.37868 2.87868C4.81607 3.44129 4.5 4.20435 4.5 5V19C4.5 19.7956 4.81607 20.5587 5.37868 21.1213C5.94129 21.6839 6.70435 22 7.5 22H17.5C18.2956 22 19.0587 21.6839 19.6213 21.1213C20.1839 20.5587 20.5 19.7956 20.5 19V9V8.94ZM14.5 5.41L17.09 8H14.5V5.41ZM18.5 19C18.5 19.2652 18.3946 19.5196 18.2071 19.7071C18.0196 19.8946 17.7652 20 17.5 20H7.5C7.23478 20 6.98043 19.8946 6.79289 19.7071C6.60536 19.5196 6.5 19.2652 6.5 19V5C6.5 4.73478 6.60536 4.48043 6.79289 4.29289C6.98043 4.10536 7.23478 4 7.5 4H12.5V9C12.5 9.26522 12.6054 9.51957 12.7929 9.70711C12.9804 9.89464 13.2348 10 13.5 10H18.5V19Z"
                          fill="#8B8B88"
                        />
                      </svg>
                    </div>
                  </div>
                )}
              </div>
              <p
                aria-label={t("components.drag-drop.p3")}
                className="flex relative items-center justify-center whitespace-nowrap text-gray-800 m-1 py-1 px-2 font-outfit text-[1rem] not-italic font-semibold leading-[1rem]"
              >
                {fileObj.file.name}
              </p>
              <Button
                onClick={() => handleRemoveFile(index)}
                className={styleButtom({
                  statesButtom: selectedFiles.length >= 2,
                })}
              >
                <XMarkIcon className="w-4	h-4" />
              </Button>
            </div>
          ))}
          {selectedFiles.length > 3 && (
            <>
              {showMore &&
                selectedFiles.slice(3).map((fileObj, index) => (
                  <div
                    key={index + 3}
                    className={styleList({ states: selectedFiles.length >= 2 })}
                  >
                    <div className="flex overflow-hidden justify-center items-center">
                      {fileObj.file.type.startsWith("image/") ? (
                        <img
                          data-testid="selected-text"
                          src={fileObj.objectURL}
                          alt={fileObj.file.name}
                          className={styleImagem({
                            statesImage: selectedFiles.length >= 2,
                          })}
                        />
                      ) : (
                        <div className="flex flex-row overflow-hidden justify-center items-center">
                          <div
                            className={styleBordaCinzaIcone({
                              statesBordaIcone: selectedFiles.length >= 2,
                            })}
                          >
                            <svg
                              className={styleImagem({
                                statesImage: selectedFiles.length >= 2,
                              })}
                              xmlns="http://www.w3.org/2000/svg"
                              width="2"
                              height="2"
                              viewBox="0 0 25 24"
                              fill="none"
                            >
                              <path
                                d="M20.5 8.94C20.4896 8.84813 20.4695 8.75763 20.44 8.67V8.58C20.3919 8.47718 20.3278 8.38267 20.25 8.3L14.25 2.3C14.1673 2.22222 14.0728 2.15808 13.97 2.11H13.88L13.56 2H7.5C6.70435 2 5.94129 2.31607 5.37868 2.87868C4.81607 3.44129 4.5 4.20435 4.5 5V19C4.5 19.7956 4.81607 20.5587 5.37868 21.1213C5.94129 21.6839 6.70435 22 7.5 22H17.5C18.2956 22 19.0587 21.6839 19.6213 21.1213C20.1839 20.5587 20.5 19.7956 20.5 19V9V8.94ZM14.5 5.41L17.09 8H14.5V5.41ZM18.5 19C18.5 19.2652 18.3946 19.5196 18.2071 19.7071C18.0196 19.8946 17.7652 20 17.5 20H7.5C7.23478 20 6.98043 19.8946 6.79289 19.7071C6.60536 19.5196 6.5 19.2652 6.5 19V5C6.5 4.73478 6.60536 4.48043 6.79289 4.29289C6.98043 4.10536 7.23478 4 7.5 4H12.5V9C12.5 9.26522 12.6054 9.51957 12.7929 9.70711C12.9804 9.89464 13.2348 10 13.5 10H18.5V19Z"
                                fill="#8B8B88"
                              />
                            </svg>
                          </div>
                        </div>
                      )}
                    </div>
                    <p
                      aria-label={t("components.drag-drop.p3")}
                      className="flex relative items-center justify-center whitespace-nowrap text-gray-800 m-1 py-1 px-2 font-outfit text-[1rem] not-italic font-semibold leading-[1rem]"
                    >
                      {fileObj.file.name}
                    </p>
                    <Button
                      onClick={() => handleRemoveFile(index + 3)}
                      className={styleButtom({
                        statesButtom: selectedFiles.length >= 2,
                      })}
                    >
                      <XMarkIcon className="w-4	h-4" />
                    </Button>
                  </div>
                ))}
              <a
                onClick={handleShowMoreFiles}
                className="text-yellow-900 underline font-outfit text-1rem not-italic font-semibold leading-[1rem] cursor-pointer"
              >
                {showMore ? (
                  t("components.drag-drop.less")
                ) : selectedFiles.length > 3 ? (
                  <span>
                    {selectedFiles.length - 3} {t("components.drag-drop.p2")}
                  </span>
                ) : null}
              </a>
            </>
          )}
        </>
      )}
    </div>
  );
};

export default DragAndDrop;
