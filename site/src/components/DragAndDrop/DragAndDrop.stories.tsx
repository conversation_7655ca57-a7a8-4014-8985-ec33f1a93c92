import { <PERSON><PERSON>, <PERSON> } from "@storybook/react";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import DragAndDrop, { DragAndDropProps } from "./DragAndDrop";

export default {
  title: "Components/DragAndDrop",
  component: DragAndDrop,
} as Meta;

const Template: Story<DragAndDropProps> = (args) => <DragAndDrop {...args} />;

export const DragDrop = Template.bind({});
DragDrop.args = {
  onFilesDrop: (files: { file: File; objectURL: string }[]) =>
    console.log("Files dropped:", files),
};
DragDrop.decorators = [
  (Story) => (
    <DndProvider backend={HTML5Backend}>
      <Story />
    </DndProvider>
  ),
];
