import { render, screen, fireEvent } from "@tests/render";
import DragAndDrop, { DragAndDropProps } from "./DragAndDrop";
import "@testing-library/jest-dom";

describe("DragAndDrop Component", () => {
  const mockFilesDrop = jest.fn();

  const defaultProps: DragAndDropProps = {
    onFilesDrop: mockFilesDrop,
  };

  beforeEach(() => {
    render(<DragAndDrop {...defaultProps} />);
    global.URL.createObjectURL = jest.fn();
  });

  it("adds an image file and displays it correctly", async () => {
    const imageFile = new File(["(⌐□_□)"], "test_image.png", {
      type: "image/png",
    });

    const fileInput = screen.getByTestId("input-dragdrop");
    fireEvent.change(fileInput, { target: { files: [imageFile] } });

    expect(await screen.findByAltText("test_image.png")).toBeInTheDocument();
  });

  it("adds a PDF file and displays it correctly", async () => {
    const pdfFile = new File(["PDF Content"], "test_document.pdf", {
      type: "application/pdf",
    });

    const fileInput = screen.getByTestId("input-dragdrop");
    fireEvent.change(fileInput, { target: { files: [pdfFile] } });

    expect(await screen.findByText("test_document.pdf")).toBeInTheDocument();
  });

  it("show multiple files label correctly", async () => {
    const pdfFile = new File(["PDF Content"], "test_document.pdf", {
      type: "application/pdf",
    });

    const fileInput = screen.getByTestId("input-dragdrop");
    fireEvent.change(fileInput, {
      target: { files: [pdfFile, pdfFile, pdfFile, pdfFile, pdfFile] },
    });

    expect(
      await screen.findByText("2 components.drag-drop.p2"),
    ).toBeInTheDocument();
  });
});
