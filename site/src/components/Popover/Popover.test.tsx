import { render, screen } from "@tests/render";
import { Popover } from ".";

describe("<Popover />", () => {
  it("should render the Popover", () => {
    render(
      <Popover content={<div data-testid="popover-content"></div>}></Popover>,
    );
    expect(screen.getByTestId("popover")).toMatchSnapshot();
  });

  it("should render the on click Popover", () => {
    render(
      <Popover
        trigger="click"
        content={<div data-testid="popover-content"></div>}
      ></Popover>,
    );
    expect(screen.getByTestId("popover")).toMatchSnapshot();
  });

  it("should render the left Popover", () => {
    render(
      <Popover
        placement="left"
        content={<div data-testid="popover-content"></div>}
      ></Popover>,
    );
    expect(screen.getByTestId("popover")).toMatchSnapshot();
  });

  it("should render the top-end Popover", () => {
    render(
      <Popover
        placement="top-end"
        content={<div data-testid="popover-content"></div>}
      ></Popover>,
    );
    expect(screen.getByTestId("popover")).toMatchSnapshot();
  });

  it("should render the top Popover", () => {
    render(
      <Popover
        placement="top"
        content={<div data-testid="popover-content"></div>}
      ></Popover>,
    );
    expect(screen.getByTestId("popover")).toMatchSnapshot();
  });

  it("should render the top-start Popover", () => {
    render(
      <Popover
        placement="top-start"
        content={<div data-testid="popover-content"></div>}
      ></Popover>,
    );
    expect(screen.getByTestId("popover")).toMatchSnapshot();
  });

  it("should render the right Popover", () => {
    render(
      <Popover
        placement="right"
        content={<div data-testid="popover-content"></div>}
      ></Popover>,
    );
    expect(screen.getByTestId("popover")).toMatchSnapshot();
  });

  it("should render the bottom-start Popover", () => {
    render(
      <Popover
        placement="bottom-start"
        content={<div data-testid="popover-content"></div>}
      ></Popover>,
    );
    expect(screen.getByTestId("popover")).toMatchSnapshot();
  });

  it("should render the bottom Popover", () => {
    render(
      <Popover
        placement="bottom"
        content={<div data-testid="popover-content"></div>}
      ></Popover>,
    );
    expect(screen.getByTestId("popover")).toMatchSnapshot();
  });

  it("should render the bottom-end Popover", () => {
    render(
      <Popover
        placement="bottom-end"
        content={<div data-testid="popover-content"></div>}
      ></Popover>,
    );
    expect(screen.getByTestId("popover")).toMatchSnapshot();
  });
});
