// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Popover /> should render the Popover 1`] = `
<div
  class="absolute inline-block z-10 rounded-xl text-xs p-3 transition-opacity duration-300 invisible opacity-0 border border-gray-700 bg-white"
  data-testid="popover"
  id=":r0:"
  role="tooltip"
  style="position: absolute; top: 0px; left: 0px;"
  tabindex="-1"
>
  <div
    class="relative z-20"
  >
    <div
      data-testid="popover-content"
    />
  </div>
  <div
    class="absolute z-10 h-2 w-2 rotate-45 bg-white border-l border-b border-gray-700"
    data-testid="flowbite-tooltip-arrow"
    style="left: -5px;"
  >
     
  </div>
</div>
`;

exports[`<Popover /> should render the bottom Popover 1`] = `
<div
  class="absolute inline-block z-10 rounded-xl text-xs p-3 transition-opacity duration-300 invisible opacity-0 border border-gray-700 bg-white"
  data-testid="popover"
  id=":rg:"
  role="tooltip"
  style="position: absolute; top: 0px; left: 0px;"
  tabindex="-1"
>
  <div
    class="relative z-20"
  >
    <div
      data-testid="popover-content"
    />
  </div>
  <div
    class="absolute z-10 h-2 w-2 rotate-[135deg] inline bg-white border-l border-b border-gray-700"
    data-testid="flowbite-tooltip-arrow"
    style="top: -5px;"
  >
     
  </div>
</div>
`;

exports[`<Popover /> should render the bottom-end Popover 1`] = `
<div
  class="absolute inline-block z-10 rounded-xl text-xs p-3 transition-opacity duration-300 invisible opacity-0 border border-gray-700 bg-white"
  data-testid="popover"
  id=":ri:"
  role="tooltip"
  style="position: absolute; top: 0px; left: 0px;"
  tabindex="-1"
>
  <div
    class="relative z-20"
  >
    <div
      data-testid="popover-content"
    />
  </div>
  <div
    class="absolute z-10 h-2 w-2 rotate-[135deg] inline bg-white border-l border-b border-gray-700"
    data-testid="flowbite-tooltip-arrow"
    style="top: -5px;"
  >
     
  </div>
</div>
`;

exports[`<Popover /> should render the bottom-start Popover 1`] = `
<div
  class="absolute inline-block z-10 rounded-xl text-xs p-3 transition-opacity duration-300 invisible opacity-0 border border-gray-700 bg-white"
  data-testid="popover"
  id=":re:"
  role="tooltip"
  style="position: absolute; top: 0px; left: 0px;"
  tabindex="-1"
>
  <div
    class="relative z-20"
  >
    <div
      data-testid="popover-content"
    />
  </div>
  <div
    class="absolute z-10 h-2 w-2 rotate-[135deg] inline bg-white border-l border-b border-gray-700"
    data-testid="flowbite-tooltip-arrow"
    style="top: -5px;"
  >
     
  </div>
</div>
`;

exports[`<Popover /> should render the left Popover 1`] = `
<div
  class="absolute inline-block z-10 rounded-xl text-xs p-3 transition-opacity duration-300 invisible opacity-0 border border-gray-700 bg-white"
  data-testid="popover"
  id=":r4:"
  role="tooltip"
  style="position: absolute; top: 0px; left: 0px;"
  tabindex="-1"
>
  <div
    class="relative z-20"
  >
    <div
      data-testid="popover-content"
    />
  </div>
  <div
    class="absolute z-10 h-2 w-2 rotate-[225deg] inline bg-white border-l border-b border-gray-700"
    data-testid="flowbite-tooltip-arrow"
    style="right: -5px;"
  >
     
  </div>
</div>
`;

exports[`<Popover /> should render the on click Popover 1`] = `
<div
  class="absolute inline-block z-10 rounded-xl text-xs p-3 transition-opacity duration-300 invisible opacity-0 border border-gray-700 bg-white"
  data-testid="popover"
  id=":r2:"
  role="tooltip"
  style="position: absolute; top: 0px; left: 0px;"
  tabindex="-1"
>
  <div
    class="relative z-20"
  >
    <div
      data-testid="popover-content"
    />
  </div>
  <div
    class="absolute z-10 h-2 w-2 rotate-45 bg-white border-l border-b border-gray-700"
    data-testid="flowbite-tooltip-arrow"
    style="left: -5px;"
  >
     
  </div>
</div>
`;

exports[`<Popover /> should render the right Popover 1`] = `
<div
  class="absolute inline-block z-10 rounded-xl text-xs p-3 transition-opacity duration-300 invisible opacity-0 border border-gray-700 bg-white"
  data-testid="popover"
  id=":rc:"
  role="tooltip"
  style="position: absolute; top: 0px; left: 0px;"
  tabindex="-1"
>
  <div
    class="relative z-20"
  >
    <div
      data-testid="popover-content"
    />
  </div>
  <div
    class="absolute z-10 h-2 w-2 rotate-45 bg-white border-l border-b border-gray-700"
    data-testid="flowbite-tooltip-arrow"
    style="left: -5px;"
  >
     
  </div>
</div>
`;

exports[`<Popover /> should render the top Popover 1`] = `
<div
  class="absolute inline-block z-10 rounded-xl text-xs p-3 transition-opacity duration-300 invisible opacity-0 border border-gray-700 bg-white"
  data-testid="popover"
  id=":r8:"
  role="tooltip"
  style="position: absolute; top: 0px; left: 0px;"
  tabindex="-1"
>
  <div
    class="relative z-20"
  >
    <div
      data-testid="popover-content"
    />
  </div>
  <div
    class="absolute z-10 h-2 w-2 -rotate-45 inline bg-white border-l border-b border-gray-700"
    data-testid="flowbite-tooltip-arrow"
    style="bottom: -5px;"
  >
     
  </div>
</div>
`;

exports[`<Popover /> should render the top-end Popover 1`] = `
<div
  class="absolute inline-block z-10 rounded-xl text-xs p-3 transition-opacity duration-300 invisible opacity-0 border border-gray-700 bg-white"
  data-testid="popover"
  id=":r6:"
  role="tooltip"
  style="position: absolute; top: 0px; left: 0px;"
  tabindex="-1"
>
  <div
    class="relative z-20"
  >
    <div
      data-testid="popover-content"
    />
  </div>
  <div
    class="absolute z-10 h-2 w-2 -rotate-45 inline bg-white border-l border-b border-gray-700"
    data-testid="flowbite-tooltip-arrow"
    style="bottom: -5px;"
  >
     
  </div>
</div>
`;

exports[`<Popover /> should render the top-start Popover 1`] = `
<div
  class="absolute inline-block z-10 rounded-xl text-xs p-3 transition-opacity duration-300 invisible opacity-0 border border-gray-700 bg-white"
  data-testid="popover"
  id=":ra:"
  role="tooltip"
  style="position: absolute; top: 0px; left: 0px;"
  tabindex="-1"
>
  <div
    class="relative z-20"
  >
    <div
      data-testid="popover-content"
    />
  </div>
  <div
    class="absolute z-10 h-2 w-2 -rotate-45 inline bg-white border-l border-b border-gray-700"
    data-testid="flowbite-tooltip-arrow"
    style="bottom: -5px;"
  >
     
  </div>
</div>
`;
