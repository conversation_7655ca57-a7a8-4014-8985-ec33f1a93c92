import { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { Popover, PopoverProps } from "./Popover";
import { Button, Heading, Text } from "..";

const story: Meta<PopoverProps> = {
  component: Popover,
  title: "Popover",
  render: ({ children = "Popover", content, ...args }) => (
    <div className="flex items-center justify-center h-[100vh] w-fit mx-auto">
      <Popover content={content} {...args}>
        {children}
      </Popover>
    </div>
  ),
} as Meta;

export default story;

type Story = StoryObj<PopoverProps>;

export const Default: Story = {
  args: {
    content: (
      <Text className="text-left">
        Some Text here <br />
        And Here
      </Text>
    ),
    children: <Heading size="4">Default</Heading>,
  },
};

export const OnClick: Story = {
  args: {
    trigger: "click",
    content: <Text>OnClick PopOver</Text>,
    children: <Button>OnClick</Button>,
  },
};

export const Left: Story = {
  args: {
    placement: "left",
    content: <Text>Left PopOver</Text>,
    children: <Heading size="4">Left</Heading>,
  },
};

export const TopEnd: Story = {
  args: {
    placement: "top-end",
    content: <Text>Top-End PopOver</Text>,
    children: <Heading size="4">Top-End</Heading>,
  },
};

export const TopCenter: Story = {
  args: {
    placement: "top",
    content: <Text>Top-Center PopOver</Text>,
    children: <Heading size="4">Top-Center</Heading>,
  },
};

export const TopStart: Story = {
  args: {
    placement: "top-start",
    content: <Text>Top-Start PopOver</Text>,
    children: <Heading size="4">Top-Start</Heading>,
  },
};

export const Right: Story = {
  args: {
    placement: "right",
    content: <Text>Right PopOver</Text>,
    children: <Heading size="4">Right</Heading>,
  },
};

export const BottomStart: Story = {
  args: {
    placement: "bottom-start",
    content: <Text>Bottom-Start PopOver</Text>,
    children: <Heading size="4">Bottom-Start</Heading>,
  },
};

export const BottomCenter: Story = {
  args: {
    placement: "bottom",
    content: <Text>Bottom-Center PopOver</Text>,
    children: <Heading size="4">Bottom-Center</Heading>,
  },
};

export const BottomEnd: Story = {
  args: {
    placement: "bottom-end",
    content: <Text>Bottom-End PopOver</Text>,
    children: <Heading size="4">Bottom-End</Heading>,
  },
};
