import { Tooltip, TooltipProps } from "flowbite-react";

export interface PopoverProps extends TooltipProps {}

const defaultStyles = {
  target: "w-full",
  animation: "transition-opacity",
  base: "absolute inline-block z-10 rounded-xl text-xs p-3",
  hidden: "invisible opacity-0",
  style: {
    dark: "border border-gray-700 bg-white",
    light: "border border-gray-700 bg-white",
    auto: "border border-gray-700 bg-white",
  },
  content: "relative z-20",
};

const defaultArrow = {
  base: "absolute z-10 h-2 w-2 rotate-45",
  style: {
    dark: "bg-white border-l border-b border-gray-700",
    light: "bg-white border-l border-b border-gray-700",
    auto: "bg-white border-l border-b border-gray-700",
  },
  placement: "-5px",
};

const bottomArrow = {
  base: "absolute z-10 h-2 w-2 -rotate-45 inline",
  style: {
    dark: "bg-white border-l border-b border-gray-700",
    light: "bg-white border-l border-b border-gray-700",
    auto: "bg-white border-l border-b border-gray-700",
  },
  placement: "-5px",
};

const topArrow = {
  base: "absolute z-10 h-2 w-2 rotate-[135deg] inline",
  style: {
    dark: "bg-white border-l border-b border-gray-700",
    light: "bg-white border-l border-b border-gray-700",
    auto: "bg-white border-l border-b border-gray-700",
  },
  placement: "-5px",
};

const rightArrow = {
  base: "absolute z-10 h-2 w-2 rotate-[225deg] inline",
  style: {
    dark: "bg-white border-l border-b border-gray-700",
    light: "bg-white border-l border-b border-gray-700",
    auto: "bg-white border-l border-b border-gray-700",
  },
  placement: "-5px",
};

export function Popover({
  children,
  content,
  placement = "right",
  trigger = "hover",
}: PopoverProps) {
  function arrowStyles(): TooltipProps["theme"] {
    const theme: TooltipProps["theme"] = {
      target: defaultStyles.target,
      animation: defaultStyles.animation,
      base: defaultStyles.base,
      hidden: defaultStyles.hidden,
      style: defaultStyles.style,
      content: defaultStyles.content,
    };
    switch (placement) {
      case "top":
      case "top-end":
      case "top-start":
        theme.arrow = bottomArrow;
        return theme;
      case "bottom":
      case "bottom-end":
      case "bottom-start":
        theme.arrow = topArrow;
        return theme;
      case "left":
        theme.arrow = rightArrow;
        return theme;
      case "right":
      default:
        theme.arrow = defaultArrow;
        return theme;
    }
  }

  return (
    <Tooltip
      theme={arrowStyles()}
      content={content}
      placement={placement}
      trigger={trigger}
      data-testid="popover"
    >
      {children}
    </Tooltip>
  );
}

export default Popover;
