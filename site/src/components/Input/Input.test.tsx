import { render, screen } from "@tests/render";
import Input from "./Input";

describe("<Input />", () => {
  it("should render the input with default state", () => {
    render(<Input />);
    expect(screen.getByRole("textbox")).toMatchSnapshot();
  });

  it("should render the input with rounded style", () => {
    render(<Input />);
    expect(screen.getByRole("textbox")).toMatchSnapshot();
  });

  it("should render the input with success state", () => {
    render(<Input state="success" />);
    expect(screen.getByRole("textbox")).toMatchSnapshot();
  });

  it("should render the input with error state", () => {
    render(<Input state="error" />);
    expect(screen.getByRole("textbox")).toMatchSnapshot();
  });

  it("should render the input with a custom class", () => {
    render(<Input className="custom-class" />);
    expect(screen.getByRole("textbox")).toMatchSnapshot();
  });

  it("should render the password type input", () => {
    render(<Input type="password" />);
    expect(screen.getByTestId("input")).toMatchSnapshot();
  });
});
