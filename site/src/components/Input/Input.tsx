import { EyeIcon, EyeSlashIcon } from "@heroicons/react/24/outline";
import { InputHTMLAttributes, forwardRef, useState } from "react";
import { tv } from "tailwind-variants";

export interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  state?: "default" | "success" | "warning" | "error";
  type?: "password" | undefined;
}

const style = tv({
  base: "py-[0.9375rem] px-[0.8125rem] text-xs border rounded-lg bg-white w-full h-10 disabled:border-gray-200 focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none",
  variants: {
    state: {
      default: "border-gray-700",
      success: "border-green-400",
      warning: "border-yellow-600",
      error: "border-red-600",
    },
    rounded: {
      true: "rounded-full",
      false: "",
    },
  },
  compoundVariants: [
    {
      state: ["default", "success", "warning", "error"],
    },
  ],
  defaultVariants: {
    state: "default",
  },
});

export const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ className, state = "default", type = undefined, ...rest }, ref) => {
    const [show, setShow] = useState(false);

    function handleOnClick() {
      setShow(!show);
    }

    return (
      <div
        className="relative flex flex-row items-center w-full"
        data-testid="input"
      >
        <input
          type={show ? "text" : type}
          className={style({ state, className })}
          ref={ref}
          {...rest}
        />
        {type === "password" &&
          (show ? (
            <EyeIcon
              className="h-10 cursor-pointer p-[0.65rem] stroke-[0.13rem] absolute right-0"
              onClick={handleOnClick}
            />
          ) : (
            <EyeSlashIcon
              className="h-10 cursor-pointer p-[0.65rem] stroke-[0.13rem] absolute right-0"
              onClick={handleOnClick}
            />
          ))}
      </div>
    );
  },
);

export default Input;
