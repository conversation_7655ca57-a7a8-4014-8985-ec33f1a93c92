// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Input /> should render the input with a custom class 1`] = `
<input
  class="py-[0.9375rem] px-[0.8125rem] text-xs border rounded-lg bg-white w-full h-10 disabled:border-gray-200 focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none border-gray-700 custom-class"
/>
`;

exports[`<Input /> should render the input with default state 1`] = `
<input
  class="py-[0.9375rem] px-[0.8125rem] text-xs border rounded-lg bg-white w-full h-10 disabled:border-gray-200 focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none border-gray-700"
/>
`;

exports[`<Input /> should render the input with error state 1`] = `
<input
  class="py-[0.9375rem] px-[0.8125rem] text-xs border rounded-lg bg-white w-full h-10 disabled:border-gray-200 focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none border-red-600"
/>
`;

exports[`<Input /> should render the input with rounded style 1`] = `
<input
  class="py-[0.9375rem] px-[0.8125rem] text-xs border rounded-lg bg-white w-full h-10 disabled:border-gray-200 focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none border-gray-700"
/>
`;

exports[`<Input /> should render the input with success state 1`] = `
<input
  class="py-[0.9375rem] px-[0.8125rem] text-xs border rounded-lg bg-white w-full h-10 disabled:border-gray-200 focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none border-green-400"
/>
`;

exports[`<Input /> should render the password type input 1`] = `
<div
  class="relative flex flex-row items-center w-full"
  data-testid="input"
>
  <input
    class="py-[0.9375rem] px-[0.8125rem] text-xs border rounded-lg bg-white w-full h-10 disabled:border-gray-200 focus:border-black-900 focus:shadow-[0_0_4px_0_rgba(0,0,0,0.3)] focus:outline-none border-gray-700"
    type="password"
  />
  <svg
    aria-hidden="true"
    class="h-10 cursor-pointer p-[0.65rem] stroke-[0.13rem] absolute right-0"
    data-slot="icon"
    fill="none"
    stroke="currentColor"
    stroke-width="1.5"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
</div>
`;
