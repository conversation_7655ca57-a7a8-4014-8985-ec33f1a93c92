import { render, fireEvent, screen } from "@tests/render";
import { LoggedOutSidebar } from "./LoggedOutSidebar";
import "@testing-library/jest-dom";

describe("Sidebar component", () => {
  it('clicks on "Entrar" button when not authenticated', async () => {
    render(<LoggedOutSidebar />);
    fireEvent.click(screen.getByTestId("Entrar"));
  });

  it('renders "Criar Conta" button', async () => {
    render(<LoggedOutSidebar />);
    const criarContaButton = await screen.findByTestId("Criar Conta");
    expect(criarContaButton).toBeInTheDocument();
  });

  it("renders logo image", async () => {
    render(<LoggedOutSidebar />);
    const logoImage = screen.getByAltText("Logo");
    expect(logoImage).toBeInTheDocument();
  });
});
