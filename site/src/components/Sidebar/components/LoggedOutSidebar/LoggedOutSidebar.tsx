import { HTMLAttributes } from "react";
import { Link } from "react-router-dom";
import { Image } from "@/components";
import logo from "@/assets/logo.svg";
import { useTranslation } from "react-i18next";

export interface LoggedOutSidebarProps extends HTMLAttributes<HTMLDivElement> {}

export const LoggedOutSidebar: React.FC<LoggedOutSidebarProps> = ({
  ...rest
}: LoggedOutSidebarProps) => {
  const { t } = useTranslation();
  return (
    <nav
      className="flex flex-col flex-auto items-start gap-4 p-6 h-full w-64 flex-shrink-0 min-h-screen antialiased bg-[#FFFFFF] border-r border-gray-100"
      {...rest}
    >
      <div className="flex flex-col gap-4 justify-center items-center">
        <Link to="/" className="h-7 mb-2">
          <Image
            src={logo}
            alt="Logo"
            className="hidden lg:block h-full w-auto"
          />
        </Link>
      </div>

      <nav className="flex flex-col w-full gap-4 justify-center items-center">
        <Link
          to="/"
          data-testid="Criar Conta"
          className="w-full py-3 px-4 rounded-md font-outfit font-medium capitalize text-xs text-center text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white"
        >
          {t("components.Sidebar.title")}
        </Link>
        <Link
          to="/"
          data-testid="Entrar"
          className="w-full py-3 px-4 rounded-md font-outfit font-medium capitalize text-xs text-center text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed"
        >
          {t("components.Sidebar.login")}
        </Link>
      </nav>
    </nav>
  );
};

export default LoggedOutSidebar;
