import React from "react";
import { <PERSON> } from "react-router-dom";
import { HTMLAttributes } from "react";
import { Popover, Button, Image } from "@/components";
import logoMob from "@/assets/logo-mob.svg";
import {
  BuildingStorefrontIcon,
  CalendarDaysIcon,
  Cog6ToothIcon,
  CreditCardIcon,
  HomeIcon,
  LockClosedIcon,
  MegaphoneIcon,
  PlusIcon,
  PresentationChartBarIcon,
  UserIcon,
  UsersIcon,
} from "@heroicons/react/24/outline";
import { useTranslation } from "react-i18next";

export interface CollapsedSidebarProps extends HTMLAttributes<HTMLDivElement> {}

const CollapsedSidebar: React.FC<CollapsedSidebarProps> = ({
  ...rest
}: CollapsedSidebarProps) => {
  const { t } = useTranslation();

  return (
    <nav
      className="flex flex-col bg-white flex-auto items-center gap-4 p-1 h-full w-14 flex-shrink-0 min-h-screen antialiased border-r border-gray-100"
      data-testid="collapsed-sidebar"
      {...rest}
    >
      <div className="flex flex-col gap-4 justify-center items-center">
        <Link to="/" className="h-7 mb-2">
          <Image src={logoMob} />
        </Link>
      </div>

      <span className="w-full border-b border-gray-100" />

      <div className="group relative justify-center">
        <Popover
          placement="right"
          content={
            <div className="flex mx-auto">{t("components.Sidebar.text")}</div>
          }
        >
          <Button
            color="primary"
            size="normal"
            className="flex justify-center items-center w-11 h-11"
          >
            <PlusIcon className="w-8 h-5" />
          </Button>
        </Popover>
      </div>

      <span className="w-full border-b border-gray-100" />

      <div className="w-full">
        <nav className="flex flex-col gap-2 ">
          {[
            { Icon: HomeIcon, label: t("components.Sidebar.labels.home") },
            {
              Icon: MegaphoneIcon,
              label: t("components.Sidebar.labels.megaphone"),
            },
            {
              Icon: CalendarDaysIcon,
              label: t("components.Sidebar.labels.calendarDays"),
            },
            {
              Icon: BuildingStorefrontIcon,
              label: t("components.Sidebar.labels.buildingStorefront"),
            },
            {
              Icon: UsersIcon,
              label: t("components.Sidebar.labels.users"),
            },
            {
              Icon: PresentationChartBarIcon,
              label: t("components.Sidebar.labels.presentationChartBar"),
            },
          ].map(({ Icon, label }, index) => (
            <Popover
              placement="right"
              content={<div className="flex mx-auto">{label}</div>}
            >
              <Link
                to="/"
                className="flex w-full h-8 items-center justify-center  text-gray-800 font-outfit font-semibold	 hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                onClick={(e) => {
                  e.stopPropagation();
                }}
                key={index}
              >
                <Icon className="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5" />
              </Link>
            </Popover>
          ))}

          <span className="w-full border-b border-gray-100" />

          {[
            {
              Icon: UserIcon,
              label: t("components.Sidebar.labels.user"),
            },
            {
              Icon: LockClosedIcon,
              label: t("components.Sidebar.labels.lockClose"),
            },
            {
              Icon: CreditCardIcon,
              label: t("components.Sidebar.labels.creditCar"),
            },
            {
              Icon: Cog6ToothIcon,
              label: t("components.Sidebar.labels.cog6Tooth"),
            },
          ].map(({ Icon, label }, index) => (
            <Popover
              placement="right"
              content={<div className="flex mx-auto">{label}</div>}
            >
              <Link
                to="/"
                className="flex w-full h-8 items-center justify-center  text-gray-800 font-outfit font-semibold	 hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                onClick={(e) => {
                  e.stopPropagation();
                }}
                key={index}
              >
                <Icon className="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5" />
              </Link>
            </Popover>
          ))}
        </nav>
      </div>
    </nav>
  );
};

export default CollapsedSidebar;
