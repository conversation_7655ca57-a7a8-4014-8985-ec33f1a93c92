import React from "react";
import { <PERSON> } from "react-router-dom";
import { HTMLAttributes } from "react";
import { <PERSON><PERSON>, Image } from "@/components";
import logo from "@/assets/logo.svg";
import {
  BuildingStorefrontIcon,
  CalendarDaysIcon,
  Cog6ToothIcon,
  CreditCardIcon,
  HomeIcon,
  LockClosedIcon,
  MegaphoneIcon,
  PlusIcon,
  PresentationChartBarIcon,
  UserIcon,
  UsersIcon,
} from "@heroicons/react/24/outline";
import { useTranslation } from "react-i18next";

export interface OpenedSidebarProps extends HTMLAttributes<HTMLDivElement> {}

export const OpenedSidebar: React.FC<OpenedSidebarProps> = ({
  ...rest
}: OpenedSidebarProps) => {
  const { t } = useTranslation();

  return (
    <nav
      className="flex flex-col flex-auto items-start gap-4 p-6 h-full w-64 flex-shrink-0 min-h-screen antialiased bg-white border-r border-gray-100"
      {...rest}
      data-testid="opened-sidebar"
    >
      <div className="flex flex-col gap-4 justify-center items-center">
        <Link to="/" className="h-7 mb-2">
          <Image src={logo} alt="Logo" className="block h-full w-auto" />
        </Link>
        <div className="block lg:hidden flex w-full gap-2 items-center">
          <img
            src="https://tecdn.b-cdn.net/img/new/avatars/2.jpg"
            className="rounded-full w-8 h-8"
            loading="lazy"
            style={{ maxWidth: "none" }}
          />
          <p className="font-outfit  text-xs capitalize text-black-900">
            Bruno Sousa
          </p>
        </div>
      </div>

      <span className="w-full border-b border-gray-100" />

      <Button
        color="primary"
        size="normal"
        className="flex justify-center items-center"
      >
        {t("components.Sidebar.text")}
        <PlusIcon className="w-8 h-5" />
      </Button>

      <span className="w-full border-b border-gray-100" />

      <div className="w-full">
        <nav className="flex flex-col gap-2 ">
          {[
            { Icon: HomeIcon, label: t("components.Sidebar.labels.home") },
            {
              Icon: MegaphoneIcon,
              label: t("components.Sidebar.labels.megaphone"),
            },
            {
              Icon: CalendarDaysIcon,
              label: t("components.Sidebar.labels.calendarDays"),
            },
            {
              Icon: BuildingStorefrontIcon,
              label: t("components.Sidebar.labels.buildingStorefront"),
            },
            {
              Icon: UsersIcon,
              label: t("components.Sidebar.labels.users"),
            },
            {
              Icon: PresentationChartBarIcon,
              label: t("components.Sidebar.labels.presentationChartBar"),
            },
          ].map(({ Icon, label }, index) => (
            <>
              <Link
                to="/"
                className="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold	 hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300 "
                onClick={(e) => {
                  e.stopPropagation();
                }}
                key={index}
              >
                <Icon className="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5" />
                <span className=" text-xs tracking-wide truncate text-black-900">
                  {label}
                </span>
              </Link>
            </>
          ))}
          <span className="flex items-center justify-center w-[14.5rem] h-[0.0625rem] border-b border-b-neutral-50" />
          {[
            {
              Icon: UserIcon,
              label: t("components.Sidebar.labels.user"),
            },
            {
              Icon: LockClosedIcon,
              label: t("components.Sidebar.labels.lockClose"),
            },
            {
              Icon: CreditCardIcon,
              label: t("components.Sidebar.labels.creditCar"),
            },
            {
              Icon: Cog6ToothIcon,
              label: t("components.Sidebar.labels.cog6Tooth"),
            },
          ].map(({ Icon, label }, index) => (
            <>
              <Link
                to="/"
                className="flex flex-row w-full items-center gap-2 h-8 px-2 text-gray-800 font-outfit font-semibold	 hover:bg-[#FFF6DB] hover:text-black-900 border-transparent rounded transition ease-in-out delay-150 duration-300"
                onClick={(e) => {
                  e.stopPropagation();
                }}
                key={index}
              >
                <Icon className="text-inherit inline-flex mb-0 justify-center items-center w-5 h-5 " />
                <span className=" text-xs tracking-wide truncate text-black-900">
                  {label}
                </span>
              </Link>
            </>
          ))}
        </nav>
      </div>
    </nav>
  );
};

export default OpenedSidebar;
