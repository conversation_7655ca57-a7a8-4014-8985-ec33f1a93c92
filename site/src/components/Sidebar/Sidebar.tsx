import React, { useState } from "react";
import { HTMLAttributes } from "react";
import { CollapsedSidebar, OpenedSidebar } from "./components";
import { ArrowLeftIcon, ArrowRightIcon } from "@heroicons/react/24/outline";

export interface SidebarProps extends HTMLAttributes<HTMLDivElement> {}

const Sidebar: React.FC<SidebarProps> = () => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleSidebar = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="relative" data-testid="sidebar">
      {!isOpen ? (
        <>
          <OpenedSidebar />
          <button
            className="absolute bottom-4 right-4 flex justify-end w-full"
            onClick={toggleSidebar}
            data-testid="left-arrow"
          >
            <ArrowLeftIcon className="w-4 h-4 transition ease-in-out delay-150 duration-300 hover:-translate-x-1 hover:text-black-900 text-yellow-900 cursor-pointer" />
          </button>
        </>
      ) : (
        <>
          <CollapsedSidebar />
          <button
            className="absolute bottom-4 right-4 flex justify-end w-full"
            onClick={toggleSidebar}
            data-testid="right-arrow"
          >
            <ArrowRightIcon className="w-4 h-4 transition ease-in-out delay-150 duration-300 hover:-translate-x-1 hover:text-black-900 text-yellow-900 cursor-pointer" />
          </button>
        </>
      )}
    </div>
  );
};

export default Sidebar;
