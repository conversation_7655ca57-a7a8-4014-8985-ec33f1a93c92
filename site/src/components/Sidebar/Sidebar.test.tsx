import { render } from "@tests/render";
import "@testing-library/jest-dom";
import Sidebar from "./Sidebar";

describe("Sidebar Component", () => {
  it("should toggle sidebar visibility on arrowRight click", () => {
    const { getByTestId } = render(<Sidebar />);

    const sidebarComponent = getByTestId("sidebar");
    const sidebarVisible = sidebarComponent.classList.contains("visible");

    expect(sidebarVisible).toBe(false);
  });

  it("should toggle sidebar visibility on arrowLeft click", () => {
    const { getByTestId } = render(<Sidebar />);

    const sidebarComponent = getByTestId("sidebar");
    const sidebarVisible = sidebarComponent.classList.contains("visible");

    expect(sidebarVisible).toBe(false);
  });
});
