// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Accordion /> should render the Accordion collapsed, and when click in the button render content 1`] = `
<div
  class="last:rounded-b-lg dark:bg-gray-900 first:rounded-t-lg p-0 border-none font-rubik overflow-hidden"
  data-testid="flowbite-accordion-content"
  hidden=""
>
  <div
    class="[&:not([hidden])]:animate-slideDown origin-top-left transition-all"
  >
    Content
  </div>
</div>
`;

exports[`<Accordion /> should render the Accordion component 1`] = `
<div
  class="divide-y border-gray-200 dark:divide-gray-700 dark:border-gray-700 rounded-none border-0 bg-transparent divide-black-900 origin-top-left transition-all"
  data-testid="flowbite-accordion"
>
  <button
    class="flex w-full items-center justify-between py-[0.88rem] text-left text-2xl font-semibold font-outfit focus:ring-gray-200 dark:hover:bg-gray-800 dark:focus:ring-gray-800 text-gray-900 dark:bg-gray-800 dark:text-white border-0 border-t-[0.0625rem] border-black-900 first:rounded-none first:text-black-900 last:rounded-none hover:bg-transparent bg-transparent focus:ring-0 origin-top-left transition-all"
    type="button"
  >
    <h2
      class=""
      data-testid="flowbite-accordion-heading"
    >
      Title
    </h2>
    <svg
      aria-hidden="true"
      class="h-10 w-10 shrink-0 rotate-180"
      data-testid="flowbite-accordion-arrow"
      fill="currentColor"
      height="1em"
      stroke="currentColor"
      stroke-width="0"
      viewBox="0 0 20 20"
      width="1em"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        clip-rule="evenodd"
        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
        fill-rule="evenodd"
      />
    </svg>
  </button>
  <div
    class="last:rounded-b-lg dark:bg-gray-900 first:rounded-t-lg p-0 border-none font-rubik overflow-hidden"
    data-testid="flowbite-accordion-content"
  >
    <div
      class="[&:not([hidden])]:animate-slideDown origin-top-left transition-all"
    >
      Content
    </div>
  </div>
</div>
`;

exports[`<Accordion /> should render the Accordion with 3 panels 1`] = `
<div
  class="divide-y border-gray-200 dark:divide-gray-700 dark:border-gray-700 rounded-none border-0 bg-transparent divide-black-900 origin-top-left transition-all"
  data-testid="flowbite-accordion"
>
  <button
    class="flex w-full items-center justify-between py-[0.88rem] text-left text-2xl font-semibold font-outfit focus:ring-gray-200 dark:hover:bg-gray-800 dark:focus:ring-gray-800 text-gray-900 dark:bg-gray-800 dark:text-white border-0 border-t-[0.0625rem] border-black-900 first:rounded-none first:text-black-900 last:rounded-none hover:bg-transparent bg-transparent focus:ring-0 origin-top-left transition-all"
    type="button"
  >
    <h2
      class=""
      data-testid="flowbite-accordion-heading"
    >
      Title 1
    </h2>
    <svg
      aria-hidden="true"
      class="h-10 w-10 shrink-0 rotate-180"
      data-testid="flowbite-accordion-arrow"
      fill="currentColor"
      height="1em"
      stroke="currentColor"
      stroke-width="0"
      viewBox="0 0 20 20"
      width="1em"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        clip-rule="evenodd"
        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
        fill-rule="evenodd"
      />
    </svg>
  </button>
  <div
    class="last:rounded-b-lg dark:bg-gray-900 first:rounded-t-lg p-0 border-none font-rubik overflow-hidden"
    data-testid="flowbite-accordion-content"
  >
    <div
      class="[&:not([hidden])]:animate-slideDown origin-top-left transition-all"
    >
      Content 1
    </div>
  </div>
  <button
    class="flex w-full items-center justify-between py-[0.88rem] text-left text-2xl font-semibold font-outfit focus:ring-gray-200 dark:hover:bg-gray-800 dark:focus:ring-gray-800 border-0 border-t-[0.0625rem] border-black-900 first:rounded-none first:text-black-900 last:rounded-none hover:bg-transparent bg-transparent focus:ring-0 origin-top-left transition-all"
    type="button"
  >
    <h2
      class=""
      data-testid="flowbite-accordion-heading"
    >
      Title 2
    </h2>
    <svg
      aria-hidden="true"
      class="h-10 w-10 shrink-0"
      data-testid="flowbite-accordion-arrow"
      fill="currentColor"
      height="1em"
      stroke="currentColor"
      stroke-width="0"
      viewBox="0 0 20 20"
      width="1em"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        clip-rule="evenodd"
        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
        fill-rule="evenodd"
      />
    </svg>
  </button>
  <div
    class="last:rounded-b-lg dark:bg-gray-900 first:rounded-t-lg p-0 border-none font-rubik overflow-hidden"
    data-testid="flowbite-accordion-content"
    hidden=""
  >
    <div
      class="[&:not([hidden])]:animate-slideDown origin-top-left transition-all"
    >
      Content 2
    </div>
  </div>
  <button
    class="flex w-full items-center justify-between py-[0.88rem] text-left text-2xl font-semibold font-outfit focus:ring-gray-200 dark:hover:bg-gray-800 dark:focus:ring-gray-800 border-0 border-t-[0.0625rem] border-black-900 first:rounded-none first:text-black-900 last:rounded-none hover:bg-transparent bg-transparent focus:ring-0 origin-top-left transition-all"
    type="button"
  >
    <h2
      class=""
      data-testid="flowbite-accordion-heading"
    >
      Title 3
    </h2>
    <svg
      aria-hidden="true"
      class="h-10 w-10 shrink-0"
      data-testid="flowbite-accordion-arrow"
      fill="currentColor"
      height="1em"
      stroke="currentColor"
      stroke-width="0"
      viewBox="0 0 20 20"
      width="1em"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        clip-rule="evenodd"
        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
        fill-rule="evenodd"
      />
    </svg>
  </button>
  <div
    class="last:rounded-b-lg dark:bg-gray-900 first:rounded-t-lg p-0 border-none font-rubik overflow-hidden"
    data-testid="flowbite-accordion-content"
    hidden=""
  >
    <div
      class="[&:not([hidden])]:animate-slideDown origin-top-left transition-all"
    >
      Content 3
    </div>
  </div>
</div>
`;
