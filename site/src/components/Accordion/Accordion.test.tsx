import { fireEvent, render, screen } from "@tests/render";

import Accordion, {
  AccordionPanel,
  AccordionTitle,
  AccordionContent,
} from "./Accordion";

describe("<Accordion />", () => {
  it("should render the Accordion component", () => {
    render(
      <Accordion>
        <AccordionPanel>
          <AccordionTitle>Title</AccordionTitle>
          <AccordionContent>Content</AccordionContent>
        </AccordionPanel>
      </Accordion>,
    );
    expect(screen.getByTestId("flowbite-accordion")).toMatchSnapshot();
  });

  it("should render the Accordion collapsed, and when click in the button render content", () => {
    render(
      <Accordion collapseAll>
        <AccordionPanel>
          <AccordionTitle>Title</AccordionTitle>
          <AccordionContent>Content</AccordionContent>
        </AccordionPanel>
      </Accordion>,
    );
    expect(screen.getByTestId("flowbite-accordion-content")).toMatchSnapshot();
    expect(
      screen
        .getByTestId("flowbite-accordion-content")
        .getAttributeNode("hidden"),
    ).toBeTruthy();
    fireEvent.click(screen.getByRole("button"));
    expect(
      screen
        .getByTestId("flowbite-accordion-content")
        .getAttributeNode("hidden"),
    ).toBeFalsy();
  });

  it("should render the Accordion with 3 panels", () => {
    render(
      <Accordion>
        <AccordionPanel>
          <AccordionTitle>Title 1</AccordionTitle>
          <AccordionContent>Content 1</AccordionContent>
        </AccordionPanel>
        <AccordionPanel>
          <AccordionTitle>Title 2</AccordionTitle>
          <AccordionContent>Content 2</AccordionContent>
        </AccordionPanel>
        <AccordionPanel>
          <AccordionTitle>Title 3</AccordionTitle>
          <AccordionContent>Content 3</AccordionContent>
        </AccordionPanel>
      </Accordion>,
    );
    expect(screen.getByTestId("flowbite-accordion")).toMatchSnapshot();
  });
});
