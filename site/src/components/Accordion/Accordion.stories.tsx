import { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import {
  Accordion,
  AccordionContent,
  AccordionPanel,
  AccordionProps,
  AccordionTitle,
} from "./Accordion";

const story: Meta<AccordionProps> = {
  component: Accordion,
  title: "Accordion",
  render: ({ children = "Accordion", ...args }) => (
    <Accordion {...args}>{children}</Accordion>
  ),
} as Meta;

export default story;

type Story = StoryObj<AccordionProps>;

export const Default: Story = {
  args: {
    children: (
      <AccordionPanel>
        <AccordionTitle>Accordion Item</AccordionTitle>
        <AccordionContent>
          <p className="mb-2 font-rubik font-normal text-black-900">
            Yes, Preline is an open-source project and is copyright 2022
            Htmlstream.
          </p>
        </AccordionContent>
      </AccordionPanel>
    ),
  },
};

const WithThreePanels = () => {
  return (
    <Accordion>
      <AccordionPanel>
        <AccordionTitle>Accordion Item 1</AccordionTitle>
        <AccordionContent>
          <p className="mb-2 font-rubik font-normal text-black-900">
            Flowbite is the first panel of 3, so we are testing the option to
            use more then 1 Panel.
          </p>
        </AccordionContent>
      </AccordionPanel>
      <AccordionPanel>
        <AccordionTitle>Accordion Item 2</AccordionTitle>
        <AccordionContent>
          <p className="mb-2 font-rubik font-normal text-black-900">
            Flowbite is the second panel of 3, so we are testing the option to
            use more then 1 Panel.
          </p>
        </AccordionContent>
      </AccordionPanel>
      <AccordionPanel>
        <AccordionTitle>Accordion Item 3</AccordionTitle>
        <AccordionContent>
          <p className="mb-2 font-rubik font-normal text-black-900">
            Flowbite is the third panel of 3, so we are testing the option to
            use more then 1 Panel.
          </p>
        </AccordionContent>
      </AccordionPanel>
    </Accordion>
  );
};

export const ThreePanels: Story = {
  render: () => <WithThreePanels />,
};
