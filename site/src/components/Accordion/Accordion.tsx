import {
  Accordion as FlowAccordion,
  AccordionProps as FlowAccordionProps,
  AccordionPanelProps as FlowAccordionPanel,
  AccordionTitleProps as FlowAccordionTitleProps,
  AccordionContentProps as FlowAccordionContentProps,
} from "flowbite-react";
import { tv } from "tailwind-variants";

export interface AccordionProps extends FlowAccordionProps {}

export interface AccordionPanelProps extends FlowAccordionPanel {}

export interface AccordionTitleProps extends FlowAccordionTitleProps {}

export interface AccordionContentProps extends FlowAccordionContentProps {}

const accordionTheme = tv({
  base: "rounded-none border-0 bg-transparent divide-black-900 origin-top-left transition-all",
});

const panelStyle = tv({
  base: "rounded-none border-0 bg-transparent origin-top-left transition-all",
});

const titleStyle = tv({
  base: "border-0 border-t-[0.0625rem] border-black-900 first:rounded-none first:text-black-900 last:rounded-none hover:bg-transparent bg-transparent focus:ring-0 origin-top-left transition-all",
});

const titleTheme: AccordionTitleProps["theme"] = {
  base: "flex w-full items-center justify-between py-[0.88rem] text-left text-2xl font-semibold font-outfit",
  arrow: {
    base: "h-10 w-10 shrink-0",
  },
};

const contentStyle = tv({
  base: "p-0 border-none font-rubik overflow-hidden",
});

export function Accordion({ children, className, ...rest }: AccordionProps) {
  return (
    <FlowAccordion className={accordionTheme({ className })} {...rest}>
      {children}
    </FlowAccordion>
  );
}

export function AccordionPanel({
  children,
  className,
  ...rest
}: AccordionPanelProps) {
  return (
    <FlowAccordion.Panel className={panelStyle({ className })} {...rest}>
      {children}
    </FlowAccordion.Panel>
  );
}

export function AccordionTitle({
  children,
  className,
  ...rest
}: AccordionTitleProps) {
  return (
    <FlowAccordion.Title
      theme={titleTheme}
      className={titleStyle({ className })}
      {...rest}
    >
      {children}
    </FlowAccordion.Title>
  );
}

export function AccordionContent({
  children,
  className,
  ...rest
}: AccordionContentProps) {
  return (
    <FlowAccordion.Content className={contentStyle({ className })} {...rest}>
      <div className="[&:not([hidden])]:animate-slideDown origin-top-left transition-all">
        {children}
      </div>
    </FlowAccordion.Content>
  );
}

export default Accordion;
