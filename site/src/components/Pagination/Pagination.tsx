import { Button } from "..";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import { tv } from "tailwind-variants";

export interface PaginationProps {
  amount: number;
  onChange?: (value: number) => void;
  value: number;
  variant?: "primary" | "secondary";
  size?: "small" | "normal" | "large";
  className?: string;
}

const style = tv({
  base: "flex flex-row gap-2 items-center w-fit",
});

const buttonStyle = tv({
  base: "rounded-full text-center flex items-center justify-center",
  variants: {
    size: {
      small: "w-7 h-7 ",
      normal: "w-10 h-10",
      large: "w-12 h-12",
    },
  },
  compoundVariants: [
    {
      size: ["large", "small", "normal"],
    },
  ],
  defaultVariants: {
    size: "normal",
  },
});

const iconStyle = tv({
  base: "stroke-[0.25rem] cursor-pointer",
  variants: {
    variant: {
      primary: "",
      secondary: "text-white",
    },
    size: {
      small: "min-w-[1rem]",
      normal: "min-w-[1.75rem] ",
      large: "min-w-[2rem]",
    },
  },
  compoundVariants: [
    {
      variant: ["primary", "secondary"],
      size: ["small", "normal", "large"],
    },
  ],
  defaultVariants: {
    variant: "primary",
    size: "normal",
  },
});

export function Pagination({
  amount,
  onChange,
  value,
  variant = "primary",
  size = "normal",
  className,
  ...rest
}: PaginationProps) {
  const buttons = [...Array(amount).keys()];
  return (
    <div className={style({ className, size })} {...rest}>
      <div className={iconStyle({ size })}>
        {value > 1 && (
          <ChevronLeftIcon
            data-testid="left-icon"
            className={iconStyle({ variant, size })}
            onClick={() => onChange && onChange(value - 1)}
          />
        )}
      </div>
      {buttons.map((button) => (
        <div key={`button-${button + 1}`} className="w-fit">
          <Button
            data-testid={`button-${button + 1}`}
            size={size}
            className={buttonStyle({ size })}
            color={
              button + 1 === value
                ? "primary"
                : variant === "secondary"
                  ? "quartenary"
                  : "secondary"
            }
            onClick={() => {
              onChange && onChange(button + 1);
            }}
          >
            <p className="select-none tracking-normal">{button + 1}</p>
          </Button>
        </div>
      ))}
      <div className={iconStyle({ size })}>
        {value < amount && (
          <ChevronRightIcon
            data-testid="right-icon"
            className={iconStyle({ variant, size })}
            onClick={() => onChange && onChange(value + 1)}
          />
        )}
      </div>
    </div>
  );
}

export default Pagination;
