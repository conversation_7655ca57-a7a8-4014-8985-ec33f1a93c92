import { Meta, StoryObj } from "@storybook/react";
import { PaginationProps, Pagination } from "./Pagination";
import { useState } from "react";

interface PaginationWithHooksProps {
  variant?: "primary" | "secondary";
  size?: "small" | "normal" | "large";
}

const story: Meta<PaginationProps> = {
  component: Pagination,
  parameters: {
    backgrounds: {
      default: "light",
      values: [
        { name: "light", value: "#fff" },
        { name: "dark", value: "#333333" },
      ],
    },
  },
  title: "Pagination",
} as Meta;

export default story;

type Story = StoryObj<PaginationProps>;

const PaginationWithHooks = ({
  variant = "primary",
  size = "normal",
}: PaginationWithHooksProps) => {
  const [index, setIndex] = useState(1);
  return (
    <Pagination
      size={size}
      amount={4}
      value={index}
      onChange={setIndex}
      variant={variant}
    />
  );
};

export const Default: Story = {
  render: () => PaginationWithHooks({ variant: "primary" }),
};

export const Small: Story = {
  render: () => PaginationWithHooks({ size: "small" }),
};

export const Large: Story = {
  render: () => PaginationWithHooks({ size: "large" }),
};

export const DarkBackground: Story = {
  parameters: {
    backgrounds: {
      default: "dark",
    },
  },
  render: () => PaginationWithHooks({ variant: "secondary" }),
};
