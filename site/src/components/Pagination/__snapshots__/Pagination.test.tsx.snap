// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Pagination /> should render the Pagination for dark backgrounds 1`] = `
<div
  class="flex flex-row gap-2 items-center w-fit"
  data-testid="pagination"
>
  <div
    class="stroke-[0.25rem] cursor-pointer min-w-[1.75rem]"
  />
  <div
    class="w-fit"
  >
    <button
      class="py-3 px-4 font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-xs rounded-full text-center flex items-center justify-center w-10 h-10"
      data-testid="button-1"
    >
      <p
        class="select-none tracking-normal"
      >
        1
      </p>
    </button>
  </div>
  <div
    class="w-fit"
  >
    <button
      class="py-3 px-4 font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-white text-xs rounded-full text-center flex items-center justify-center w-10 h-10"
      data-testid="button-2"
    >
      <p
        class="select-none tracking-normal"
      >
        2
      </p>
    </button>
  </div>
  <div
    class="w-fit"
  >
    <button
      class="py-3 px-4 font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-white text-xs rounded-full text-center flex items-center justify-center w-10 h-10"
      data-testid="button-3"
    >
      <p
        class="select-none tracking-normal"
      >
        3
      </p>
    </button>
  </div>
  <div
    class="w-fit"
  >
    <button
      class="py-3 px-4 font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-white text-xs rounded-full text-center flex items-center justify-center w-10 h-10"
      data-testid="button-4"
    >
      <p
        class="select-none tracking-normal"
      >
        4
      </p>
    </button>
  </div>
  <div
    class="stroke-[0.25rem] cursor-pointer min-w-[1.75rem]"
  >
    <svg
      aria-hidden="true"
      class="stroke-[0.25rem] cursor-pointer text-white min-w-[1.75rem]"
      data-slot="icon"
      data-testid="right-icon"
      fill="none"
      stroke="currentColor"
      stroke-width="1.5"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="m8.25 4.5 7.5 7.5-7.5 7.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  </div>
</div>
`;

exports[`<Pagination /> should render the Pagination large size 1`] = `
<div
  class="flex flex-row gap-2 items-center w-fit"
  data-testid="pagination"
>
  <div
    class="stroke-[0.25rem] cursor-pointer min-w-[2rem]"
  />
  <div
    class="w-fit"
  >
    <button
      class="font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-sm px-5 py-[0.875rem] rounded-full text-center flex items-center justify-center w-12 h-12"
      data-testid="button-1"
    >
      <p
        class="select-none tracking-normal"
      >
        1
      </p>
    </button>
  </div>
  <div
    class="w-fit"
  >
    <button
      class="font-outfit font-medium tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-sm px-5 py-[0.875rem] rounded-full text-center flex items-center justify-center w-12 h-12"
      data-testid="button-2"
    >
      <p
        class="select-none tracking-normal"
      >
        2
      </p>
    </button>
  </div>
  <div
    class="w-fit"
  >
    <button
      class="font-outfit font-medium tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-sm px-5 py-[0.875rem] rounded-full text-center flex items-center justify-center w-12 h-12"
      data-testid="button-3"
    >
      <p
        class="select-none tracking-normal"
      >
        3
      </p>
    </button>
  </div>
  <div
    class="w-fit"
  >
    <button
      class="font-outfit font-medium tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-sm px-5 py-[0.875rem] rounded-full text-center flex items-center justify-center w-12 h-12"
      data-testid="button-4"
    >
      <p
        class="select-none tracking-normal"
      >
        4
      </p>
    </button>
  </div>
  <div
    class="stroke-[0.25rem] cursor-pointer min-w-[2rem]"
  >
    <svg
      aria-hidden="true"
      class="stroke-[0.25rem] cursor-pointer min-w-[2rem]"
      data-slot="icon"
      data-testid="right-icon"
      fill="none"
      stroke="currentColor"
      stroke-width="1.5"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="m8.25 4.5 7.5 7.5-7.5 7.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  </div>
</div>
`;

exports[`<Pagination /> should render the Pagination small size 1`] = `
<div
  class="flex flex-row gap-2 items-center w-fit"
  data-testid="pagination"
>
  <div
    class="stroke-[0.25rem] cursor-pointer min-w-[1rem]"
  />
  <div
    class="w-fit"
  >
    <button
      class="font-outfit text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-[0.5625rem] font-semibold py-[0.4532rem] px-3 rounded-full text-center flex items-center justify-center w-7 h-7"
      data-testid="button-1"
    >
      <p
        class="select-none tracking-normal"
      >
        1
      </p>
    </button>
  </div>
  <div
    class="w-fit"
  >
    <button
      class="font-outfit tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-[0.5625rem] font-semibold py-[0.4532rem] px-3 rounded-full text-center flex items-center justify-center w-7 h-7"
      data-testid="button-2"
    >
      <p
        class="select-none tracking-normal"
      >
        2
      </p>
    </button>
  </div>
  <div
    class="w-fit"
  >
    <button
      class="font-outfit tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-[0.5625rem] font-semibold py-[0.4532rem] px-3 rounded-full text-center flex items-center justify-center w-7 h-7"
      data-testid="button-3"
    >
      <p
        class="select-none tracking-normal"
      >
        3
      </p>
    </button>
  </div>
  <div
    class="w-fit"
  >
    <button
      class="font-outfit tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-[0.5625rem] font-semibold py-[0.4532rem] px-3 rounded-full text-center flex items-center justify-center w-7 h-7"
      data-testid="button-4"
    >
      <p
        class="select-none tracking-normal"
      >
        4
      </p>
    </button>
  </div>
  <div
    class="stroke-[0.25rem] cursor-pointer min-w-[1rem]"
  >
    <svg
      aria-hidden="true"
      class="stroke-[0.25rem] cursor-pointer min-w-[1rem]"
      data-slot="icon"
      data-testid="right-icon"
      fill="none"
      stroke="currentColor"
      stroke-width="1.5"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="m8.25 4.5 7.5 7.5-7.5 7.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  </div>
</div>
`;

exports[`<Pagination /> should render the Pagination with 4 buttons 1`] = `
<div
  class="flex flex-row gap-2 items-center w-fit"
  data-testid="pagination"
>
  <div
    class="stroke-[0.25rem] cursor-pointer min-w-[1.75rem]"
  />
  <div
    class="w-fit"
  >
    <button
      class="py-3 px-4 font-outfit font-medium text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white text-xs rounded-full text-center flex items-center justify-center w-10 h-10"
      data-testid="button-1"
    >
      <p
        class="select-none tracking-normal"
      >
        1
      </p>
    </button>
  </div>
  <div
    class="w-fit"
  >
    <button
      class="py-3 px-4 font-outfit font-medium tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-xs rounded-full text-center flex items-center justify-center w-10 h-10"
      data-testid="button-2"
    >
      <p
        class="select-none tracking-normal"
      >
        2
      </p>
    </button>
  </div>
  <div
    class="w-fit"
  >
    <button
      class="py-3 px-4 font-outfit font-medium tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-xs rounded-full text-center flex items-center justify-center w-10 h-10"
      data-testid="button-3"
    >
      <p
        class="select-none tracking-normal"
      >
        3
      </p>
    </button>
  </div>
  <div
    class="w-fit"
  >
    <button
      class="py-3 px-4 font-outfit font-medium tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-black-900 text-white hover:bg-black-800 text-xs rounded-full text-center flex items-center justify-center w-10 h-10"
      data-testid="button-4"
    >
      <p
        class="select-none tracking-normal"
      >
        4
      </p>
    </button>
  </div>
  <div
    class="stroke-[0.25rem] cursor-pointer min-w-[1.75rem]"
  >
    <svg
      aria-hidden="true"
      class="stroke-[0.25rem] cursor-pointer min-w-[1.75rem]"
      data-slot="icon"
      data-testid="right-icon"
      fill="none"
      stroke="currentColor"
      stroke-width="1.5"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="m8.25 4.5 7.5 7.5-7.5 7.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  </div>
</div>
`;
