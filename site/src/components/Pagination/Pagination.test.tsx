import { fireEvent, render, screen } from "@tests/render";
import Pagination from "./Pagination";

describe("<Pagination />", () => {
  let value = 0;
  function handleOnChange(newValue: number) {
    value = newValue;
  }

  beforeEach(() => {
    handleOnChange(1);
  });

  it("should render the Pagination with 4 buttons", () => {
    render(
      <Pagination
        value={value}
        onChange={handleOnChange}
        amount={4}
        data-testid="pagination"
      />,
    );
    expect(screen.getByTestId("pagination")).toMatchSnapshot();
  });

  it("should render the Pagination small size", () => {
    render(
      <Pagination
        value={value}
        onChange={handleOnChange}
        size="small"
        amount={4}
        data-testid="pagination"
      />,
    );
    expect(screen.getByTestId("pagination")).toMatchSnapshot();
  });

  it("should render the Pagination large size", () => {
    render(
      <Pagination
        value={value}
        onChange={handleOnChange}
        size="large"
        amount={4}
        data-testid="pagination"
      />,
    );
    expect(screen.getByTestId("pagination")).toMatchSnapshot();
  });

  it("should render the Pagination for dark backgrounds", () => {
    render(
      <Pagination
        value={value}
        onChange={handleOnChange}
        amount={4}
        variant="secondary"
        data-testid="pagination"
      />,
    );
    expect(screen.getByTestId("pagination")).toMatchSnapshot();
  });

  it("value starts in 1 and should change to 3 clicking on button 3", () => {
    render(
      <Pagination
        value={value}
        onChange={handleOnChange}
        amount={4}
        data-testid="pagination"
      />,
    );
    fireEvent.click(screen.getByTestId("button-3"));
    expect(value).toEqual(3);
  });

  it("value starts in 1 and should change to 2 clicking on right arrow", () => {
    render(
      <Pagination
        value={value}
        onChange={handleOnChange}
        amount={4}
        data-testid="pagination"
      />,
    );
    fireEvent.click(screen.getByTestId("right-icon"));
    expect(value).toEqual(2);
  });

  it("value starts in 4 and should change to 3 clicking on left arrow", () => {
    handleOnChange(4);
    render(
      <Pagination
        value={value}
        onChange={handleOnChange}
        amount={4}
        data-testid="pagination"
      />,
    );
    fireEvent.click(screen.getByTestId("left-icon"));
    expect(value).toEqual(3);
  });
});
