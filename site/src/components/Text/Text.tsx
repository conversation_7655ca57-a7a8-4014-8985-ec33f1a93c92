import { BaseHTMLAttributes } from "react";
import { tv } from "tailwind-variants";

export interface TextProps extends BaseHTMLAttributes<HTMLParagraphElement> {}

const style = tv({
  base: "font-rubik font-[400] text-[0.75rem]",
});

export function Text({ children, className, ...rest }: TextProps) {
  return (
    <p className={style({ className })} {...rest}>
      {children}
    </p>
  );
}

export default Text;
