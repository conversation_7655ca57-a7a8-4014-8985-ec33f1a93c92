import { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import {
  TableProps,
  Table,
  TableHead,
  TableHeadCell,
  TableBody,
  TableRow,
  TableCell,
} from "./Table";
import { useState } from "react";
import { Text } from "..";

const story: Meta<TableProps> = {
  component: Table,
  title: "Table",
  render: ({ children = "Table", ...args }) => (
    <Table {...args}>{children}</Table>
  ),
} as Meta;

export default story;

type Story = StoryObj<TableProps>;

const tableContent = {
  headCells: ["Name", "Age", "Address", "Email"],
  bodyCells: [
    {
      name: "<PERSON>",
      age: 45,
      address: "New York No. 1 Lake Park",
      email: "<EMAIL>",
    },
    {
      name: "<PERSON>",
      age: 27,
      address: "London No. 1 Lake Park",
      email: "<EMAIL>",
    },
    {
      name: "<PERSON>",
      age: 31,
      address: "Sidney No. 1 Lake Park",
      email: "<EMAIL>",
    },
    {
      name: "<PERSON>",
      age: 16,
      address: "LA No. 1 Lake Park",
      email: "<EMAIL>",
    },
  ],
};

export const Default: Story = {
  args: {
    children: (
      <>
        <TableHead>
          {tableContent.headCells.map((headCell, index) => (
            <TableHeadCell key={`headCell-${index}`}>{headCell}</TableHeadCell>
          ))}
        </TableHead>
        <TableBody>
          {tableContent.bodyCells.map((bodyCell, index) => (
            <TableRow key={`table-row-${index}`}>
              <TableCell>{bodyCell.name}</TableCell>
              <TableCell>{bodyCell.age}</TableCell>
              <TableCell>{bodyCell.address}</TableCell>
              <TableCell>{bodyCell.email}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </>
    ),
  },
};

const TableWithHook = () => {
  const [content, setContent] = useState(tableContent);

  function deleteCell(index: number) {
    const newContent = { ...content };
    newContent.bodyCells.splice(index, 1);
    setContent(newContent);
  }

  return (
    <Table>
      <TableHead>
        {tableContent.headCells.map((headCell, index) => (
          <TableHeadCell key={`headCell-${index}`}>{headCell}</TableHeadCell>
        ))}
        <TableHeadCell>Action</TableHeadCell>
      </TableHead>
      <TableBody>
        {tableContent.bodyCells.map((bodyCell, index) => (
          <TableRow key={`table-row-${index}`}>
            <TableCell>{bodyCell.name}</TableCell>
            <TableCell>{bodyCell.age}</TableCell>
            <TableCell>{bodyCell.address}</TableCell>
            <TableCell>{bodyCell.email}</TableCell>
            <TableCell>
              <Text
                className="text-base font-rubik font-medium text-yellow-900 cursor-pointer"
                onClick={() => deleteCell(index)}
              >
                Delete
              </Text>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export const WithHooks: Story = {
  render: () => <TableWithHook />,
};
