import {
  Table as FlowTable,
  TableProps as FlowTableProps,
  TableHeadProps as FlowTableHeaderProps,
  TableHeadCellProps as FlowTableHeaderCellProps,
  TableBodyProps as FlowTableBodyProps,
  TableRowProps as FlowTableRowProps,
  TableCellProps as FlowTableCellProps,
} from "flowbite-react";
import { tv } from "tailwind-variants";

export interface TableProps extends FlowTableProps {}

export interface TableHeaderProps extends FlowTableHeaderProps {}

export interface TableHeaderCellProps extends FlowTableHeaderCellProps {}

export interface TableBodyProps extends FlowTableBodyProps {}

export interface TableRowProps extends FlowTableRowProps {}

export interface TableCellProps extends FlowTableCellProps {}

const theme: TableProps["theme"] = {
  root: {
    base: "w-full text-left text-sm text-gray-500 dark:text-gray-400 divide-y divide-gray-700 bg-white border-b border-b-gray-700",
    shadow: "",
    wrapper: "relative",
  },
  body: {
    base: "group/body divide-y divide-gray-700",
    cell: {
      base: "px-5 py-3 text-base font-rubik font-normal text-black-600",
    },
  },
  head: {
    base: "group/head text-xs uppercase text-gray-700",
    cell: {
      base: "px-5 py-3 text-sm font-outfit font-medium text-yellow-900",
    },
  },
  row: {
    base: "group/row",
    hovered: "",
    striped:
      "odd:bg-white even:bg-gray-50 odd:dark:bg-gray-800 even:dark:bg-gray-700",
  },
};

const bodyStyle = tv({
  base: "text-base font-rubik font-normal text-black-600",
});

export function Table({ children, className }: TableProps) {
  return (
    <FlowTable theme={theme} className={className} data-testid="table">
      {children}
    </FlowTable>
  );
}

export function TableHead({ children, className }: TableHeaderProps) {
  return <FlowTable.Head className={className}>{children}</FlowTable.Head>;
}

export function TableHeadCell({ children, className }: TableHeaderCellProps) {
  return (
    <FlowTable.HeadCell className={className}>{children}</FlowTable.HeadCell>
  );
}

export function TableBody({ children, className }: TableBodyProps) {
  return (
    <FlowTable.Body className={bodyStyle({ className })}>
      {children}
    </FlowTable.Body>
  );
}

export function TableRow({ children, className }: TableRowProps) {
  return <FlowTable.Row className={className}>{children}</FlowTable.Row>;
}

export function TableCell({ children, className }: TableCellProps) {
  return <FlowTable.Cell className={className}>{children}</FlowTable.Cell>;
}

export default Table;
