// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Table /> should render the table 1`] = `
<table
  class="w-full text-left text-sm text-gray-500 dark:text-gray-400 divide-y divide-gray-700 bg-white border-b border-b-gray-700"
  data-testid="table"
>
  <thead
    class="group/head text-xs uppercase text-gray-700"
  >
    <tr>
      <th
        class="px-5 py-3 text-sm font-outfit font-medium text-yellow-900"
      >
        Name
      </th>
      <th
        class="px-5 py-3 text-sm font-outfit font-medium text-yellow-900"
      >
        Age
      </th>
      <th
        class="px-5 py-3 text-sm font-outfit font-medium text-yellow-900"
      >
        Address
      </th>
      <th
        class="px-5 py-3 text-sm font-outfit font-medium text-yellow-900"
      >
        Email
      </th>
    </tr>
  </thead>
  <tbody
    class="group/body divide-y divide-gray-700 text-base font-rubik font-normal text-black-600"
  >
    <tr
      class="group/row"
      data-testid="table-row-element"
    >
      <td
        class="px-5 py-3 text-base font-rubik font-normal text-black-600"
      >
        <PERSON>
      </td>
      <td
        class="px-5 py-3 text-base font-rubik font-normal text-black-600"
      >
        45
      </td>
      <td
        class="px-5 py-3 text-base font-rubik font-normal text-black-600"
      >
        New York No. 1 Lake Park
      </td>
      <td
        class="px-5 py-3 text-base font-rubik font-normal text-black-600"
      >
        <EMAIL>
      </td>
    </tr>
    <tr
      class="group/row"
      data-testid="table-row-element"
    >
      <td
        class="px-5 py-3 text-base font-rubik font-normal text-black-600"
      >
        Jim Green
      </td>
      <td
        class="px-5 py-3 text-base font-rubik font-normal text-black-600"
      >
        27
      </td>
      <td
        class="px-5 py-3 text-base font-rubik font-normal text-black-600"
      >
        London No. 1 Lake Park
      </td>
      <td
        class="px-5 py-3 text-base font-rubik font-normal text-black-600"
      >
        <EMAIL>
      </td>
    </tr>
    <tr
      class="group/row"
      data-testid="table-row-element"
    >
      <td
        class="px-5 py-3 text-base font-rubik font-normal text-black-600"
      >
        Joe Black
      </td>
      <td
        class="px-5 py-3 text-base font-rubik font-normal text-black-600"
      >
        31
      </td>
      <td
        class="px-5 py-3 text-base font-rubik font-normal text-black-600"
      >
        Sidney No. 1 Lake Park
      </td>
      <td
        class="px-5 py-3 text-base font-rubik font-normal text-black-600"
      >
        <EMAIL>
      </td>
    </tr>
    <tr
      class="group/row"
      data-testid="table-row-element"
    >
      <td
        class="px-5 py-3 text-base font-rubik font-normal text-black-600"
      >
        Edward King
      </td>
      <td
        class="px-5 py-3 text-base font-rubik font-normal text-black-600"
      >
        16
      </td>
      <td
        class="px-5 py-3 text-base font-rubik font-normal text-black-600"
      >
        LA No. 1 Lake Park
      </td>
      <td
        class="px-5 py-3 text-base font-rubik font-normal text-black-600"
      >
        <EMAIL>
      </td>
    </tr>
  </tbody>
</table>
`;
