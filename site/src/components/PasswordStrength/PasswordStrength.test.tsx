import { render, screen } from "@tests/render";
import PasswordStrength from "./PasswordStrength";

describe("PasswordStrength", () => {
  it("should render the PasswordStrength with default state", () => {
    render(<PasswordStrength value="" />);
    expect(screen.getByTestId("password-strength")).toMatchSnapshot();
  });

  test("renders password strength component", () => {
    render(<PasswordStrength value="" />);
    const passwordStrengthElement = screen.getByTestId("password-strength");
    expect(passwordStrengthElement).toBeTruthy();
  });

  test("updates strength based on password input", () => {
    const { container, rerender } = render(<PasswordStrength value="" />);
    expect(container.firstChild).toMatchSnapshot();
    rerender(<PasswordStrength value="abcd" />);
    expect(container.firstChild).toMatchSnapshot();
  });

  describe("PasswordStrength strength levels", () => {
    test("renders Weak state", () => {
      render(<PasswordStrength value="abc" />);
      expect(
        screen.getAllByTestId("password-Progress-strength-0"),
      ).toHaveLength(1);
    });

    test("renders Medium state", () => {
      render(<PasswordStrength value="abc123" />);
      expect(
        screen.getAllByTestId("password-Progress-strength-1"),
      ).toHaveLength(1);
    });

    test("renders Strong state", () => {
      render(<PasswordStrength value="abc123!" />);
      expect(
        screen.getAllByTestId("password-Progress-strength-2"),
      ).toHaveLength(1);
    });

    test("renders Very Strong state", () => {
      render(<PasswordStrength value="abc123!XYZ" />);
      expect(
        screen.getAllByTestId("password-Progress-strength-3"),
      ).toHaveLength(1);
    });
  });
});
