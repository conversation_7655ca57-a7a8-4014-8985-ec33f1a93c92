import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { tv } from "tailwind-variants";
import CalculateStrength from "./CalculateStrength";

export interface PasswordStrengthProps {
  value: string;
  className?: string;
}

const baseStyle = tv({
  base: "flex flex-col mt-1 gap-2 p-[0.313rem] h-0 w-full bg-white rounded-full",
});

const baseProgressBarDisabled = tv({
  extend: baseStyle,
  base: ["bg-white"],
});

const baseProgressBarWeak = tv({
  extend: baseStyle,
  base: ["bg-red-500"],
});

const baseProgressBarMedium = tv({
  extend: baseStyle,
  base: ["bg-yellow-500"],
});

const baseProgressBarStrong = tv({
  extend: baseStyle,
  base: ["bg-blue-500"],
});

const baseProgressBarVeryStrong = tv({
  extend: baseStyle,
  base: ["bg-green-400"],
});

interface StrengthType {
  name: string;
  css: string;
}

const PasswordStrength: React.FC<PasswordStrengthProps> = ({
  value,
  className,
}) => {
  const [strength, setStrength] = useState<number>(0);
  const { t } = useTranslation();

  const strengths: StrengthType[] = [
    {
      name: t("components.PasswordStrength.weak"),
      css: baseProgressBarWeak(),
    },
    {
      name: t("components.PasswordStrength.medium"),
      css: baseProgressBarMedium(),
    },
    {
      name: t("components.PasswordStrength.strong"),
      css: baseProgressBarStrong(),
    },
    {
      name: t("components.PasswordStrength.very-strong"),
      css: baseProgressBarVeryStrong(),
    },
  ];

  useEffect(() => {
    const newStrength = CalculateStrength(value);
    setStrength(newStrength as number);
  }, [value]);

  return (
    <div data-testid="password-strength" className={className}>
      <h1 className="flex mb-1.5 text-[0.625rem] font-semibold font-outfit not-italic leading-4 text-black-600">
        {t("components.PasswordStrength.label")}
      </h1>
      <div className="flex flex-row gap-2">
        {strengths.map((_, index) => (
          <progress
            data-testid={"password-Progress-strength-" + index}
            key={index}
            className={
              strength === 0 || strength - 1 < index
                ? baseProgressBarDisabled()
                : strengths[strength - 1].css
            }
            max="100"
          />
        ))}
      </div>
      <p className="mt-1 text-[0.5625rem] text-right font-medium font-outfit text-black-600 uppercase leading-[0.5625rem] tracking-[0.0625rem]">
        {strength === 0
          ? strengths[strength].name
          : strengths[strength - 1].name}
      </p>
    </div>
  );
};

export default PasswordStrength;
