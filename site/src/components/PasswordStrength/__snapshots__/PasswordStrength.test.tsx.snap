// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PasswordStrength should render the PasswordStrength with default state 1`] = `
<div
  data-testid="password-strength"
>
  <h1
    class="flex mb-1.5 text-[0.625rem] font-semibold font-outfit not-italic leading-4 text-black-600"
  >
    components.PasswordStrength.label
  </h1>
  <div
    class="flex flex-row gap-2"
  >
    <progress
      class="flex flex-col mt-1 gap-2 p-[0.313rem] h-0 w-full rounded-full bg-white"
      data-testid="password-Progress-strength-0"
      max="100"
    />
    <progress
      class="flex flex-col mt-1 gap-2 p-[0.313rem] h-0 w-full rounded-full bg-white"
      data-testid="password-Progress-strength-1"
      max="100"
    />
    <progress
      class="flex flex-col mt-1 gap-2 p-[0.313rem] h-0 w-full rounded-full bg-white"
      data-testid="password-Progress-strength-2"
      max="100"
    />
    <progress
      class="flex flex-col mt-1 gap-2 p-[0.313rem] h-0 w-full rounded-full bg-white"
      data-testid="password-Progress-strength-3"
      max="100"
    />
  </div>
  <p
    class="mt-1 text-[0.5625rem] text-right font-medium font-outfit text-black-600 uppercase leading-[0.5625rem] tracking-[0.0625rem]"
  >
    components.PasswordStrength.weak
  </p>
</div>
`;

exports[`PasswordStrength updates strength based on password input 1`] = `
<div
  data-testid="password-strength"
>
  <h1
    class="flex mb-1.5 text-[0.625rem] font-semibold font-outfit not-italic leading-4 text-black-600"
  >
    components.PasswordStrength.label
  </h1>
  <div
    class="flex flex-row gap-2"
  >
    <progress
      class="flex flex-col mt-1 gap-2 p-[0.313rem] h-0 w-full rounded-full bg-white"
      data-testid="password-Progress-strength-0"
      max="100"
    />
    <progress
      class="flex flex-col mt-1 gap-2 p-[0.313rem] h-0 w-full rounded-full bg-white"
      data-testid="password-Progress-strength-1"
      max="100"
    />
    <progress
      class="flex flex-col mt-1 gap-2 p-[0.313rem] h-0 w-full rounded-full bg-white"
      data-testid="password-Progress-strength-2"
      max="100"
    />
    <progress
      class="flex flex-col mt-1 gap-2 p-[0.313rem] h-0 w-full rounded-full bg-white"
      data-testid="password-Progress-strength-3"
      max="100"
    />
  </div>
  <p
    class="mt-1 text-[0.5625rem] text-right font-medium font-outfit text-black-600 uppercase leading-[0.5625rem] tracking-[0.0625rem]"
  >
    components.PasswordStrength.weak
  </p>
</div>
`;

exports[`PasswordStrength updates strength based on password input 2`] = `
<div
  data-testid="password-strength"
>
  <h1
    class="flex mb-1.5 text-[0.625rem] font-semibold font-outfit not-italic leading-4 text-black-600"
  >
    components.PasswordStrength.label
  </h1>
  <div
    class="flex flex-row gap-2"
  >
    <progress
      class="flex flex-col mt-1 gap-2 p-[0.313rem] h-0 w-full rounded-full bg-red-500"
      data-testid="password-Progress-strength-0"
      max="100"
    />
    <progress
      class="flex flex-col mt-1 gap-2 p-[0.313rem] h-0 w-full rounded-full bg-white"
      data-testid="password-Progress-strength-1"
      max="100"
    />
    <progress
      class="flex flex-col mt-1 gap-2 p-[0.313rem] h-0 w-full rounded-full bg-white"
      data-testid="password-Progress-strength-2"
      max="100"
    />
    <progress
      class="flex flex-col mt-1 gap-2 p-[0.313rem] h-0 w-full rounded-full bg-white"
      data-testid="password-Progress-strength-3"
      max="100"
    />
  </div>
  <p
    class="mt-1 text-[0.5625rem] text-right font-medium font-outfit text-black-600 uppercase leading-[0.5625rem] tracking-[0.0625rem]"
  >
    components.PasswordStrength.weak
  </p>
</div>
`;
