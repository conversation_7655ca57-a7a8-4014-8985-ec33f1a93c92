import React, { HTMLAttributes, useState } from "react";
import { Link } from "react-router-dom";
import { Image } from "../../components";
import { tv } from "tailwind-variants";
import logo from "@/assets/logo.svg";
import logoMob from "@/assets/logo-mob.svg";

export interface LoggedOutNavbarProps extends HTMLAttributes<HTMLDivElement> {}

const style = tv({
  slots: {
    menuOpen:
      "block fixed top-0 left-0 w-[20.5rem] h-full bg-white border-r border-gray-100 lg:hidden z-50 duration-200 transition-all ease-out translate-x-0",
    menuClosed:
      "block fixed top-0 left-0 w-[20.5rem] h-full bg-white borber-r border-gray-100 lg:hidden z-50 duration-200 transition-all ease-out -translate-x-full",
    overlayOpen:
      "fixed w-full h-full top-0 left-0 z-20 bg-black-900 opacity-70 block transition-all duration-300",
    overlayClosed:
      "fixed w-full h-full top-0 left-0 z-20 bg-black-900 opacity-0 block pointer-events-none animate-delayedDisplayNone transition-all duration-300",
  },
});

const { menuOpen, menuClosed, overlayOpen, overlayClosed } = style();

const LoggedOutNavbar: React.FC<LoggedOutNavbarProps> = ({
  ...params
}: LoggedOutNavbarProps) => {
  const [isNavOpen, setIsNavOpen] = useState(false);

  const handleToggleMenu = () => {
    setIsNavOpen(!isNavOpen);
  };

  const handleCloseMenu = () => {
    setIsNavOpen(false);
  };

  return (
    <nav
      data-testid="TestID"
      {...params}
      className="flex whitespace-nowrap justify-between bg-[#FFFFFF] w-full h-14 items-center border-b border-gray-100 py-2 px-4 relative z-10"
    >
      <div className={isNavOpen ? overlayOpen() : overlayClosed()}></div>

      <div className="h-7">
        <Link to="/">
          <Image src={logo} className="hidden lg:block h-full w-auto" />
        </Link>
        <Link to="/" className="absolute m-9">
          <Image src={logoMob} className="block lg:hidden" />
        </Link>
      </div>

      <div className="flex justify-end items-center gap-4 md:gap-8">
        <Link
          to="/"
          className="hidden lg:block w-full py-3 px-4 rounded-md font-outfit font-medium text-xs text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed"
        >
          Entrar
        </Link>
        <Link
          to="/"
          className="hidden lg:block w-full py-3 px-4 rounded-md font-outfit font-medium text-xs text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white"
        >
          Criar Conta
        </Link>

        <button
          onClick={handleToggleMenu}
          className="flex w-10 h-10 justify-center items-center lg:hidden"
        >
          <div className="w-6 h-6">
            <svg
              className="w-6 h-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              onClick={() => setIsNavOpen(!isNavOpen)}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d={
                  isNavOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"
                }
              />
            </svg>
          </div>
        </button>
      </div>
      <div
        onClick={handleCloseMenu}
        className={isNavOpen ? menuOpen() : menuClosed()}
      >
        <section className="flex w-full h-12 p-4 justify-between">
          <a href="seu_link_aqui">
            <Image src={logo} />
          </a>
          <button
            onClick={handleToggleMenu}
            className="flex w-10 h-10 justify-center items-center lg:hidden"
          >
            <div className="w-6 h-6">
              <svg
                className="w-6 h-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                onClick={() => setIsNavOpen(!isNavOpen)}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d={
                    isNavOpen
                      ? "M6 18L18 6M6 6l12 12"
                      : "M4 6h16M4 12h16M4 18h16"
                  }
                />
              </svg>
            </div>
          </button>
        </section>
      </div>
    </nav>
  );
};

export default LoggedOutNavbar;
