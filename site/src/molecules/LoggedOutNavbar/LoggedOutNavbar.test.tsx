import { render, screen } from "@tests/render";
import LoggedOutNavbar from "./LoggedOutNavbar";

describe("Navbar", () => {
  it("renders Navbar component correctly", async () => {
    render(<LoggedOutNavbar />);
    const navbar = await screen.findByTestId("TestID");
    expect(navbar).toMatchSnapshot();
  });

  it("renders Navbar component responsively", async () => {
    render(<LoggedOutNavbar />);
    window.innerWidth = 320;
    window.innerHeight = 568;
    window.dispatchEvent(new Event("resize"));
    const navbar = await screen.findByTestId("TestID");
    expect(navbar).toMatchSnapshot();
  });
});
