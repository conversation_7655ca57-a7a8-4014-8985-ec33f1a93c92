// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Navbar renders Navbar component correctly 1`] = `
<nav
  class="flex whitespace-nowrap justify-between bg-[#FFFFFF] w-full h-14 items-center border-b border-gray-100 py-2 px-4 relative z-10"
  data-testid="TestID"
>
  <div
    class="fixed w-full h-full top-0 left-0 z-20 bg-black-900 opacity-0 block pointer-events-none animate-delayedDisplayNone transition-all duration-300"
  />
  <div
    class="h-7"
  >
    <a
      href="/"
    >
      <img
        class="hidden lg:block h-full w-auto"
        src=""
      />
    </a>
    <a
      class="absolute m-9"
      href="/"
    >
      <img
        class="block lg:hidden"
        src=""
      />
    </a>
  </div>
  <div
    class="flex justify-end items-center gap-4 md:gap-8"
  >
    <a
      class="hidden lg:block w-full py-3 px-4 rounded-md font-outfit font-medium text-xs text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed"
      href="/"
    >
      Entrar
    </a>
    <a
      class="hidden lg:block w-full py-3 px-4 rounded-md font-outfit font-medium text-xs text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white"
      href="/"
    >
      Criar Conta
    </a>
    <button
      class="flex w-10 h-10 justify-center items-center lg:hidden"
    >
      <div
        class="w-6 h-6"
      >
        <svg
          class="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            d="M4 6h16M4 12h16M4 18h16"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
          />
        </svg>
      </div>
    </button>
  </div>
  <div
    class="block fixed top-0 left-0 w-[20.5rem] h-full bg-white borber-r border-gray-100 lg:hidden z-50 duration-200 transition-all ease-out -translate-x-full"
  >
    <section
      class="flex w-full h-12 p-4 justify-between"
    >
      <a
        href="seu_link_aqui"
      >
        <img
          src=""
        />
      </a>
      <button
        class="flex w-10 h-10 justify-center items-center lg:hidden"
      >
        <div
          class="w-6 h-6"
        >
          <svg
            class="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              d="M4 6h16M4 12h16M4 18h16"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
            />
          </svg>
        </div>
      </button>
    </section>
  </div>
</nav>
`;

exports[`Navbar renders Navbar component responsively 1`] = `
<nav
  class="flex whitespace-nowrap justify-between bg-[#FFFFFF] w-full h-14 items-center border-b border-gray-100 py-2 px-4 relative z-10"
  data-testid="TestID"
>
  <div
    class="fixed w-full h-full top-0 left-0 z-20 bg-black-900 opacity-0 block pointer-events-none animate-delayedDisplayNone transition-all duration-300"
  />
  <div
    class="h-7"
  >
    <a
      href="/"
    >
      <img
        class="hidden lg:block h-full w-auto"
        src=""
      />
    </a>
    <a
      class="absolute m-9"
      href="/"
    >
      <img
        class="block lg:hidden"
        src=""
      />
    </a>
  </div>
  <div
    class="flex justify-end items-center gap-4 md:gap-8"
  >
    <a
      class="hidden lg:block w-full py-3 px-4 rounded-md font-outfit font-medium text-xs text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed"
      href="/"
    >
      Entrar
    </a>
    <a
      class="hidden lg:block w-full py-3 px-4 rounded-md font-outfit font-medium text-xs text-black-900 tracking-[0.0625rem] duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed bg-yellow-800 hover:bg-yellow-900 hover:text-white"
      href="/"
    >
      Criar Conta
    </a>
    <button
      class="flex w-10 h-10 justify-center items-center lg:hidden"
    >
      <div
        class="w-6 h-6"
      >
        <svg
          class="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            d="M4 6h16M4 12h16M4 18h16"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
          />
        </svg>
      </div>
    </button>
  </div>
  <div
    class="block fixed top-0 left-0 w-[20.5rem] h-full bg-white borber-r border-gray-100 lg:hidden z-50 duration-200 transition-all ease-out -translate-x-full"
  >
    <section
      class="flex w-full h-12 p-4 justify-between"
    >
      <a
        href="seu_link_aqui"
      >
        <img
          src=""
        />
      </a>
      <button
        class="flex w-10 h-10 justify-center items-center lg:hidden"
      >
        <div
          class="w-6 h-6"
        >
          <svg
            class="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              d="M4 6h16M4 12h16M4 18h16"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
            />
          </svg>
        </div>
      </button>
    </section>
  </div>
</nav>
`;
