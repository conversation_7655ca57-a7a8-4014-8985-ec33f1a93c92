import { render, screen } from "@tests/render";
import LoggedInNavbar from "./LoggedInNavbar";

describe("Navbar", () => {
  it("renders Navbar component correctly", async () => {
    render(<LoggedInNavbar />);
    const navbar = await screen.findByTestId("loggedlnNavbar");
    expect(navbar).toMatchSnapshot();
  });

  it("renders Navbar component responsively", async () => {
    render(<LoggedInNavbar />);
    window.innerWidth = 320;
    window.innerHeight = 568;
    window.dispatchEvent(new Event("resize"));
    const navbar = await screen.findByTestId("loggedlnNavbar");
    expect(navbar).toMatchSnapshot();
  });
});
