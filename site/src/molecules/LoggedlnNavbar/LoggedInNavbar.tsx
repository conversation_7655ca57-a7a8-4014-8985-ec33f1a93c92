import React, { HTMLAttributes, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Image } from "../../components";
import { tv } from "tailwind-variants";
import logoMob from "@/assets/logo-mob.svg";
import {
  Bars3Icon,
  BellIcon,
  MagnifyingGlassIcon,
  TicketIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { Sidebar } from "@/components/Sidebar";

export interface LoggedInNavbarProps extends HTMLAttributes<HTMLDivElement> {
  hasLogo?: boolean;
}

const style = tv({
  slots: {
    menuOpen:
      "block fixed top-0 left-0 w-64 h-full bg-white border-r lg:hidden z-50 duration-200 transition-all ease-out translate-x-0",
    menuClosed:
      "block fixed top-0 left-0 w-[20.5rem] h-full bg-white borber-r border-gray-100 lg:hidden z-50 duration-200 transition-all ease-out -translate-x-full",
    overlayOpen:
      "lg:hidden fixed w-full h-full top-0 left-0 z-20 bg-black-900 opacity-70 block transition-all duration-300",
    overlayClosed:
      "fixed w-full h-full top-0 left-0 z-20 bg-black-900 opacity-0 block pointer-events-none animate-delayedDisplayNone transition-all duration-300",
  },
});

const { menuOpen, menuClosed, overlayOpen, overlayClosed } = style();

const LoggedInNavbar: React.FC<LoggedInNavbarProps> = ({
  hasLogo,
  ...params
}: LoggedInNavbarProps) => {
  const [isNavOpen, setIsNavOpen] = useState(false);

  const handleToggleMenu = () => {
    setIsNavOpen(!isNavOpen);
  };

  const handleCloseMenu = () => {
    setIsNavOpen(false);
  };

  return (
    <nav
      data-testid="loggedlnNavbar"
      {...params}
      className="flex justify-between items-center bg-white w-full h-14 py-2 px-4 border-b border-gray-100 z-10 grid-area-header"
    >
      <div className={isNavOpen ? overlayOpen() : overlayClosed()}></div>

      <div className="flex justify-between items-center gap-8">
        {hasLogo && (
          <>
            <Link to="/" className="h-12">
              <Image src={logoMob} className="block lg:hidden" />
            </Link>
          </>
        )}

        <div className="hidden lg:block">breadcrumbs here</div>
      </div>

      <div className="hidden lg:block">search here</div>

      <div className="flex justify-between items-center gap-8">
        <Link
          to="/"
          className="w-auto h-10 py-3 px-4 rounded-md font-outfit text-xs text-black-900 tracking-[0.0625rem] bg-blue-100 hover:bg-yellow-900 hover:text-white duration-300 disabled:bg-gray-700 disabled:text-white disabled:cursor-not-allowed hidden sm:flex items-center truncate"
        >
          <TicketIcon className="mr-2 w-6 h-6" />
          <span className="hidden lg:inline">10 Créditos</span>
          <span className="lg:hidden">10</span>
        </Link>
        <button role="button" className="lg:hidden p-4">
          <MagnifyingGlassIcon className="w-6 h-6" />
        </button>
        <button
          id="dropdownMenuButton1"
          role="button"
          className="w-10 h-10 flex justify-center items-center"
        >
          <BellIcon className="w-6 h-6" />
          <span className="absolute -mt-9 ml-3.5 rounded-full bg-danger px-[0.45em] py-[0.15em] text-[0.6rem] font-bold leading-none text-white">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="8"
              height="8"
              viewBox="0 0 8 8"
              fill="none"
            >
              <circle cx="4" cy="4" r="4" fill="#D63649" />
            </svg>
          </span>
        </button>
        <button role="button" className="hidden lg:block">
          <img
            src="https://tecdn.b-cdn.net/img/new/avatars/2.jpg"
            className="rounded-full w-8 h-8"
          />
        </button>
        <button
          onClick={handleToggleMenu}
          className="flex w-10 h-10 justify-center items-center lg:hidden"
        >
          <Bars3Icon className="w-6 h-6" />
        </button>
      </div>

      <div
        onClick={handleCloseMenu}
        className={isNavOpen ? menuOpen() : menuClosed()}
      >
        <section className="relative flex flex-col">
          <button
            onClick={handleToggleMenu}
            className="flex w-10 h-10 justify-center items-center lg:hidden absolute right-0 top-5 z-20 hover:rotate-90 transition-all duration-900"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
          <Sidebar />
        </section>
      </div>
    </nav>
  );
};

export default LoggedInNavbar;
