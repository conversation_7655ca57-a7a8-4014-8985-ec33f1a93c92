import axios from "axios";

const { VITE_API_URL } = import.meta.env;

/**
 
call the API*
@param query
@returns data
*/
export default async function callApi<T>(
  query: string,
  variables: unknown,
): Promise<T> {
  console.log(import.meta.env, "Calling API", VITE_API_URL, query, variables);
  const response = await axios.post(VITE_API_URL, {
    query,
    variables,
  });

  if (response.data.errors) {
    throw new Error(response.data.errors[0].message);
  }

  console.log("API Response:", response.data);

  return response.data.data as T;
}
