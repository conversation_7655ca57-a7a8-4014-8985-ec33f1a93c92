# Conteoodo

Production: [https://conteoodo.com](https://conteoodo.com)

## Development

Install all dependencies:

```sh
yarn install
```

Setup your VS Code to auto format with eslint/prettier:

Create a file `.vscode/settings.json`:

```js
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
```

Install also [vscode-tailwindcss](vscode:extension/bradlc.vscode-tailwindcss)

Run the server:

```sh
cp env.example .env.example
yarn dev
```

Run tests:

```sh
yarn test
```

How to commit:

```sh
git add .
git commit
```

## Release a version:

```sh
yarn release
```
