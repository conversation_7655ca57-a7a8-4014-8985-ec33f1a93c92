name: Test

on:
  push:

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: <PERSON>de
        uses: actions/setup-node@v3
        with:
          node-version: "18"
          cache: "yarn"
          cache-dependency-path: "**/yarn.lock"

      - name: Install
        run: yarn install --frozen-lockfile

      - name: Test
        run: yarn tsc

      - name: Lint
        run: yarn lint

      - name: Prettier
        run: yarn prettier

      - name: Test
        run: yarn test
