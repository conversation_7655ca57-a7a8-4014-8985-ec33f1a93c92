name: Publish PR Storybook

on:
  workflow_dispatch:

jobs:
  storybook:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup AWS CLI
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: sa-east-1

      - name: <PERSON><PERSON>
        uses: actions/setup-node@v3
        with:
          node-version: "18"
          cache: "yarn"
          cache-dependency-path: "**/yarn.lock"

      - name: Install
        run: yarn install --frozen-lockfile

      - name: Set env.BRANCH
        run: echo "BRANCH=$(echo $GITHUB_REF | cut -d'/' -f 3)" >> $GITHUB_ENV

      - name: Get Pull Request Number
        run: echo "PR_NUMBER=$(gh pr view --json number -q .number || echo "")" >> $GITHUB_ENV
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Sync files to S3 bucket
        run: |
          yarn build-storybook
          aws s3 sync storybook-static/ s3://sb.conteoodo.com/branches/${{env.BRANCH}}/
          aws s3 sync locales/ s3://sb.conteoodo.com/locales/

      - name: Find Comment
        uses: peter-evans/find-comment@v2
        id: fc
        with:
          issue-number: ${{ env.PR_NUMBER }}
          comment-author: "github-actions[bot]"
          body-includes: Storybook URL

      - name: Create or update comment
        uses: peter-evans/create-or-update-comment@v3
        with:
          comment-id: ${{ steps.fc.outputs.comment-id }}
          issue-number: ${{ env.PR_NUMBER }}
          body: |
            Storybook URL: http://sb.conteoodo.com.s3-website-sa-east-1.amazonaws.com/branches/${{ env.BRANCH }}/
          edit-mode: replace
