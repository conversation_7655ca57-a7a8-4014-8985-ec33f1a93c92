name: Publish Main Storybook

on:
  push:
    branches:
      - main

jobs:
  storybook:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup AWS CLI
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: sa-east-1

      - name: <PERSON><PERSON>
        uses: actions/setup-node@v3
        with:
          node-version: "18"
          cache: "yarn"
          cache-dependency-path: "**/yarn.lock"

      - name: Install
        run: yarn install --frozen-lockfile

      - name: Sync files to S3 bucket
        run: |
          yarn build-storybook
          aws s3 sync storybook-static/ s3://sb.conteoodo.com/
          aws s3 sync locales/ s3://sb.conteoodo.com/locales/
