{"jsc": {"target": "es2017", "parser": {"syntax": "typescript", "tsx": true, "decorators": false, "dynamicImport": false}, "transform": {"react": {"pragma": "React.createElement", "pragmaFrag": "React.Fragment", "throwIfNamespace": true, "development": false, "useBuiltins": false, "runtime": "automatic"}, "hidden": {"jest": true}}}, "module": {"type": "commonjs", "strict": false, "strictMode": true, "lazy": false, "noInterop": false}}