/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./node_modules/tailwind-datepicker-react/dist/**/*.js",
    "node_modules/flowbite-react/lib/esm/**/*.js",
  ],
  theme: {
    extend: {
      fontFamily: {
        outfit: ["Outfit", "sans"],
        rubik: ["Rubik", "sans"],
      },
      gridTemplateColumns: {
        mainLayout: "18rem auto",
        secondaryLayout: "18rem auto 16rem",
      },
      colors: {
        yellow: {
          800: "#FFCC33",
          900: "#CC8800",
        },
        blue: {
          100: "#D8E0F0F2",
          500: "#2F80ED",
        },
        black: {
          800: "#3a3946",
          900: "#000117",
          600: "#1F2937",
        },
        gray: {
          100: "#f4f4f2",
          200: "#fafaf8",
          300: "#ddd9d3",
          600: "#d9d9d9",
          700: "#DDD9D3F2",
          800: "#8B8B88",
        },
        amber: {
          100: "#fff5da",
          200: "#fcf7d9",
          300: "#FFF6DB",
        },
      },
      keyframes: {
        slideDown: {
          "0%, 50%": { transform: "translateY(-100%)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
        delayedDisplayNone: {
          "0%": { display: "inherit", opacity: ".35" },
          "100%": { display: "none", opacity: "0" },
        },
      },
      animation: {
        slideDown: "slideDown .25s ease-out forwards",
        delayedDisplayNone: "delayedDisplayNone 1s 2s ease-out forwards",
      },
    },
  },
  plugins: [require("flowbite/plugin")],
};
