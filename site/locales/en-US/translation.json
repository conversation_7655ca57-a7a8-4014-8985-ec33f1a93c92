{"home": {"buttons": {"sign-up": "Sign up", "login": "<PERSON><PERSON>", "generate-posts": "Generate Posts", "test-free": "Try Now", "free-test": "Try Now"}, "title": "Create your content campaign now!", "description": "Enter the activity area, campaign objectivity, campaing period and click \"Generate Posts\".", "label": {"area": "Á<PERSON>", "objective": "Objetivo", "period": "<PERSON><PERSON><PERSON>", "preposition": "to"}, "datepicker-language": "US", "placeholders": {"area": "e.g. Personal Trainer", "objective": "Get new clients", "date": "Select a date"}, "how-works": {"title": "Quality content in a few clicks, generated by <1>Artificial Intelligence!</1>", "mini-title-1": "Timeline", "mini-title-2": "Stories", "mini-description": "Generate multiple posts for social networks in a few clicks, including texts and images."}, "campaigns": {"title-1": "A digital content agency in the palm of your hand!", "description-1": "Create full marketing campaigns with Artificial Inteligence generated text and images.", "title-2": "Your personalized content for your brand!", "description-2": "Create full campaigns in a few clicks and schedule the publication on your social networks!"}, "custom-content": {"title-1": "<1> Unique </1>content for unique clients!", "description": "Check out some content generated on our platform by real customers!", "title-2": "Your personalized content for your brand!", "cards": {"title-1": "Your brand on every post", "description-1": "Your logo is added on every AI-generated posts.", "title-2": "Contact information", "description-2": "Choose phone, email or WhatsApp to be scheduled in the post.", "title-3": "Call to action text", "description-3": "Call to action texts based on your campaign goal.", "title-4": "Custom Colors", "description-4": "Colors of your visual identity applied to your posts automatically"}}, "multitool": {"title": "Enough using multiple AI tools to make content marketing. Here you have everything!", "description": "With just a few informations, our powerful Artificial Intelligence API will take care of everything else for you.", "items": {"title-1": "AI-generated images and texts in a few clicks", "description-1": "Generate multiple posts for social networks in a few clicks, including texts and images.", "title-2": "Create complete marketing campaigns instead of isolated posts", "description-2": "Generate multiple posts for social networks in a few clicks, including texts and images.", "title-3": "Automatically scheduled posts", "description-3": "Define which social networks the campaign posts will be published on. Edit or change the text or image of the post at any time, up until the publication date.", "title-4": "Track the performance of your campaigns", "description-4": "View data graphs and metrics from your campaigns."}}, "examples": {"title": "Quality content in a few clicks, generated by <1>Artificial Intelligence</1>!", "punchline": "Start now to create your posts with Artificial Intelligence"}, "questions": {"title": "Frequently Asked Questions", "description": "If you don't find the answer to your question in the FAQ, you can check out our <1> support </1> section or send your question to <1> <EMAIL></1>.", "question-1": "Is the content repeated?", "answer-1": "Lorem ipsum dolor sit amet consectetur. Maecenas eget sed id quisque sed consectetur pellentesque arcu. Tortor vulputate pellentesque sit id nisi pellentesque integer dolor malesuada. Ac aliquam a vitae lacus elit. Suspendisse in maecenas ultrices ut ante. Interdum sed lorem urna tortor morbi. Arcu volutpat faucibus dignissim at in adipiscing.", "question-2": "Is it copyright free?", "answer-2": "Lorem ipsum dolor sit amet consectetur. Maecenas eget sed id quisque sed consectetur pellentesque arcu. Tortor vulputate pellentesque sit id nisi pellentesque integer dolor malesuada. Ac aliquam a vitae lacus elit. Suspendisse in maecenas ultrices ut ante. Interdum sed lorem urna tortor morbi. Arcu volutpat faucibus dignissim at in adipiscing.", "question-3": "How many posts can I publish per day?", "answer-3": "Lorem ipsum dolor sit amet consectetur. Maecenas eget sed id quisque sed consectetur pellentesque arcu. Tortor vulputate pellentesque sit id nisi pellentesque integer dolor malesuada. Ac aliquam a vitae lacus elit. Suspendisse in maecenas ultrices ut ante. Interdum sed lorem urna tortor morbi. Arcu volutpat faucibus dignissim at in adipiscing.", "question-4": "Can I change the Post text?", "answer-4": "Lorem ipsum dolor sit amet consectetur. Maecenas eget sed id quisque sed consectetur pellentesque arcu. Tortor vulputate pellentesque sit id nisi pellentesque integer dolor malesuada. Ac aliquam a vitae lacus elit. Suspendisse in maecenas ultrices ut ante. Interdum sed lorem urna tortor morbi. Arcu volutpat faucibus dignissim at in adipiscing.", "question-5": "Can I change the Post image?", "answer-5": "Lorem ipsum dolor sit amet consectetur. Maecenas eget sed id quisque sed consectetur pellentesque arcu. Tortor vulputate pellentesque sit id nisi pellentesque integer dolor malesuada. Ac aliquam a vitae lacus elit. Suspendisse in maecenas ultrices ut ante. Interdum sed lorem urna tortor morbi. Arcu volutpat faucibus dignissim at in adipiscing."}}, "sign-up": {"title": "Create an account", "description": "Create your account to complete the setup of your campaign and start using all available AI tools!", "label": "Do you want to create your account with:", "google": "Google", "email": "Email"}, "sign-up-with-email": {"title": "Register", "description": "Let's create your account start publish some conteoodos!", "continue": "Continue", "password-title": "Password", "password-hint": "6 characters Minimum. Use letters, numbers and symbols to have a more secure password.", "form": {"labels": {"name": "Name", "email": "Email", "password": "Password", "confirm-password": "Repeat Password"}, "weak-password-text": "The inserted password is too weak. <br>Try to include symbols, numbers and switch between upper and lower case letters for a more secure password.", "placeholders": {"name": "Your name", "email": "Your email"}, "errors": {"required": "Required field", "invalid-email": "Invalid email", "invalid-password": "Weak password", "password-short": "Password too short", "password-not-match": "Passwords not match"}}}, "confirm-account": {"title": "Confirmation Code", "description": "Please, insert the confirmation code you received on your email:", "form": {"labels": {"code": "Código"}, "placeholders": {"code": "<PERSON><PERSON> có<PERSON>"}, "errors": {"required": "Required field"}}, "buttons": {"send": "Send"}}, "sign-in": {"title": "Sign In", "buttons": {"email": "Email", "google": "Google"}}, "sign-in-with-email": {"title": "Sign In", "form": {"labels": {"email": "Email", "password": "Password"}, "errors": {"required": "Required field", "invalid-email": "Invalid email"}, "placeholders": {"email": "Your e-mail", "password": "Your password"}}, "continue": "Continue", "reset-password": "Reset Password"}, "reset-password": {"title": "Reset Password", "description": "Please enter your email below. If there is an account with this email, we will send you a link to reset your password.", "form": {"label": "Email", "placeholders": {"email": "Your e-mail"}, "send": "Send", "errors": {"required": "Required field", "invalid-email": "Invalid email"}}, "phase-2": {"title": "<PERSON>", "description": "A password reset link has been sent to your email. Follow the instructions in the email to reset your password."}, "continue": "Continue"}, "set-new-password": {"title": "Reset Password", "description": "6 characters Minimum. Use letters, numbers and symbols to have a more secure password.", "form": {"labels": {"code": "Code", "password": "Password", "repeat-password": "Repeat Password"}, "continue": "Continue", "placeholders": {"code": "Code sent to your Email"}, "weak-password-text": "The inserted password is too weak. <br>Try to include symbols, numbers and switch between upper and lower case letters for a more secure password.", "errors": {"required": "Required field", "invalid-password": "Weak password", "password-not-match": "Passwords not match", "password-short": "Password too short"}}, "phase-2": {"title": "Everything Ready!", "description": "Your password was changed. Sign in right now!", "goSignIn": "Go to Sign in"}}, "checkout": {"language": "en-US", "title": "Plans", "erros": {"min-added-credits": "Min 100 Credits", "type": "Must be a Integer number"}, "plans": {"recomended": "Recomended", "sign": "Sign", "currency": "$", "per-month": "/month", "card-1": {"title": "Basic", "description": "For those who are an influencer, independent professional, or have a small business.", "credits": "credits", "price": "89.90"}, "card-2": {"title": "Plus", "description": "Recommended for freelancers or small advertising and marketing agencies.", "credits": "credits", "price": "224.90"}, "card-3": {"title": "Advanced", "description": "Recommended for large teams or multiple teams within an agency.", "credits": "credits", "price": "349.90"}, "card-4": {"title": "Custom", "description": "Define the amount of credits you need.", "credits": "credits", "price": "."}}, "questions": {"title": "Frequently Asked Questions", "description": "If you don't find the answer to your question in the FAQ, you can check out our <1> support </1> section or send your question to <1> <EMAIL></1>.", "question-1": "How much credits do I need?", "answer-1": "Lorem ipsum dolor sit amet consectetur. Maecenas eget sed id quisque sed consectetur pellentesque arcu. Tortor vulputate pellentesque sit id nisi pellentesque integer dolor malesuada. Ac aliquam a vitae lacus elit. Suspendisse in maecenas ultrices ut ante. Interdum sed lorem urna tortor morbi. Arcu volutpat faucibus dignissim at in adipiscing.", "question-2": "1 credit is equivalent to 1 post?", "answer-2": "Lorem ipsum dolor sit amet consectetur. Maecenas eget sed id quisque sed consectetur pellentesque arcu. Tortor vulputate pellentesque sit id nisi pellentesque integer dolor malesuada. Ac aliquam a vitae lacus elit. Suspendisse in maecenas ultrices ut ante. Interdum sed lorem urna tortor morbi. Arcu volutpat faucibus dignissim at in adipiscing.", "question-3": "Do credits expire?", "answer-3": "Lorem ipsum dolor sit amet consectetur. Maecenas eget sed id quisque sed consectetur pellentesque arcu. Tortor vulputate pellentesque sit id nisi pellentesque integer dolor malesuada. Ac aliquam a vitae lacus elit. Suspendisse in maecenas ultrices ut ante. Interdum sed lorem urna tortor morbi. Arcu volutpat faucibus dignissim at in adipiscing.", "question-4": "What happens if I haven't enough credits to create a campaign?", "answer-4": "Lorem ipsum dolor sit amet consectetur. Maecenas eget sed id quisque sed consectetur pellentesque arcu. Tortor vulputate pellentesque sit id nisi pellentesque integer dolor malesuada. Ac aliquam a vitae lacus elit. Suspendisse in maecenas ultrices ut ante. Interdum sed lorem urna tortor morbi. Arcu volutpat faucibus dignissim at in adipiscing."}}, "clientsaccounts": {"title": "Client Accounts", "button": "New Client"}, "campaign-list": {"title": "Campaigns", "new-campaign": "New Campaign", "filter": {"labels": {"campaign": "Campaign", "client": "Client", "status": "Status", "social-network": "Social Network"}, "placeholders": {"campaign": "Campaign", "client": "Client", "status": "Status", "social-network": "Social Network"}, "button": "Filter"}, "cards": {"status": {"finished": "Finished"}, "preposition": "to"}}, "createclient": {"button": "Client Accounts", "modalcreateclient": {"hadear": "Create Client Account", "body": {"text1": "Account Name", "text2": "Team", "text3": "Team Member Permissions", "options": {"permission1": "View Only", "permission2": "View and Edit"}}, "fotter": {"botton1": "Cancel", "botton2": "Create Account"}}, "components": {"text": "Add your Logo", "modalcreatelogo": {"hadear": "Add Logo", "body": {"text1": "Account Name", "text2": "Send Image"}, "fotter": {"botton1": "<PERSON><PERSON><PERSON>", "botton2": "Add"}}, "modalcreatephoto": {"hadear": "Add Photo", "body": {"text1": "Select a folder", "text2": "Send image"}, "fotter": {"button1": "Cancel", "button2": "Add"}}, "modalcreatefonts": {"hadear": "Add Fonts", "body": {"text1": "Display Font", "text2": "Weight", "text3": "Body Font", "text4": "Weight"}, "fotter": {"button1": "Cancel", "button2": "Add"}}, "modalcreatecolors": {"hadear": "Add Colors", "body": {"text1": "Name", "text2": "Primary Color", "text3": "Secondary Color", "text4": "Tertiary Color", "text5": "Text Color (Dark)", "text6": "Text Color (Light)"}, "fotter": {"button1": "Cancel", "button2": "Add"}}, "modalhome": {"hadear": "Early Access", "hadear2": "Sign up for early access to the Conteoodo platform.", "hadear3": "Registration Complete!", "body": {"text1": "First Name", "text2": "Last Name", "text3": "Email", "text4": "You will receive an email with instructions to join the platform as soon as it is available, Thanks!"}, "fotter": {"button1": "Cancel", "button2": "Register", "botton3": "Close"}}, "menutags": {"tag1": "Logo", "tag2": "Colors", "tag3": "Fonts", "tag4": "Photos"}, "card": {"botton1": "Edit", "botton2": "Delete"}}}, "create-campaign": {"title": "New Camapaign", "generate-post": "Generate Posts", "form": {"labels": {"area": "Area", "objective": "Objective", "start-date": "Start", "end-date": "End", "posts-per-day": "Posts per day", "post-hours": "Post Hour ", "week-days": "Week Days"}, "blocks-titles": {"campaign-data": "Campaign Data", "format": "Format", "publish-in": "Publish In", "style": "Style"}, "week-days": {"Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Saturday": "Saturday", "Sunday": "Sunday"}, "errors": {"required": "Required field", "required-posts-per-day": "Select posts per day amount", "required-hours": "Fill all posts hours fields", "required-week-days": "Select week days", "required-format": "Select a format", "required-social-network": "Select at least one social network", "required-style": "Select a style"}, "styles": {"realistic": "Realistic", "abstract": "Abstract", "aquarelle": "<PERSON><PERSON><PERSON><PERSON>", "sciFi": "SciFi", "fantasy": "Fantasy", "3d": "3d", "painting": "Painting", "portrait": "Portrait"}, "placeholders": {"area": "Personal Trainer", "objective": "Get more trainees"}}, "modal": {"cancel": "Cancel", "title": "Review your campaign", "credits": "Credit Balance:", "client": "Client", "total-posts": "Total Posts", "used-credits": "Used Credits"}, "finished": {"title": "Wonderfull! Your campaign is being created!", "description": "We are generating the <1>{{amountPosts}} posts</1> of your campaign. You can continue navigating or close this tab. We'll send a notification when your campaign is ready.", "generating-posts": "Generating Posts"}}, "components": {"drag-drop": {"input": "Choose file", "p1": "Drop your file here or click to select", "p2": "more files...", "p3": "File name...", "div": "Loading...", "less": "Show less"}, "PasswordStrength": {"label": "Password Strength", "weak": "Weak", "medium": "Medium", "strong": "Strong", "very-strong": "Very Strong"}, "Sidebar": {"labels": {"home": "Home", "megaphone": "Campaigns", "calendarDays": "Post Schedules", "buildingStorefront": "Customer Contacts", "users": "Teams", "presentationChartBar": "Performance", "user": "My Account", "lockClose": "Login and Password", "creditCar": "Payment", "cog6Tooth": "Settings"}, "text": "New Campaigns", "title": "Create account", "login": "Sign in"}, "CampaignInfoSidebar": {"labels": {"text": "New Campaigns", "heading": "Campaigns", "data1": "Start", "date2": "End", "images": "Logo", "color1": "Primary Color", "color2": "Secondary Color", "posts1": "Publish 01 Free Post", "posts2": "Publish 90 Posts", "datepicker": {"placeholders": "Select Data"}}, "Calendar": {"week": {"0": "Dom", "1": "Mon", "2": "Have", "3": "Wed", "4": "<PERSON>hu", "5": "Sex", "6": "<PERSON>b"}, "month": {"0": "January", "1": "February", "2": "March", "3": "April", "4": "May", "5": "June", "6": "July", "7": "August", "8": "September", "9": "October", "10": "November", "11": "December"}}}, "DatePicker": {"language": "US"}, "DropDown": {"placeholder": "Select..."}, "NavBar": {"login": "<PERSON><PERSON>", "signUp": "Sign Up"}}}