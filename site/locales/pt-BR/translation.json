{"home": {"buttons": {"sign-up": "<PERSON><PERSON><PERSON>", "login": "Entrar", "generate-posts": "Gerar <PERSON>s", "test-free": "<PERSON><PERSON>", "free-test": "<PERSON><PERSON>"}, "title": "Crie sua campanha de conteúdo agora!", "description": "Insira a área de atuação, objetivo da campanha, o período e clique em \"Gerar Posts\".", "label": {"area": "Á<PERSON>", "objective": "Objetivo", "period": "<PERSON><PERSON><PERSON>", "preposition": "à"}, "datepicker-language": "BR", "placeholders": {"area": "Ex: Personal Trainer", "objective": "Conseguir novos alunos", "date": "Selecione um dia"}, "how-works": {"title": "Conteúdo de qualidade em poucos cliques, gerados por <1>Inteligência Artificia!</1>", "mini-title-1": "Timeline", "mini-title-2": "Stories", "mini-description": "<PERSON><PERSON> múl<PERSON>los posts para redes sociais em poucos cliques, incluindo textos e imagens."}, "campaigns": {"title-1": "Uma agência de conteúdo digital na palma da sua mão!", "description-1": "Crie campanhas de marketing completas com texto e imagens geradas por inteligência artificial.", "title-2": "Crie e publique conteúdos Automagicamente!", "description-2": "Crie campanhas completas em poucos cliques e agende a publicação nas suas redes sociais!"}, "custom-content": {"title-1": "Conteúdos<1> únicos </1>para clientes únicos!", "description": "Confira alguns conteúdos gerados em nossa plataforma por clientes reais!", "title-2": "Seus conteúdo personalizado para sua marca!", "cards": {"title-1": "Sua marca em todos os posts", "description-1": "Seu logo é adicionado em todos os posts gerados pela IA.", "title-2": "Informações de Contato", "description-2": "Escolha telefone, email ou whatsApp para serem mostrados no post.", "title-3": "Texto de chamada de ação", "description-3": "Textos de chamada baseados no seu objetivo de campanha.", "title-4": "<PERSON>s Personalizadas", "description-4": "Cores da sua identidade visual aplicadas aos seus posts automaticamente"}}, "multitool": {"title": "Chega de usar múltiplas ferramentas de IA para fazer marketing de conteúdo. Aqui você tem tudo!", "description": "Com apenas algumas informações nossa poderosa API de Inteligência Artificial cuidará de todo o resto para você.", "items": {"title-1": "Imagens e Textos gerados por IA em poucos cliques", "description-1": "<PERSON><PERSON> múl<PERSON>los posts para redes sociais em poucos cliques, incluindo textos e imagens.", "title-2": "Crie Campanhas de marketing completas ao invés de posts isolados", "description-2": "<PERSON><PERSON> múl<PERSON>los posts para redes sociais em poucos cliques, incluindo textos e imagens.", "title-3": "Publicações agendadas automaticamente", "description-3": "Defina em quais redes sociais os posts da campanha serão publicados. Edite ou altere o texto ou imagem do post a qualquer momento, até a data da publicação.", "title-4": "Acompanhe o desempenho de sua campanhas", "description-4": "Visualize gráficos de dados e métricas de suas campanhas."}}, "examples": {"title": "Conteúdo de qualidade em poucos cliques, gerados por <1>Inteligência Artificia</1>!", "punchline": "Comece agora a criar seus posts com Inteligência Artificial!"}, "questions": {"title": "Perguntas Frequentes", "description": "Caso não encontre a resposta para sua dúvida nas perguntas frequentes, você pode conferir a nossa sessão de<1> suporte </1>ou enviar sua dúvida para<1> <EMAIL></1>.", "question-1": "O conteúdo é repetido?", "answer-1": "Lorem ipsum dolor sit amet consectetur. Maecenas eget sed id quisque sed consectetur pellentesque arcu. Tortor vulputate pellentesque sit id nisi pellentesque integer dolor malesuada. Ac aliquam a vitae lacus elit. Suspendisse in maecenas ultrices ut ante. Interdum sed lorem urna tortor morbi. Arcu volutpat faucibus dignissim at in adipiscing.", "question-2": "É livre de direitos autorais?", "answer-2": "Lorem ipsum dolor sit amet consectetur. Maecenas eget sed id quisque sed consectetur pellentesque arcu. Tortor vulputate pellentesque sit id nisi pellentesque integer dolor malesuada. Ac aliquam a vitae lacus elit. Suspendisse in maecenas ultrices ut ante. Interdum sed lorem urna tortor morbi. Arcu volutpat faucibus dignissim at in adipiscing.", "question-3": "Quantos posts posso publicar por dia?", "answer-3": "Lorem ipsum dolor sit amet consectetur. Maecenas eget sed id quisque sed consectetur pellentesque arcu. Tortor vulputate pellentesque sit id nisi pellentesque integer dolor malesuada. Ac aliquam a vitae lacus elit. Suspendisse in maecenas ultrices ut ante. Interdum sed lorem urna tortor morbi. Arcu volutpat faucibus dignissim at in adipiscing.", "question-4": "Posso alterar o texto do Post?", "answer-4": "Lorem ipsum dolor sit amet consectetur. Maecenas eget sed id quisque sed consectetur pellentesque arcu. Tortor vulputate pellentesque sit id nisi pellentesque integer dolor malesuada. Ac aliquam a vitae lacus elit. Suspendisse in maecenas ultrices ut ante. Interdum sed lorem urna tortor morbi. Arcu volutpat faucibus dignissim at in adipiscing.", "question-5": "Posso alterar a imagem do Post?", "answer-5": "Lorem ipsum dolor sit amet consectetur. Maecenas eget sed id quisque sed consectetur pellentesque arcu. Tortor vulputate pellentesque sit id nisi pellentesque integer dolor malesuada. Ac aliquam a vitae lacus elit. Suspendisse in maecenas ultrices ut ante. Interdum sed lorem urna tortor morbi. Arcu volutpat faucibus dignissim at in adipiscing."}}, "sign-up": {"title": "C<PERSON><PERSON> conta", "description": "Crie sua conta para finalizar a criação de sua campanha e poder utilizar todas as ferramentas de Inteligência Artificial disponíveis!", "label": "Deseja criar sua conta com:", "google": "Google", "email": "Email"}, "sign-up-with-email": {"title": "Cadastro", "description": "Bora criar sua conta e começar a publicar conteoodos!", "continue": "<PERSON><PERSON><PERSON><PERSON>", "password-title": "<PERSON><PERSON>", "password-hint": "<PERSON><PERSON><PERSON> de 6 caracteres. Use letras, números e símbolos para ter uma senha mais segura.", "form": {"labels": {"name": "Nome", "email": "Email", "password": "<PERSON><PERSON>", "confirm-password": "<PERSON><PERSON><PERSON>"}, "weak-password-text": "A senha inserida é muito fraca. <br>Tente incluir sí<PERSON>, números e alternar entre letras maiúsculas e minúsculas para uma senha mais segura.", "placeholders": {"name": "Seu nome", "email": "Seu email"}, "errors": {"required": "Preencha este campo", "invalid-email": "Preencha com email válido", "invalid-password": "Senha muito fraca", "password-short": "Senha curta demais", "password-not-match": "<PERSON><PERSON> n<PERSON>m"}}}, "confirm-account": {"title": "Código de Confirmação", "description": "Por favor insira o código enviado para seu endereço de email para confirmar sua conta:", "form": {"labels": {"code": "Código"}, "placeholders": {"code": "<PERSON><PERSON> có<PERSON>"}, "errors": {"required": "Preencha este campo"}}, "buttons": {"send": "Enviar"}}, "sign-in": {"title": "Entrar", "buttons": {"email": "Email", "google": "Google"}}, "sign-in-with-email": {"title": "Entrar", "form": {"labels": {"email": "Email", "password": "<PERSON><PERSON>"}, "errors": {"required": "Preencha este campo", "invalid-email": "Preencha com email válido"}, "placeholders": {"email": "Seu e-mail", "password": "<PERSON><PERSON> se<PERSON>a"}}, "continue": "<PERSON><PERSON><PERSON><PERSON>", "reset-password": "<PERSON><PERSON><PERSON><PERSON>"}, "reset-password": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Por favor insira seu email abaixo. Caso exista uma conta com este email, lhe enviaremos um link para redefinição de senha.", "form": {"label": "Email", "placeholders": {"email": "Seu e-mail"}, "send": "Enviar", "errors": {"required": "Preencha este campo", "invalid-email": "Preencha com email válido"}}, "phase-2": {"title": "Link <PERSON>", "description": "Um link para redefinição de senha foi enviado para seu email. Siga as instruções no email para redefinir sua senha."}, "continue": "<PERSON><PERSON><PERSON><PERSON>"}, "set-new-password": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> de 6 caracteres. Use letras, números e símbolos para ter uma senha mais segura.", "form": {"labels": {"code": "Código", "password": "<PERSON><PERSON>", "repeat-password": "<PERSON><PERSON><PERSON>"}, "continue": "<PERSON><PERSON><PERSON><PERSON>", "placeholders": {"code": "<PERSON>ó<PERSON> recebido no Email"}, "weak-password-text": "A senha inserida é muito fraca.<br>Tente incluir sí<PERSON>, números e alternar entre letras maiúsculas e minúsculas para uma senha mais segura.", "errors": {"required": "Preencha este campo", "invalid-password": "Senha muito fraca", "password-not-match": "<PERSON><PERSON> n<PERSON>m", "password-short": "Senha curta demais"}}, "phase-2": {"title": "Tudo <PERSON>!", "description": "Sua senha foi redefinida. Faça Login agora mesmo!", "goSignIn": "<PERSON><PERSON>"}}, "checkout": {"language": "pt-PT", "title": "Planos", "erros": {"min-added-credits": "Mínimo 100 Créditos", "type": "Digite um número inteiro"}, "plans": {"recomended": "Recomendado", "sign": "<PERSON><PERSON><PERSON>", "currency": "R$", "per-month": "/mês", "card-1": {"title": "Basic", "description": "Para você que é influencer, profissional liberal, ou tem um pequeno negócio.", "credits": "c<PERSON><PERSON><PERSON>", "price": "89,90"}, "card-2": {"title": "Plus", "description": "Recomendado para freelancers ou pequenas agências de publicidade e marketing.", "credits": "c<PERSON><PERSON><PERSON>", "price": "224,90"}, "card-3": {"title": "Advanced", "description": "Recomendado para grandes equipes ou múltiplos times dentro de uma agência.", "credits": "c<PERSON><PERSON><PERSON>", "price": "349,90"}, "card-4": {"title": "Personalizado", "description": "Defina a quantidade de créditos que você precisa.", "credits": "c<PERSON><PERSON><PERSON>", "price": ","}}, "questions": {"title": "Perguntas Frequentes", "description": "Caso não encontre a resposta para sua dúvida nas perguntas frequentes, você pode conferir a nossa sessão de<1> suporte </1>ou enviar sua dúvida para<1> <EMAIL></1>.", "question-1": "Quantos créditos eu preciso?", "answer-1": "Lorem ipsum dolor sit amet consectetur. Maecenas eget sed id quisque sed consectetur pellentesque arcu. Tortor vulputate pellentesque sit id nisi pellentesque integer dolor malesuada. Ac aliquam a vitae lacus elit. Suspendisse in maecenas ultrices ut ante. Interdum sed lorem urna tortor morbi. Arcu volutpat faucibus dignissim at in adipiscing.", "question-2": "1 crédito equivale à 1 post?", "answer-2": "Lorem ipsum dolor sit amet consectetur. Maecenas eget sed id quisque sed consectetur pellentesque arcu. Tortor vulputate pellentesque sit id nisi pellentesque integer dolor malesuada. Ac aliquam a vitae lacus elit. Suspendisse in maecenas ultrices ut ante. Interdum sed lorem urna tortor morbi. Arcu volutpat faucibus dignissim at in adipiscing.", "question-3": "Os créditos expiram?", "answer-3": "Lorem ipsum dolor sit amet consectetur. Maecenas eget sed id quisque sed consectetur pellentesque arcu. Tortor vulputate pellentesque sit id nisi pellentesque integer dolor malesuada. Ac aliquam a vitae lacus elit. Suspendisse in maecenas ultrices ut ante. Interdum sed lorem urna tortor morbi. Arcu volutpat faucibus dignissim at in adipiscing.", "question-4": "O que acontece se faltar créditos para criar uma campanha?", "answer-4": "Lorem ipsum dolor sit amet consectetur. Maecenas eget sed id quisque sed consectetur pellentesque arcu. Tortor vulputate pellentesque sit id nisi pellentesque integer dolor malesuada. Ac aliquam a vitae lacus elit. Suspendisse in maecenas ultrices ut ante. Interdum sed lorem urna tortor morbi. Arcu volutpat faucibus dignissim at in adipiscing."}}, "clientsaccounts": {"title": "Contas de Clientes", "button": "Novo Cliente"}, "campaign-list": {"title": "<PERSON><PERSON><PERSON>", "new-campaign": "Nova Campanha", "filter": {"labels": {"campaign": "<PERSON>an<PERSON>", "client": "Cliente", "status": "Status", "social-network": "Rede Social"}, "placeholders": {"campaign": "<PERSON>an<PERSON>", "client": "Cliente", "status": "Status", "social-network": "Rede Social"}, "button": "Filtrar"}, "cards": {"status": {"finished": "Finalizado"}, "preposition": "a"}}, "createclient": {"button": "Contas de Clientes", "modalcreateclient": {"hadear": "<PERSON><PERSON><PERSON> Cliente", "body": {"text1": "<PERSON>me da Conta", "text2": "Equipe", "text3": "Permissões dos Membros da Equipe", "options": {"permission1": "<PERSON><PERSON><PERSON>", "permission2": "Ver e Editar"}}, "fotter": {"botton1": "<PERSON><PERSON><PERSON>", "botton2": "<PERSON><PERSON><PERSON>"}}, "components": {"text": "Adicione sua Logo", "modalcreatelogo": {"hadear": "<PERSON><PERSON><PERSON><PERSON>", "body": {"text1": "<PERSON>me da Conta", "text2": "Enviar Imagem"}, "fotter": {"botton1": "<PERSON><PERSON><PERSON>", "botton2": "<PERSON><PERSON><PERSON><PERSON>"}}, "modalcreatephoto": {"hadear": "<PERSON><PERSON><PERSON><PERSON>", "body": {"text1": "Selecione uma pasta", "text2": "Enviar Imagem"}, "fotter": {"botton1": "<PERSON><PERSON><PERSON>", "botton2": "<PERSON><PERSON><PERSON><PERSON>"}}, "modalcreatefonts": {"hadear": "<PERSON><PERSON><PERSON><PERSON>", "body": {"text1": "Fonte de exibição", "text2": "Peso", "text3": "Fonte Corpo", "text4": "Peso"}, "fotter": {"botton1": "<PERSON><PERSON><PERSON>", "botton2": "<PERSON><PERSON><PERSON><PERSON>"}}, "modalcreatecolors": {"hadear": "<PERSON><PERSON><PERSON><PERSON>", "body": {"text1": "Nome", "text2": "Cor <PERSON>", "text3": "<PERSON><PERSON>", "text4": "<PERSON><PERSON>", "text5": "<PERSON><PERSON> <PERSON> (Escuro)", "text6": "<PERSON><PERSON> <PERSON> (Claro)"}, "fotter": {"botton1": "<PERSON><PERSON><PERSON>", "botton2": "<PERSON><PERSON><PERSON><PERSON>"}}, "modalhome": {"hadear": "<PERSON><PERSON> antecipado", "hadear2": "Cadastre-se para ter acesso antecipado à plataforma Conteoodo.", "hadear3": "Cadastro Concluído!", "body": {"text1": "Nome", "text2": "Sobrenome", "text3": "Email", "text4": "Você receberá um email com instruções para entrar na plataforma assim que estiver disponível, <PERSON><PERSON><PERSON>!"}, "fotter": {"button1": "<PERSON><PERSON><PERSON>", "button2": "Cadastrar-se", "botton3": "<PERSON><PERSON><PERSON>"}}, "menutags": {"tag1": "Logo", "tag2": "Cores", "tag3": "<PERSON><PERSON><PERSON>", "tag4": "Fotos"}, "card": {"botton1": "<PERSON><PERSON>", "botton2": "Excluir"}}}, "create-campaign": {"title": "Nova Campanha", "generate-post": "Gerar <PERSON>s", "form": {"labels": {"area": "Á<PERSON>", "objective": "Objetivo", "start-date": "Início", "end-date": "<PERSON><PERSON><PERSON><PERSON>", "posts-per-day": "Posts Por Dia", "post-hours": "<PERSON>r<PERSON>rio <PERSON> ", "week-days": "<PERSON><PERSON> Semana"}, "blocks-titles": {"campaign-data": "<PERSON><PERSON>", "format": "Formato", "publish-in": "Publicar em", "style": "<PERSON><PERSON><PERSON>"}, "week-days": {"Monday": "Segunda", "Tuesday": "<PERSON><PERSON><PERSON>", "Wednesday": "Quarta", "Thursday": "<PERSON><PERSON><PERSON>", "Friday": "Sexta", "Saturday": "Sábado", "Sunday": "Domingo"}, "errors": {"required": "Preencha este campo", "required-posts-per-day": "Selecione quantidade de posts por dia", "required-hours": "Prencha todos os campos dos horários", "required-week-days": "Selecione os dias da semana", "required-format": "Selecione um formato", "required-social-network": "Selecione ao menos uma rede social", "required-style": "Selecione um Estilo"}, "styles": {"realistic": "Realista", "abstract": "Abstrato", "aquarelle": "Aquarela", "sciFi": "SciFi", "fantasy": "Fantasia", "3d": "3d", "painting": "Pintura", "portrait": "Retrato"}, "placeholders": {"area": "Personal Trainer", "objective": "Conseguir novos alunos"}}, "modal": {"cancel": "<PERSON><PERSON><PERSON>", "title": "Revise <PERSON><PERSON>", "credits": "Saldo em Créditos:", "client": "Cliente", "total-posts": "Total de Posts", "used-credits": "Créditos Utilizados"}, "finished": {"title": "Maravilha! Sua campanha está sendo criada!", "description": "Estamos gerando os <1>{{amountPosts}}  posts</1> de sua campanha. Você pode continuar navegando ou fechar essa janela. Enviaremos uma notificação quando sua campanha estiver pronta.", "generating-posts": "Gerando Posts"}}, "components": {"drag-drop": {"input": "Escolher arquivo", "p1": "Solte seu arquivo aqui ou clique para selecionar", "p2": "Mais  arquivo...", "p3": "Nome do arquivo...", "div": "Carregando...", "less": "<PERSON><PERSON> menos"}, "PasswordStrength": {"label": "Força da Senha", "weak": "Fraco", "medium": "Médio", "strong": "Forte", "very-strong": "<PERSON><PERSON>"}, "Sidebar": {"labels": {"home": "<PERSON><PERSON>o", "megaphone": "<PERSON><PERSON><PERSON>", "calendarDays": "Agendas de Posts", "buildingStorefront": "Contatos de Clientes", "users": "Equipes", "presentationChartBar": "<PERSON><PERSON><PERSON><PERSON>", "user": "<PERSON><PERSON>", "lockClose": "Login e Senha", "creditCar": "Pagamento", "cog6Tooth": "Configurações"}, "text": "Nova Campanhas", "title": "C<PERSON><PERSON> conta", "login": "Entrar"}, "CampaignInfoSidebar": {"labels": {"text": "Nova Campanhas", "heading": "<PERSON><PERSON><PERSON>", "data1": "<PERSON><PERSON>o", "data2": "<PERSON><PERSON><PERSON><PERSON>", "imagens": "Logo", "cor1": "<PERSON><PERSON>", "cor2": "<PERSON><PERSON>", "posts1": "Publicar 01 Post Grátis", "posts2": "Publique 90 postagens", "datepicker": {"placeholders": "Selecione a Data"}}}, "Calendar": {"week": {"0": "Dom", "1": "Seg", "2": "<PERSON><PERSON>", "3": "<PERSON>ua", "4": "<PERSON>ui", "5": "Sex", "6": "<PERSON>b"}, "month": {"0": "Janeiro ", "1": "<PERSON><PERSON> ", "2": "Março ", "3": "Abril ", "4": "<PERSON><PERSON> ", "5": "<PERSON><PERSON> ", "6": "<PERSON><PERSON> ", "7": "Augosto ", "8": "Setembro ", "9": "Out<PERSON>ro ", "10": "Novembro ", "11": "Dezembro "}}, "DatePicker": {"language": "BR"}, "DropDown": {"placeholder": "Selecione..."}, "NavBar": {"login": "Entrar", "signUp": "<PERSON><PERSON><PERSON>"}}}