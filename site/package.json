{"name": "site", "private": true, "version": "0.0.1", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "prettier": "npx prettier src --check", "prettier:fix": "npx prettier --write .", "preview:": "vite preview", "test": "jest", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "release": "standard-version", "release:minor": "standard-version --release-as minor", "release:patch": "standard-version --release-as patch", "release:major": "standard-version --release-as major"}, "dependencies": {"@heroicons/react": "^2.1.1", "@hookform/resolvers": "^3.3.4", "@tanstack/react-query": "^5.28.4", "aos": "^2.3.4", "axios": "^1.6.8", "date-fns": "^2.29.3", "flowbite-react": "^0.7.2", "history": "^5.3.0", "i18next": "^23.7.16", "i18next-browser-languagedetector": "^7.2.0", "i18next-http-backend": "^2.4.2", "localforage": "^1.10.0", "match-sorter": "^6.3.1", "nuka-carousel": "^7.0.0", "react": "^18.2.0", "react-big-calendar": "^1.11.1", "react-cookie": "^7.1.4", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-hook-form": "^7.49.2", "react-i18next": "^14.0.0", "react-router-dom": "^6.22.0", "react-select": "^5.8.0", "sort-by": "^1.2.0", "tailwind-variants": "^0.1.18", "yup": "^1.3.3"}, "devDependencies": {"@commitlint/cli": "18.4.3", "@commitlint/config-conventional": "18.4.3", "@storybook/addon-actions": "^7.6.4", "@storybook/addon-essentials": "^7.6.4", "@storybook/addon-interactions": "^7.6.4", "@storybook/addon-links": "^7.6.4", "@storybook/addon-onboarding": "^1.0.9", "@storybook/blocks": "^7.6.4", "@storybook/preset-create-react-app": "^7.6.4", "@storybook/react": "^7.6.4", "@storybook/react-vite": "^7.6.4", "@storybook/test": "^7.6.4", "@swc/core": "^1.3.100", "@swc/jest": "^0.2.29", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^15.0.2", "@testing-library/user-event": "^14.5.2", "@types/aos": "^3.0.7", "@types/jest": "^29.5.11", "@types/react": "^18.2.37", "@types/react-big-calendar": "^1.8.9", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.16", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "eslint-plugin-storybook": "^0.6.15", "husky": "^8.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "prettier": "^3.1.1", "standard-version": "^9.5.0", "storybook": "^7.6.4", "storybook-addon-remix-react-router": "^3.0.0", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.0"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}