module.exports = {
  roots: ["<rootDir>/src"],
  collectCoverageFrom: ["src/**/*.{js,jsx,ts,tsx}"],
  coveragePathIgnorePatterns: [],
  testEnvironment: "jsdom",
  modulePaths: ["<rootDir>/src"],
  transform: {
    "^.+\\.(ts|js|tsx|jsx)$": "@swc/jest",
  },
  transformIgnorePatterns: [
    "[/\\\\]node_modules[/\\\\].+\\.(js|jsx|mjs|cjs|ts|tsx|jpeg)$",
  ],
  moduleNameMapper: {
    "\\.(css|jpg|jpeg|png|svg)$": "<rootDir>/tests/empty-module.js",
    "^@/(.*)$": "<rootDir>/src/$1",
    "@tests/(.*)$": "<rootDir>/tests/$1",
  },
  modulePaths: ["<rootDir>/src"],
  moduleFileExtensions: ["tsx", "ts", "js", "jsx"],
  resetMocks: true,
};
