apiVersion: secrets.infisical.com/v1alpha1
kind: InfisicalSecret
metadata:
  name: conteoodo-infisicalsecret
  labels:
    name: conteoodo-infisicalsecret
    app: conteoodo-pro
spec:
  hostAPI: https://app.infisical.com/api
  resyncInterval: 60
  authentication:
    serviceToken:
      serviceTokenSecretReference:
        secretName: infisical-token
        secretNamespace: default
      secretsScope:
        envSlug: prod
        secretsPath: "/"
  managedSecretReference:
    secretName: conteoodo-infisicalsecret
    secretNamespace: default
