apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-deploy
  labels:
    name: api-deploy
    app: conteoodo-pro
spec:
  replicas: 3
  selector:
    matchLabels:
      name: api-pod
      app: conteoodo-pro

  template:
    metadata:
      name: api-pod
      labels:
        name: api-pod
        app: conteoodo-pro
    spec:
      imagePullSecrets:
        - name: conteoodo-registry-secret
      containers:
        - name: api
          image: sjc.vultrcr.com/conteoodo/go:latest
          envFrom:
            - secretRef:
                name: conteoodo-infisicalsecret
          ports:
            - containerPort: 80