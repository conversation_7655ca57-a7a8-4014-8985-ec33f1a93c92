# Infra on Vultr with Kubernetes

Prepare the cluster first by installing some plugins:

```sh
kubectl --kubeconfig=./config/vultr.yaml apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.5.1/deploy/static/provider/cloud/deploy.yaml
kubectl --kubeconfig=./config/vultr.yaml apply -f https://github.com/jetstack/cert-manager/releases/download/v1.10.1/cert-manager.yaml
kubectl --kubeconfig=./config/vultr.yaml apply -f https://raw.githubusercontent.com/Infisical/infisical/main/k8-operator/kubectl-install/install-secrets-operator.yaml
```

Create the infisical secret. Generate the service token in the Access Control and add below:

```sh
kubectl --kubeconfig=./config/vultr.yaml create secret generic infisical-token --from-literal=infisicalToken=<your-service-token-here> 
```

Create the registry secret. Get the username and password in vultr container registry:

```sh
kubectl --kubeconfig=./config/vultr.yaml create secret docker-registry conteoodo-registry-secret --docker-server=sjc.vultrcr.com/conteoodo --docker-username=USERNAME --docker-password=PASSWORD --docker-email=<EMAIL>
```

Fetch the load balancer IP address.

```sh
kubectl --kubeconfig=./config/vultr.yaml get services/ingress-nginx-controller -n ingress-nginx
```

Creating the pod and the service:

```sh
kubectl --kubeconfig=./config/vultr.yaml create -f .
```

To check everything:

```sh
kubectl --kubeconfig=./config/vultr.yaml get deployments,pods,svc,ingress,clusterissuer
kubectl --kubeconfig=./config/vultr.yaml get all,cm,secret,ing -A
```

Update any changes:

```sh
kubectl --kubeconfig=./config/vultr.yaml apply -f .
```

Get Logs:

```sh
kubectl --kubeconfig=./config/vultr.yaml logs pod/prometheus-server-8444b5b7f7-bmkxk -n prometheus
```

Deploy a new image:

```sh
kubectl --kubeconfig=./config/vultr.yaml rollout restart deployment api-deploy
```

Destroy all:

```sh
kubectl --kubeconfig=./config/vultr.yaml delete -f .
```

kubectl --kubeconfig=./config/vultr.yaml port-forward service/prometheus-server 8081:80 -n prometheus

# Monitoring

```sh
helm --kubeconfig=./config/vultr.yaml install grafana grafana/grafana --namespace grafana --create-namespace
helm --kubeconfig=./config/vultr.yaml show values grafana/loki-distributed > loki-distributed-overrides.yaml
helm --kubeconfig=./config/vultr.yaml upgrade --install --values loki-distributed-overrides.yaml loki grafana/loki-distributed -n grafana-loki --create-namespace
helm --kubeconfig=./config/vultr.yaml show values grafana/promtail > promtail-overrides.yaml
helm --kubeconfig=./config/vultr.yaml upgrade --install --values promtail-overrides.yaml promtail grafana/promtail -n grafana-loki
kubectl --kubeconfig=./config/vultr.yaml port-forward service/grafana 8080:80 -n grafana
kubectl --kubeconfig=./config/vultr.yaml get secret grafana -n grafana -o jsonpath="{.data.admin-password}" | base64 --decode ; echo

helm --kubeconfig=./config/vultr.yaml install prometheus prometheus-community/prometheus --namespace prometheus --create-namespace
```

# Links

[https://www.vultr.com/docs/how-to-deploy-an-express-js-application-on-vultr-kubernetes-engine/](https://www.vultr.com/docs/how-to-deploy-an-express-js-application-on-vultr-kubernetes-engine/)
[https://kubernetes.io/docs/tasks/inject-data-application/distribute-credentials-secure/#define-container-environment-variables-using-secret-data](https://kubernetes.io/docs/tasks/inject-data-application/distribute-credentials-secure/#define-container-environment-variables-using-secret-data)
[https://www.vultr.com/docs/implement-a-ci-cd-pipeline-with-github-actions-and-vultr-kubernetes-engine/#Create_a_cluster_role_in_VKE](https://www.vultr.com/docs/implement-a-ci-cd-pipeline-with-github-actions-and-vultr-kubernetes-engine/#Create_a_cluster_role_in_VKE)
[https://docs.vultr.com/how-to-use-a-vultr-load-balancer-with-vke](https://docs.vultr.com/how-to-use-a-vultr-load-balancer-with-vke)
[https://docs.vultr.com/install-prometheus-and-grafana-on-vultr-kubernetes-engine-with-prometheus-operator](https://docs.vultr.com/install-prometheus-and-grafana-on-vultr-kubernetes-engine-with-prometheus-operator)
[https://akyriako.medium.com/kubernetes-logging-with-grafana-loki-promtail-in-under-10-minutes-d2847d526f9e](https://akyriako.medium.com/kubernetes-logging-with-grafana-loki-promtail-in-under-10-minutes-d2847d526f9e)
[https://semaphoreci.com/blog/prometheus-grafana-kubernetes-helm](https://semaphoreci.com/blog/prometheus-grafana-kubernetes-helm)