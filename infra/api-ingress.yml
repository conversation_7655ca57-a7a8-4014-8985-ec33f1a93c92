apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api-ingress
  labels:
    name: api-ingress
    app: conteoodo-pro
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-api
    nginx.ingress.kubernetes.io/affinity: "cookie"
    nginx.ingress.kubernetes.io/session-cookie-name: "sticky"
    nginx.ingress.kubernetes.io/session-cookie-max-age: "172800"
    nginx.ingress.kubernetes.io/session-cookie-expires: "172800"
spec:
  tls:
    - secretName: tls-secret
      hosts:
        - api.conteoodo.com
  rules:
    - host: api.conteoodo.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api-service
                port:
                  number: 80
