package main

import (
	"context"
	"errors"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"os"

	"github.com/99designs/gqlgen/graphql"
	"github.com/99designs/gqlgen/graphql/handler"
	"github.com/99designs/gqlgen/graphql/playground"
	"github.com/conteoodo/go/api"
	"github.com/conteoodo/go/api/middleware"
	"github.com/conteoodo/go/graph"
	"github.com/go-chi/chi"
	"github.com/go-chi/cors"
)

func main() {
	api.Init()

	router := chi.NewRouter()
	router.Use(middleware.AuthMiddleware())

	config := graph.Config{Resolvers: &graph.Resolver{}}
	config.Directives.IsAuthenticated = func(ctx context.Context, obj interface{}, next graphql.Resolver) (res interface{}, err error) {
		// Run built-in gqlgen directive first
		user := middleware.GetContext(ctx)
		if user == nil {
			return nil, errors.New("access denied")
		}

		return next(ctx)
	}

	srv := handler.NewDefaultServer(graph.NewExecutableSchema(config))

	cors := cors.New(cors.Options{
		AllowedOrigins: []string{"*"},
		AllowedMethods: []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders: []string{"Accept", "Authorization", "Content-Type"},
	})

	router.Use(cors.Handler)
	router.Handle("/", playground.Handler("GraphQL playground", "/query"))
	router.Handle("/query", srv)
	router.Post("/stripe", func(w http.ResponseWriter, r *http.Request) {
		const MaxBodyBytes = int64(65536)
		r.Body = http.MaxBytesReader(w, r.Body, MaxBodyBytes)
		payload, err := ioutil.ReadAll(r.Body)
		if err != nil {
			fmt.Fprintf(os.Stderr, "Error reading request body: %v\n", err)
			w.WriteHeader(http.StatusServiceUnavailable)
			return
		}

		signatureHeader := r.Header.Get("Stripe-Signature")
		paymentDomain := api.GetPaymentDomain()
		_, err2 := paymentDomain.ValidatePayment(signatureHeader, payload)

		if err2 != nil {
			fmt.Fprintf(os.Stderr, err.Error(), err)
			w.WriteHeader(http.StatusBadRequest)
			return
		}

		w.WriteHeader(http.StatusOK)
	})

	log.Println("connect to GET http://localhost:80/ for GraphQL playground")
	log.Println("connect to POST http://localhost:80/query for GraphQL queries")
	log.Println("connect to POST http://localhost:80/stripe for Stripe webhook")
	log.Fatal(http.ListenAndServe(":80", router))
}
