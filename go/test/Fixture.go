package test

import (
	"github.com/upper/db/v4"
)

type IFixture interface {
	Truncate(tables []string)
}

type Fixture struct {
	session db.Session
}

// create fixture instance
func NewFixture(session db.Session) Fixture {
	return Fixture{
		session,
	}
}

// Truncate tables
func (fixture Fixture) Truncate(tables []string) {
	for _, table := range tables {
		fixture.session.SQL().Query(`truncate table "` + table + `"`)
	}
}
