FROM golang:1.21.6 as builder
WORKDIR /app
COPY . .

# build server
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o server

# Install Certificate
RUN apt-get update && apt-get install -y --no-install-recommends ca-certificates

FROM scratch

# Copy Certificate
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# copy server files
COPY --from=builder /app/server /server
COPY --from=builder /app/cert /cert
ENTRYPOINT [ "/server" ]