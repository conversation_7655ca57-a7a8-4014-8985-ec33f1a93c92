// Code generated by MockGen. DO NOT EDIT.
// Source: api/data/UserData.go

// Package mock is a generated GoMock package.
package mock

import (
	data "github.com/conteoodo/go/api/data"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIUserData is a mock of IUserData interface.
type MockIUserData struct {
	ctrl     *gomock.Controller
	recorder *MockIUserDataMockRecorder
}

// MockIUserDataMockRecorder is the mock recorder for MockIUserData.
type MockIUserDataMockRecorder struct {
	mock *MockIUserData
}

// NewMockIUserData creates a new mock instance.
func NewMockIUserData(ctrl *gomock.Controller) *MockIUserData {
	mock := &MockIUserData{ctrl: ctrl}
	mock.recorder = &MockIUserDataMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIUserData) EXPECT() *MockIUserDataMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockIUserData) Create(fullName, email, password string) data.User {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", fullName, email, password)
	ret0, _ := ret[0].(data.User)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockIUserDataMockRecorder) Create(fullName, email, password interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockIUserData)(nil).Create), fullName, email, password)
}

// CreateOAuth mocks base method.
func (m *MockIUserData) CreateOAuth(fullName, email, authId string) data.User {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOAuth", fullName, email, authId)
	ret0, _ := ret[0].(data.User)
	return ret0
}

// CreateOAuth indicates an expected call of CreateOAuth.
func (mr *MockIUserDataMockRecorder) CreateOAuth(fullName, email, authId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOAuth", reflect.TypeOf((*MockIUserData)(nil).CreateOAuth), fullName, email, authId)
}

// GetOneByAuthId mocks base method.
func (m *MockIUserData) GetOneByAuthId(id string) data.User {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOneByAuthId", id)
	ret0, _ := ret[0].(data.User)
	return ret0
}

// GetOneByAuthId indicates an expected call of GetOneByAuthId.
func (mr *MockIUserDataMockRecorder) GetOneByAuthId(id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOneByAuthId", reflect.TypeOf((*MockIUserData)(nil).GetOneByAuthId), id)
}

// GetOneByEmail mocks base method.
func (m *MockIUserData) GetOneByEmail(email string) data.User {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOneByEmail", email)
	ret0, _ := ret[0].(data.User)
	return ret0
}

// GetOneByEmail indicates an expected call of GetOneByEmail.
func (mr *MockIUserDataMockRecorder) GetOneByEmail(email interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOneByEmail", reflect.TypeOf((*MockIUserData)(nil).GetOneByEmail), email)
}

// GetOneById mocks base method.
func (m *MockIUserData) GetOneById(id string) data.User {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOneById", id)
	ret0, _ := ret[0].(data.User)
	return ret0
}

// GetOneById indicates an expected call of GetOneById.
func (mr *MockIUserDataMockRecorder) GetOneById(id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOneById", reflect.TypeOf((*MockIUserData)(nil).GetOneById), id)
}

// GetUserByCode mocks base method.
func (m *MockIUserData) GetUserByCode(code string) data.User {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserByCode", code)
	ret0, _ := ret[0].(data.User)
	return ret0
}

// GetUserByCode indicates an expected call of GetUserByCode.
func (mr *MockIUserDataMockRecorder) GetUserByCode(code interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserByCode", reflect.TypeOf((*MockIUserData)(nil).GetUserByCode), code)
}

// Update mocks base method.
func (m *MockIUserData) Update(id string, data data.User) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", id, data)
	ret0, _ := ret[0].(bool)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockIUserDataMockRecorder) Update(id, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockIUserData)(nil).Update), id, data)
}
