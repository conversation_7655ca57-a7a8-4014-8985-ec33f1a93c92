// Code generated by MockGen. DO NOT EDIT.
// Source: api/data/ThemeData.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	data "github.com/conteoodo/go/api/data"
	gomock "github.com/golang/mock/gomock"
)

// MockIThemeData is a mock of IThemeData interface.
type MockIThemeData struct {
	ctrl     *gomock.Controller
	recorder *MockIThemeDataMockRecorder
}

// MockIThemeDataMockRecorder is the mock recorder for MockIThemeData.
type MockIThemeDataMockRecorder struct {
	mock *MockIThemeData
}

// NewMockIThemeData creates a new mock instance.
func NewMockIThemeData(ctrl *gomock.Controller) *MockIThemeData {
	mock := &MockIThemeData{ctrl: ctrl}
	mock.recorder = &MockIThemeDataMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIThemeData) EXPECT() *MockIThemeDataMockRecorder {
	return m.recorder
}

// GetThemes mocks base method.
func (m *MockIThemeData) GetThemes(page int) []data.Theme {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetThemes", page)
	ret0, _ := ret[0].([]data.Theme)
	return ret0
}

// GetThemes indicates an expected call of GetThemes.
func (mr *MockIThemeDataMockRecorder) GetThemes(page interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetThemes", reflect.TypeOf((*MockIThemeData)(nil).GetThemes), page)
}
