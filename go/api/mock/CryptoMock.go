// Code generated by MockGen. DO NOT EDIT.
// Source: api/infra/Crypto.go

// Package mock is a generated GoMock package.
package mock

import (
	infra "github.com/conteoodo/go/api/infra"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockICrypto is a mock of ICrypto interface.
type MockICrypto struct {
	ctrl     *gomock.Controller
	recorder *MockICryptoMockRecorder
}

// MockICryptoMockRecorder is the mock recorder for MockICrypto.
type MockICryptoMockRecorder struct {
	mock *MockICrypto
}

// NewMockICrypto creates a new mock instance.
func NewMockICrypto(ctrl *gomock.Controller) *MockICrypto {
	mock := &MockICrypto{ctrl: ctrl}
	mock.recorder = &MockICryptoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICrypto) EXPECT() *MockICryptoMockRecorder {
	return m.recorder
}

// CheckHash mocks base method.
func (m *MockICrypto) CheckHash(hash, password string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckHash", hash, password)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckHash indicates an expected call of CheckHash.
func (mr *MockICryptoMockRecorder) CheckHash(hash, password interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckHash", reflect.TypeOf((*MockICrypto)(nil).CheckHash), hash, password)
}

// ExtractToken mocks base method.
func (m *MockICrypto) ExtractToken(token string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExtractToken", token)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExtractToken indicates an expected call of ExtractToken.
func (mr *MockICryptoMockRecorder) ExtractToken(token interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExtractToken", reflect.TypeOf((*MockICrypto)(nil).ExtractToken), token)
}

// GenerateHash mocks base method.
func (m *MockICrypto) GenerateHash(text string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateHash", text)
	ret0, _ := ret[0].(string)
	return ret0
}

// GenerateHash indicates an expected call of GenerateHash.
func (mr *MockICryptoMockRecorder) GenerateHash(text interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateHash", reflect.TypeOf((*MockICrypto)(nil).GenerateHash), text)
}

// GenerateJWT mocks base method.
func (m *MockICrypto) GenerateJWT(id string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateJWT", id)
	ret0, _ := ret[0].(string)
	return ret0
}

// GenerateJWT indicates an expected call of GenerateJWT.
func (mr *MockICryptoMockRecorder) GenerateJWT(id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateJWT", reflect.TypeOf((*MockICrypto)(nil).GenerateJWT), id)
}

// GenerateRandomString mocks base method.
func (m *MockICrypto) GenerateRandomString(size int) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateRandomString", size)
	ret0, _ := ret[0].(string)
	return ret0
}

// GenerateRandomString indicates an expected call of GenerateRandomString.
func (mr *MockICryptoMockRecorder) GenerateRandomString(size interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateRandomString", reflect.TypeOf((*MockICrypto)(nil).GenerateRandomString), size)
}

// VerifyIdToken mocks base method.
func (m *MockICrypto) VerifyIdToken(idToken string) (infra.UserOAuth, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyIdToken", idToken)
	ret0, _ := ret[0].(infra.UserOAuth)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyIdToken indicates an expected call of VerifyIdToken.
func (mr *MockICryptoMockRecorder) VerifyIdToken(idToken interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyIdToken", reflect.TypeOf((*MockICrypto)(nil).VerifyIdToken), idToken)
}
