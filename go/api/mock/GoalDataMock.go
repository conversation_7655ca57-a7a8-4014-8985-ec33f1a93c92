// Code generated by MockGen. DO NOT EDIT.
// Source: api/data/GoalData.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	data "github.com/conteoodo/go/api/data"
	gomock "github.com/golang/mock/gomock"
)

// MockIGoalData is a mock of IGoalData interface.
type MockIGoalData struct {
	ctrl     *gomock.Controller
	recorder *MockIGoalDataMockRecorder
}

// MockIGoalDataMockRecorder is the mock recorder for MockIGoalData.
type MockIGoalDataMockRecorder struct {
	mock *MockIGoalData
}

// NewMockIGoalData creates a new mock instance.
func NewMockIGoalData(ctrl *gomock.Controller) *MockIGoalData {
	mock := &MockIGoalData{ctrl: ctrl}
	mock.recorder = &MockIGoalDataMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIGoalData) EXPECT() *MockIGoalDataMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockIGoalData) Create(fullName, email, password string) data.Goal {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", fullName, email, password)
	ret0, _ := ret[0].(data.Goal)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockIGoalDataMockRecorder) Create(fullName, email, password interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockIGoalData)(nil).Create), fullName, email, password)
}

// GetGoals mocks base method.
func (m *MockIGoalData) GetGoals(themeId string, page int) []data.Goal {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGoals", themeId, page)
	ret0, _ := ret[0].([]data.Goal)
	return ret0
}

// GetGoals indicates an expected call of GetGoals.
func (mr *MockIGoalDataMockRecorder) GetGoals(themeId, page interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGoals", reflect.TypeOf((*MockIGoalData)(nil).GetGoals), themeId, page)
}
