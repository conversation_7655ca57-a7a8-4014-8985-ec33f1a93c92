// Code generated by MockGen. DO NOT EDIT.
// Source: api/infra/Payment.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	stripe "github.com/stripe/stripe-go/v74"
)

// MockIPayment is a mock of IPayment interface.
type MockIPayment struct {
	ctrl     *gomock.Controller
	recorder *MockIPaymentMockRecorder
}

// MockIPaymentMockRecorder is the mock recorder for MockIPayment.
type MockIPaymentMockRecorder struct {
	mock *MockIPayment
}

// NewMockIPayment creates a new mock instance.
func NewMockIPayment(ctrl *gomock.Controller) *MockIPayment {
	mock := &MockIPayment{ctrl: ctrl}
	mock.recorder = &MockIPaymentMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIPayment) EXPECT() *MockIPaymentMockRecorder {
	return m.recorder
}

// CreatePaymentIntent mocks base method.
func (m *MockIPayment) CreatePaymentIntent(amount float64) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePaymentIntent", amount)
	ret0, _ := ret[0].(string)
	return ret0
}

// CreatePaymentIntent indicates an expected call of CreatePaymentIntent.
func (mr *MockIPaymentMockRecorder) CreatePaymentIntent(amount interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePaymentIntent", reflect.TypeOf((*MockIPayment)(nil).CreatePaymentIntent), amount)
}

// ValidateHeader mocks base method.
func (m *MockIPayment) ValidateHeader(signatureHeader string, payload []byte) stripe.Event {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateHeader", signatureHeader, payload)
	ret0, _ := ret[0].(stripe.Event)
	return ret0
}

// ValidateHeader indicates an expected call of ValidateHeader.
func (mr *MockIPaymentMockRecorder) ValidateHeader(signatureHeader, payload interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateHeader", reflect.TypeOf((*MockIPayment)(nil).ValidateHeader), signatureHeader, payload)
}
