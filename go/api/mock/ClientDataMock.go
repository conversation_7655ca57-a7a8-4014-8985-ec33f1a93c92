// Code generated by MockGen. DO NOT EDIT.
// Source: api/data/ClientData.go

// Package mock is a generated GoMock package.
package mock

import (
	data "github.com/conteoodo/go/api/data"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIClientData is a mock of IClientData interface.
type MockIClientData struct {
	ctrl     *gomock.Controller
	recorder *MockIClientDataMockRecorder
}

// MockIClientDataMockRecorder is the mock recorder for MockIClientData.
type MockIClientDataMockRecorder struct {
	mock *MockIClientData
}

// NewMockIClientData creates a new mock instance.
func NewMockIClientData(ctrl *gomock.Controller) *MockIClientData {
	mock := &MockIClientData{ctrl: ctrl}
	mock.recorder = &MockIClientDataMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClientData) EXPECT() *MockIClientDataMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockIClientData) Create(userId, name, logo, primaryColor, secondaryColor string) data.Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", userId, name, logo, primaryColor, secondaryColor)
	ret0, _ := ret[0].(data.Client)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockIClientDataMockRecorder) Create(userId, name, logo, primaryColor, secondaryColor interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockIClientData)(nil).Create), userId, name, logo, primaryColor, secondaryColor)
}
