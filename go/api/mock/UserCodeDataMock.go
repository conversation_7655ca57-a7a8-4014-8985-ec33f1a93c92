// Code generated by MockGen. DO NOT EDIT.
// Source: api/data/UserCodeData.go

// Package mock is a generated GoMock package.
package mock

import (
	data "github.com/conteoodo/go/api/data"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIUserCodeData is a mock of IUserCodeData interface.
type MockIUserCodeData struct {
	ctrl     *gomock.Controller
	recorder *MockIUserCodeDataMockRecorder
}

// MockIUserCodeDataMockRecorder is the mock recorder for MockIUserCodeData.
type MockIUserCodeDataMockRecorder struct {
	mock *MockIUserCodeData
}

// NewMockIUserCodeData creates a new mock instance.
func NewMockIUserCodeData(ctrl *gomock.Controller) *MockIUserCodeData {
	mock := &MockIUserCodeData{ctrl: ctrl}
	mock.recorder = &MockIUserCodeDataMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIUserCodeData) EXPECT() *MockIUserCodeDataMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockIUserCodeData) Create(userId, code string) data.UserCode {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", userId, code)
	ret0, _ := ret[0].(data.UserCode)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockIUserCodeDataMockRecorder) Create(userId, code interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockIUserCodeData)(nil).Create), userId, code)
}

// GetLastUserCode mocks base method.
func (m *MockIUserCodeData) GetLastUserCode(userId string) data.UserCode {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastUserCode", userId)
	ret0, _ := ret[0].(data.UserCode)
	return ret0
}

// GetLastUserCode indicates an expected call of GetLastUserCode.
func (mr *MockIUserCodeDataMockRecorder) GetLastUserCode(userId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastUserCode", reflect.TypeOf((*MockIUserCodeData)(nil).GetLastUserCode), userId)
}
