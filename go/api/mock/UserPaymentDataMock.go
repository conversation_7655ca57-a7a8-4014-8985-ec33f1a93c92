// Code generated by MockGen. DO NOT EDIT.
// Source: api/data/UserPaymentData.go

// Package mock is a generated GoMock package.
package mock

import (
	data "github.com/conteoodo/go/api/data"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIUserPaymentData is a mock of IUserPaymentData interface.
type MockIUserPaymentData struct {
	ctrl     *gomock.Controller
	recorder *MockIUserPaymentDataMockRecorder
}

// MockIUserPaymentDataMockRecorder is the mock recorder for MockIUserPaymentData.
type MockIUserPaymentDataMockRecorder struct {
	mock *MockIUserPaymentData
}

// NewMockIUserPaymentData creates a new mock instance.
func NewMockIUserPaymentData(ctrl *gomock.Controller) *MockIUserPaymentData {
	mock := &MockIUserPaymentData{ctrl: ctrl}
	mock.recorder = &MockIUserPaymentDataMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIUserPaymentData) EXPECT() *MockIUserPaymentDataMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockIUserPaymentData) Create(userId, productId, paymentToken string) data.UserPayment {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", userId, productId, paymentToken)
	ret0, _ := ret[0].(data.UserPayment)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockIUserPaymentDataMockRecorder) Create(userId, productId, paymentToken interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockIUserPaymentData)(nil).Create), userId, productId, paymentToken)
}

// Update mocks base method.
func (m *MockIUserPaymentData) Update(paymentToken string, data data.UserPayment) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", paymentToken, data)
	ret0, _ := ret[0].(bool)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockIUserPaymentDataMockRecorder) Update(paymentToken, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockIUserPaymentData)(nil).Update), paymentToken, data)
}
