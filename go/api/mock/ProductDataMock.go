// Code generated by MockGen. DO NOT EDIT.
// Source: api/data/ProductData.go

// Package mock is a generated GoMock package.
package mock

import (
	data "github.com/conteoodo/go/api/data"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIProductData is a mock of IProductData interface.
type MockIProductData struct {
	ctrl     *gomock.Controller
	recorder *MockIProductDataMockRecorder
}

// MockIProductDataMockRecorder is the mock recorder for MockIProductData.
type MockIProductDataMockRecorder struct {
	mock *MockIProductData
}

// NewMockIProductData creates a new mock instance.
func NewMockIProductData(ctrl *gomock.Controller) *MockIProductData {
	mock := &MockIProductData{ctrl: ctrl}
	mock.recorder = &MockIProductDataMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIProductData) EXPECT() *MockIProductDataMockRecorder {
	return m.recorder
}

// GetOneByCode mocks base method.
func (m *MockIProductData) GetOneByCode(code string) data.Product {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOneByCode", code)
	ret0, _ := ret[0].(data.Product)
	return ret0
}

// GetOneByCode indicates an expected call of GetOneByCode.
func (mr *MockIProductDataMockRecorder) GetOneByCode(code interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOneByCode", reflect.TypeOf((*MockIProductData)(nil).GetOneByCode), code)
}
