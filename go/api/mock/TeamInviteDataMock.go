// Code generated by MockGen. DO NOT EDIT.
// Source: api/data/TeamInviteData.go

// Package mock is a generated GoMock package.
package mock

import (
	data "github.com/conteoodo/go/api/data"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockITeamInviteData is a mock of ITeamInviteData interface.
type MockITeamInviteData struct {
	ctrl     *gomock.Controller
	recorder *MockITeamInviteDataMockRecorder
}

// MockITeamInviteDataMockRecorder is the mock recorder for MockITeamInviteData.
type MockITeamInviteDataMockRecorder struct {
	mock *MockITeamInviteData
}

// NewMockITeamInviteData creates a new mock instance.
func NewMockITeamInviteData(ctrl *gomock.Controller) *MockITeamInviteData {
	mock := &MockITeamInviteData{ctrl: ctrl}
	mock.recorder = &MockITeamInviteDataMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockITeamInviteData) EXPECT() *MockITeamInviteDataMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockITeamInviteData) Create(teamId, name, email string) data.TeamInvite {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", teamId, name, email)
	ret0, _ := ret[0].(data.TeamInvite)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockITeamInviteDataMockRecorder) Create(teamId, name, email interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockITeamInviteData)(nil).Create), teamId, name, email)
}
