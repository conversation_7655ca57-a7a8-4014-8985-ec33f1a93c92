// Code generated by MockGen. DO NOT EDIT.
// Source: api/data/UserLoginData.go

// Package mock is a generated GoMock package.
package mock

import (
	data "github.com/conteoodo/go/api/data"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIUserLoginData is a mock of IUserLoginData interface.
type MockIUserLoginData struct {
	ctrl     *gomock.Controller
	recorder *MockIUserLoginDataMockRecorder
}

// MockIUserLoginDataMockRecorder is the mock recorder for MockIUserLoginData.
type MockIUserLoginDataMockRecorder struct {
	mock *MockIUserLoginData
}

// NewMockIUserLoginData creates a new mock instance.
func NewMockIUserLoginData(ctrl *gomock.Controller) *MockIUserLoginData {
	mock := &MockIUserLoginData{ctrl: ctrl}
	mock.recorder = &MockIUserLoginDataMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIUserLoginData) EXPECT() *MockIUserLoginDataMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockIUserLoginData) Create(userId string) data.UserLogin {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", userId)
	ret0, _ := ret[0].(data.UserLogin)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockIUserLoginDataMockRecorder) Create(userId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockIUserLoginData)(nil).Create), userId)
}
