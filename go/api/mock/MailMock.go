// Code generated by MockGen. DO NOT EDIT.
// Source: api/infra/Mail.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIMail is a mock of IMail interface.
type MockIMail struct {
	ctrl     *gomock.Controller
	recorder *MockIMailMockRecorder
}

// MockIMailMockRecorder is the mock recorder for MockIMail.
type MockIMailMockRecorder struct {
	mock *MockIMail
}

// NewMockIMail creates a new mock instance.
func NewMockIMail(ctrl *gomock.Controller) *MockIMail {
	mock := &MockIMail{ctrl: ctrl}
	mock.recorder = &MockIMailMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIMail) EXPECT() *MockIMailMockRecorder {
	return m.recorder
}

// SendEmail mocks base method.
func (m *MockIMail) SendEmail(Email, Name, TemplateName string, Variables map[string]interface{}) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendEmail", Email, Name, TemplateName, Variables)
	ret0, _ := ret[0].(bool)
	return ret0
}

// SendEmail indicates an expected call of SendEmail.
func (mr *MockIMailMockRecorder) SendEmail(Email, Name, TemplateName, Variables interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendEmail", reflect.TypeOf((*MockIMail)(nil).SendEmail), Email, Name, TemplateName, Variables)
}
