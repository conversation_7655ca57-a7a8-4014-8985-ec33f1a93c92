// Code generated by MockGen. DO NOT EDIT.
// Source: api/data/TeamData.go

// Package mock is a generated GoMock package.
package mock

import (
	data "github.com/conteoodo/go/api/data"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockITeamData is a mock of ITeamData interface.
type MockITeamData struct {
	ctrl     *gomock.Controller
	recorder *MockITeamDataMockRecorder
}

// MockITeamDataMockRecorder is the mock recorder for MockITeamData.
type MockITeamDataMockRecorder struct {
	mock *MockITeamData
}

// NewMockITeamData creates a new mock instance.
func NewMockITeamData(ctrl *gomock.Controller) *MockITeamData {
	mock := &MockITeamData{ctrl: ctrl}
	mock.recorder = &MockITeamDataMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockITeamData) EXPECT() *MockITeamDataMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockITeamData) Create(userId, name string) data.Team {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", userId, name)
	ret0, _ := ret[0].(data.Team)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockITeamDataMockRecorder) Create(userId, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockITeamData)(nil).Create), userId, name)
}
