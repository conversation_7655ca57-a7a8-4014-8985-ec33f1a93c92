// Code generated by MockGen. DO NOT EDIT.
// Source: api/data/CampaignData.go

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"
	time "time"

	data "github.com/conteoodo/go/api/data"
	gomock "github.com/golang/mock/gomock"
)

// MockICampaignData is a mock of ICampaignData interface.
type MockICampaignData struct {
	ctrl     *gomock.Controller
	recorder *MockICampaignDataMockRecorder
}

// MockICampaignDataMockRecorder is the mock recorder for MockICampaignData.
type MockICampaignDataMockRecorder struct {
	mock *MockICampaignData
}

// NewMockICampaignData creates a new mock instance.
func NewMockICampaignData(ctrl *gomock.Controller) *MockICampaignData {
	mock := &MockICampaignData{ctrl: ctrl}
	mock.recorder = &MockICampaignDataMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICampaignData) EXPECT() *MockICampaignDataMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockICampaignData) Create(userId, goalId string, initialDate, finalDate time.Time, socialNetworks []string, postDailyAmount int, postHours, weekDays, formats, styles []string) data.Campaign {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", userId, goalId, initialDate, finalDate, socialNetworks, postDailyAmount, postHours, weekDays, formats, styles)
	ret0, _ := ret[0].(data.Campaign)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockICampaignDataMockRecorder) Create(userId, goalId, initialDate, finalDate, socialNetworks, postDailyAmount, postHours, weekDays, formats, styles interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockICampaignData)(nil).Create), userId, goalId, initialDate, finalDate, socialNetworks, postDailyAmount, postHours, weekDays, formats, styles)
}

// Search mocks base method.
func (m *MockICampaignData) Search(userId string) []data.Campaign {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Search", userId)
	ret0, _ := ret[0].([]data.Campaign)
	return ret0
}

// Search indicates an expected call of Search.
func (mr *MockICampaignDataMockRecorder) Search(userId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Search", reflect.TypeOf((*MockICampaignData)(nil).Search), userId)
}
