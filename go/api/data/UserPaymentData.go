package data

import (
	"fmt"
	"time"

	"github.com/upper/db/v4"
)

type UserPayment struct {
	Id           string    `db:"id,omitempty"`
	UserId       string    `db:"user_id"`
	ProductId    string    `db:"product_id"`
	PaymentToken string    `db:"payment_token"`
	Status       string    `db:"status,omitempty"`
	CreatedAt    time.Time `db:"created_at,omitempty"`
	UpdatedAt    time.Time `db:"updated_at,omitempty"`
	DeletedAt    time.Time `db:"deleted_at,omitempty"`
}

type IUserPaymentData interface {
	Create(userId, productId, paymentToken string) UserPayment
	Update(paymentToken string, data UserPayment) bool
}

type UserPaymentData struct {
	session db.Session
}

// create the new instance of user payment
func NewUserPaymentData(session db.Session) IUserPaymentData {
	return UserPaymentData{
		session,
	}
}

// create the user payment
func (userPaymentData UserPaymentData) Create(userId, productId, paymentToken string) UserPayment {
	var userPaymentCollection = userPaymentData.session.Collection("user_payment")

	insert, err := userPaymentCollection.Insert(UserPayment{
		UserId:       userId,
		ProductId:    productId,
		PaymentToken: paymentToken,
	})

	if err != nil {
		fmt.Println("Save user payment: ", err)
	}

	var userPayment UserPayment
	err2 := userPaymentCollection.Find(db.Cond{"id": insert.ID()}).One(&userPayment)

	if err2 != nil {
		fmt.Println("Get user payment: ", err2)
	}

	return userPayment
}

// update user_payment by paymentToken
func (userPaymentData UserPaymentData) Update(paymentToken string, data UserPayment) bool {
	var userPaymentCollection = userPaymentData.session.Collection("user_payment")

	res := userPaymentCollection.Find(db.Cond{"payment_token": paymentToken})

	var userPayment UserPayment
	err := res.One(&userPayment)
	if err != nil {
		return false
	}

	if data.Status != "" {
		userPayment.Status = data.Status
	}

	userPayment.UpdatedAt = time.Now()

	res.Update(userPayment)
	return true
}
