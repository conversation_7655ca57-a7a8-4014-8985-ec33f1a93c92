package data

import (
	"fmt"
	"time"

	"github.com/upper/db/v4"
)

type UserCode struct {
	Id        string    `db:"id,omitempty"`
	UserId    string    `db:"user_id"`
	Code      string    `db:"code"`
	CreatedAt time.Time `db:"created_at,omitempty"`
	UpdatedAt time.Time `db:"updated_at,omitempty"`
	DeletedAt time.Time `db:"deleted_at,omitempty"`
}

type IUserCodeData interface {
	Create(userId, code string) UserCode
	GetLastUserCode(userId string) UserCode
}

type UserCodeData struct {
	session db.Session
}

// create the new instance of user data
func NewUserCodeData(session db.Session) IUserCodeData {
	return UserCodeData{
		session,
	}
}

// get last userCode by userId
func (userCodeData UserCodeData) GetLastUserCode(userId string) UserCode {
	var userCodeCollection = userCodeData.session.Collection("user_code")

	var userCode UserCode
	err := userCodeCollection.Find(db.Cond{"user_id": userId}).OrderBy("created_at DESC").One(&userCode)

	if err != nil {
		return UserCode{}
	}

	return userCode
}

// create the user code
func (userCodeData UserCodeData) Create(userId, code string) UserCode {
	var userCodeCollection = userCodeData.session.Collection("user_code")

	insert, err := userCodeCollection.Insert(UserCode{
		UserId: userId,
		Code:   code,
	})

	if err != nil {
		fmt.Println("Save user code: ", err)
	}

	var userCode UserCode
	err2 := userCodeCollection.Find(db.Cond{"id": insert.ID()}).One(&userCode)

	if err2 != nil {
		fmt.Println("Get user code: ", err2)
	}

	return userCode
}
