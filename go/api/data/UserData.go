package data

import (
	"database/sql"
	"fmt"
	"time"

	"github.com/upper/db/v4"
)

type User struct {
	Id          string         `db:"id,omitempty,pk"`
	FullName    string         `db:"full_name"`
	Email       string         `db:"email"`
	Password    sql.NullString `db:"password,omitempty"`
	AuthId      sql.NullString `db:"auth_id,omitempty"`
	IsConfirmed bool           `db:"is_confirmed"`
	CreatedAt   time.Time      `db:"created_at,omitempty"`
	UpdatedAt   time.Time      `db:"updated_at,omitempty"`
	DeletedAt   time.Time      `db:"deleted_at,omitempty"`
}

type IUserData interface {
	Create(fullName, email, password string) User
	CreateOAuth(fullName, email, authId string) User
	Update(id string, data User) bool
	GetOneByEmail(email string) User
	GetOneById(id string) User
	GetOneByAuthId(id string) User
	GetUserByCode(code string) User
}

type UserData struct {
	session db.Session
}

// create the new instance of user data
func NewUserData(session db.Session) IUserData {
	return UserData{
		session,
	}
}

// create an user in the database
func (userData UserData) Create(fullName, email, password string) User {
	var userCollection = userData.session.Collection("user")

	insert, err := userCollection.Insert(User{
		FullName:    fullName,
		Email:       email,
		Password:    sql.NullString{String: password, Valid: true},
		IsConfirmed: false,
	})

	if err != nil {
		fmt.Println("Save user: ", err)
	}

	var user User
	err2 := userCollection.Find(db.Cond{"id": insert.ID()}).One(&user)

	if err2 != nil {
		fmt.Println("Get user: ", err2)
	}

	return user
}

// create an user in the database
func (userData UserData) CreateOAuth(fullName, email, authId string) User {
	var userCollection = userData.session.Collection("user")

	insert, err := userCollection.Insert(User{
		FullName:    fullName,
		Email:       email,
		AuthId:      sql.NullString{String: authId, Valid: true},
		IsConfirmed: true,
	})

	if err != nil {
		fmt.Println("Save user: ", err)
	}

	var user User
	err2 := userCollection.Find(db.Cond{"id": insert.ID()}).One(&user)

	if err2 != nil {
		fmt.Println("Get user: ", err2)
	}

	return user
}

// get user by Email
func (userData UserData) GetOneByEmail(email string) User {
	var userCollection = userData.session.Collection("user")

	var user User
	err := userCollection.Find(db.Cond{"email": email}).One(&user)

	if err != nil {
		return User{}
	}

	return user
}

// get user by Id
func (userData UserData) GetOneById(id string) User {
	var userCollection = userData.session.Collection("user")

	var user User
	err := userCollection.Find(db.Cond{"id": id}).One(&user)

	if err != nil {
		return User{}
	}

	return user
}

// get user by Id
func (userData UserData) GetOneByAuthId(id string) User {
	var userCollection = userData.session.Collection("user")

	var user User
	err := userCollection.Find(db.Cond{"auth_id": id}).One(&user)

	if err != nil {
		return User{}
	}

	return user
}

// get user by code
func (userData UserData) GetUserByCode(code string) User {
	var userCodeCollection = userData.session.Collection("user_code")

	var userCode UserCode
	err := userCodeCollection.Find(db.Cond{"code": code}).One(&userCode)

	if err != nil {
		return User{}
	}

	var userCollection = userData.session.Collection("user")

	var user User
	err2 := userCollection.Find(db.Cond{"id": userCode.UserId}).One(&user)

	if err2 != nil {
		return User{}
	}

	return user
}

// update user by id
func (userData UserData) Update(id string, data User) bool {
	var userCollection = userData.session.Collection("user")

	res := userCollection.Find(db.Cond{"id": id})

	var user User
	err := res.One(&user)
	if err != nil {
		return false
	}

	if data.FullName != "" {
		user.FullName = data.FullName
	}

	if data.Password.Valid {
		user.Password = data.Password
	}

	if data.IsConfirmed {
		user.IsConfirmed = data.IsConfirmed
	}

	user.UpdatedAt = time.Now()

	res.Update(user)
	return true
}
