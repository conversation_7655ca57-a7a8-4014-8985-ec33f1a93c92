package data

import (
	"time"

	"github.com/upper/db/v4"
)

type Theme struct {
	Id        string    `db:"id,omitempty,pk"`
	Name      string    `db:"name"`
	CreatedAt time.Time `db:"created_at,omitempty"`
	UpdatedAt time.Time `db:"updated_at,omitempty"`
	DeletedAt time.Time `db:"deleted_at,omitempty"`
}

type IThemeData interface {
	GetThemes(page int) []Theme
}

type ThemeData struct {
	session db.Session
}

// create the new instance of user data
func NewThemeData(session db.Session) IThemeData {
	return ThemeData{
		session,
	}
}

// get paginated themes
func (themeData ThemeData) GetThemes(page int) []Theme {
	var themeCollection = themeData.session.Collection("theme")
	var themes []Theme

	res := themeCollection.Find().Paginate(50).Page(uint(page))
	err := res.All(&themes)

	if err != nil {
		return []Theme{}
	}

	return themes
}
