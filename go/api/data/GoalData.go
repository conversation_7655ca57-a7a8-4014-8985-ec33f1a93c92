package data

import (
	"time"

	"github.com/upper/db/v4"
)

type Goal struct {
	Id             string    `db:"id,omitempty"`
	ThemeId        string    `db:"theme_id"`
	Language       string    `db:"language"`
	Name           string    `db:"name"`
	ImageClass     string    `db:"image_class"`
	TextClass      string    `db:"text_class"`
	PrefixPrompt   string    `db:"prefix_prompt"`
	NegativePrompt string    `db:"negativePrompt"`
	CreatedAt      time.Time `db:"created_at,omitempty"`
	UpdatedAt      time.Time `db:"updated_at,omitempty"`
	DeletedAt      time.Time `db:"deleted_at,omitempty"`
}

type IGoalData interface {
	Create(fullName, email, password string) Goal
	GetGoals(themeId string, page int) []Goal
}

type GoalData struct {
	session db.Session
}

// create the new instance of user data
func NewGoalData(session db.Session) IGoalData {
	return GoalData{
		session,
	}
}

// create an user in the database
func (goalData GoalData) Create(fullName, email, password string) Goal {
	return Goal{}
}

// get paginated goals
func (goalData GoalData) GetGoals(themeId string, page int) []Goal {
	var goalCollection = goalData.session.Collection("goal")
	var goals []Goal

	res := goalCollection.Find(db.Cond{
		"theme_id": themeId,
	}).Paginate(50).Page(uint(page))
	err := res.All(&goals)

	if err != nil {
		return []Goal{}
	}

	return goals
}
