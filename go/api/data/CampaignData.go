package data

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/upper/db/v4"
)

type Campaign struct {
	Id              string    `db:"id,omitempty"`
	UserId          string    `db:"user_id"`
	GoalId          string    `db:"goal_id"`
	InitialDate     time.Time `db:"initial_date"`
	FinalDate       time.Time `db:"final_date"`
	SocialNetworks  string    `db:"social_networks"`
	PostDailyAmount int       `db:"post_daily_amount"`
	PostHours       string    `db:"post_hours"`
	WeekDays        string    `db:"week_days"`
	Formats         string    `db:"formats"`
	Styles          string    `db:"styles"`
	CreatedAt       time.Time `db:"created_at,omitempty"`
	UpdatedAt       time.Time `db:"updated_at,omitempty"`
	DeletedAt       time.Time `db:"deleted_at,omitempty"`
}

type ICampaignData interface {
	Create(
		userId string,
		goalId string,
		initialDate time.Time,
		finalDate time.Time,
		socialNetworks []string,
		postDailyAmount int,
		postHours []string,
		weekDays []string,
		formats []string,
		styles []string,
	) Campaign
	Search(
		userId string,
	) []Campaign
}

type CampaignData struct {
	session db.Session
}

// create the new instance of user data
func NewCampaignData(session db.Session) ICampaignData {
	return CampaignData{
		session,
	}
}

func toJson(items []string) string {
	jsonValue, _ := json.Marshal(items)
	return string(jsonValue)
}

// create the campaign
func (campaignData CampaignData) Create(
	userId string,
	goalId string,
	initialDate time.Time,
	finalDate time.Time,
	socialNetworks []string,
	postDailyAmount int,
	postHours []string,
	weekDays []string,
	formats []string,
	styles []string,
) Campaign {
	var campaignCollection = campaignData.session.Collection("campaign")

	insert, err := campaignCollection.Insert(Campaign{
		UserId:          userId,
		GoalId:          goalId,
		InitialDate:     initialDate,
		FinalDate:       finalDate,
		SocialNetworks:  toJson(socialNetworks),
		PostDailyAmount: postDailyAmount,
		PostHours:       toJson(postHours),
		WeekDays:        toJson(weekDays),
		Formats:         toJson(formats),
		Styles:          toJson(styles),
	})

	if err != nil {
		fmt.Println("Save campaign: ", err)
	}

	var campaign Campaign
	err2 := campaignCollection.Find(db.Cond{"id": insert.ID()}).One(&campaign)

	if err2 != nil {
		fmt.Println("Get campaign: ", err2)
	}

	return campaign
}

// get campaigns
func (campaignData CampaignData) Search(
	userId string,
) []Campaign {
	var campaignCollection = campaignData.session.Collection("campaign")

	var campaigns []Campaign

	res := campaignCollection.Find()
	err := res.All(&campaigns)

	if err != nil {
		fmt.Println("Fetch campaign: ", err)
	}

	return campaigns
}
