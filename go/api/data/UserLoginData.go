package data

import (
	"fmt"
	"time"

	"github.com/upper/db/v4"
)

type UserLogin struct {
	Id        string    `db:"id,omitempty"`
	UserId    string    `db:"user_id"`
	CreatedAt time.Time `db:"created_at,omitempty"`
	UpdatedAt time.Time `db:"updated_at,omitemptyt"`
	DeletedAt time.Time `db:"deleted_at,omitempty"`
}

type IUserLoginData interface {
	Create(userId string) UserLogin
}

type UserLoginData struct {
	session db.Session
}

// create the new instance of user login
func NewUserLoginData(session db.Session) IUserLoginData {
	return UserLoginData{
		session,
	}
}

// create the user login
func (userLoginData UserLoginData) Create(userId string) UserLogin {
	var userLoginCollection = userLoginData.session.Collection("user_login")

	insert, err := userLoginCollection.Insert(UserLogin{
		UserId: userId,
	})

	if err != nil {
		fmt.Println("Save user login: ", err)
	}

	var userLogin UserLogin
	err2 := userLoginCollection.Find(db.Cond{"id": insert.ID()}).One(&userLogin)

	if err2 != nil {
		fmt.Println("Get user login: ", err2)
	}

	return userLogin
}
