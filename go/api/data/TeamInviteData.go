package data

import (
	"fmt"
	"time"

	"github.com/upper/db/v4"
)

type TeamInvite struct {
	Id         string    `db:"id,omitempty,pk"`
	TeamId     string    `db:"team_id"`
	Email      string    `db:"email"`
	Name       string    `db:"name"`
	AcceptedAt time.Time `db:"accepted_at,omitempty"`
	CreatedAt  time.Time `db:"created_at,omitempty"`
	UpdatedAt  time.Time `db:"updated_at,omitempty"`
	DeletedAt  time.Time `db:"deleted_at,omitempty"`
}

type ITeamInviteData interface {
	Create(teamId, name, email string) TeamInvite
}

type TeamInviteData struct {
	session db.Session
}

// create the new instance of team invite data
func NewTeamInviteData(session db.Session) ITeamInviteData {
	return TeamInviteData{
		session,
	}
}

// create the team invite
func (teamInviteData TeamInviteData) Create(teamId, name, email string) TeamInvite {
	var teamInviteCollection = teamInviteData.session.Collection("team_invite")

	insert, err := teamInviteCollection.Insert(TeamInvite{
		TeamId: teamId,
		Name:   name,
		Email:  email,
	})

	if err != nil {
		fmt.Println("Save team invite: ", err)
	}

	var teamInvite TeamInvite
	err2 := teamInviteCollection.Find(db.Cond{"id": insert.ID()}).One(&teamInvite)

	if err2 != nil {
		fmt.Println("Get team invite: ", err2)
	}

	return teamInvite
}
