package data

import (
	"fmt"
	"time"

	"github.com/upper/db/v4"
)

type Team struct {
	Id        string    `db:"id,omitempty,pk"`
	UserId    string    `db:"user_id"`
	Name      string    `db:"name"`
	CreatedAt time.Time `db:"created_at,omitempty"`
	UpdatedAt time.Time `db:"updated_at,omitempty"`
	DeletedAt time.Time `db:"deleted_at,omitempty"`
}

type ITeamData interface {
	Create(userId, name string) Team
}

type TeamData struct {
	session db.Session
}

// create the new instance of team data
func NewTeamData(session db.Session) ITeamData {
	return TeamData{
		session,
	}
}

// create the team
func (teamData TeamData) Create(userId, name string) Team {
	var teamCollection = teamData.session.Collection("team")

	insert, err := teamCollection.Insert(Team{
		UserId: userId,
		Name:   name,
	})

	if err != nil {
		fmt.Println("Save team: ", err)
	}

	var team Team
	err2 := teamCollection.Find(db.Cond{"id": insert.ID()}).One(&team)

	if err2 != nil {
		fmt.Println("Get team: ", err2)
	}

	return team
}
