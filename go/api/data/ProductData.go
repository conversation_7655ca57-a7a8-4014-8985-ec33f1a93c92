package data

import (
	"time"

	"github.com/upper/db/v4"
)

type Product struct {
	Id        string    `db:"id,omitempty,pk"`
	Code      string    `db:"code"`
	Price     float64   `db:"price"`
	CreatedAt time.Time `db:"created_at,omitempty"`
	UpdatedAt time.Time `db:"updated_at,omitempty"`
	DeletedAt time.Time `db:"deleted_at,omitempty"`
}

type IProductData interface {
	GetOneByCode(code string) Product
}

type ProductData struct {
	session db.Session
}

// create the new instance of product data
func NewProductData(session db.Session) IProductData {
	return ProductData{
		session,
	}
}

// get product by Code
func (productData ProductData) GetOneByCode(code string) Product {
	var productCollection = productData.session.Collection("product")

	var product Product
	err := productCollection.Find(db.Cond{"code": code}).One(&product)

	if err != nil {
		return Product{}
	}

	return product
}
