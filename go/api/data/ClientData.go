package data

import (
	"fmt"
	"time"

	"github.com/upper/db/v4"
)

type Client struct {
	Id             string    `db:"id,omitempty"`
	UserId         string    `db:"user_id"`
	Name           string    `db:"name"`
	Logo           string    `db:"logo"`
	PrimaryColor   string    `db:"primary_color"`
	SecondaryColor string    `db:"secondary_color"`
	CreatedAt      time.Time `db:"created_at,omitempty"`
	UpdatedAt      time.Time `db:"updated_at,omitempty"`
	DeletedAt      time.Time `db:"deleted_at,omitempty"`
}

type IClientData interface {
	Create(userId, name, logo, primaryColor, secondaryColor string) Client
}

type ClientData struct {
	session db.Session
}

// create the new instance of company data
func NewClientData(session db.Session) IClientData {
	return ClientData{
		session,
	}
}

// create a company in the database
func (companyData ClientData) Create(userId, name, logo, primaryColor, secondaryColor string) Client {
	var companyCollection = companyData.session.Collection("company")

	insert, err := companyCollection.Insert(Client{
		UserId:         userId,
		Name:           name,
		Logo:           logo,
		PrimaryColor:   primaryColor,
		SecondaryColor: secondaryColor,
	})

	if err != nil {
		fmt.Println("Save company: ", err)
	}

	var company Client
	err2 := companyCollection.Find(db.Cond{"id": insert.ID()}).One(&company)

	if err2 != nil {
		fmt.Println("Get company: ", err2)
	}

	return company
}
