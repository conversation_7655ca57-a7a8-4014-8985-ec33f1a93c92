package data

import (
	"fmt"
	"time"

	"github.com/upper/db/v4"
)

type GoalItem struct {
	Id        string    `db:"id,omitempty"`
	GoalId    string    `db:"goal_id"`
	Date      time.Time `db:"date"`
	Title     string    `db:"title"`
	Prompt    string    `db:"prompt"`
	CreatedAt time.Time `db:"created_at,omitempty"`
	UpdatedAt time.Time `db:"updated_at,omitempty"`
	DeletedAt time.Time `db:"deleted_at,omitempty"`

	Language     string `db:"language"`
	ImageClass   string `db:"image_class"`
	OverlayClass string `db:"overlay_class"`
	TextClass    string `db:"text_class"`
	CampaignId   string `db:"campaign_id,omitempty"`
}

type IGoalItemData interface {
	GetValidGoalItems() []GoalItem
}

type GoalItemData struct {
	session db.Session
}

// create the new instance of user data
func NewGoalItemData(session db.Session) IGoalItemData {
	return GoalItemData{
		session,
	}
}

// create an user in the database
func (goalItemData GoalItemData) GetValidGoalItems() []GoalItem {
	rows, err := goalItemData.session.SQL().
		Query(`
SELECT goal_item.*,
       campaign.id AS campaign_id,
	   goal.language,
       goal.image_class,
       goal.overlay_class,
       goal.text_class
  FROM goal_item
  JOIN goal on goal.id = goal_item.goal_id
  JOIN campaign on campaign.goal_id = goal.id
 WHERE campaign.initial_date <= NOW() AND campaign.final_date >= NOW()
 LIMIT 50
		`)

	if err != nil {
		fmt.Println("GetValidGoalItems: ", err)
	}

	iter := goalItemData.session.SQL().NewIterator(rows)

	var goalItems []GoalItem
	if err := iter.All(&goalItems); err != nil {
		fmt.Println("GetValidGoalItems: ", err)
	}

	return goalItems
}
