package image

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"strings"
)

type IStability interface {
	GenerateImage(positivePrompt, negativePrompt string) (string, error)
}

type Stability struct {
	api<PERSON>ey string
}

// create the instance
func NewStability(apiKey string) Stability {
	return Stability{
		apiKey,
	}
}

type DeepAIResponse struct {
	Id         string
	Output_url string
	Status     string
}

// Generate the Image
func (stability Stability) GenerateImage(positivePrompt, negativePrompt string) (string, error) {
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	writer.Write<PERSON><PERSON>("steps", "40")
	writer.Write<PERSON>ield("width", "1024")
	writer.Write<PERSON><PERSON>("height", "1024")
	writer.Write<PERSON>ield("seed", "0")
	writer.Write<PERSON>ield("cfg_scale", "5")
	writer.Write<PERSON>ield("samples", "1")

	arrayStr := []string{`[{"text": "`, positivePrompt, `", "weight": 1 }, { "text": "`, negativePrompt, `", "weight": -1 }]`}

	var jsonStr = []byte(strings.Join(arrayStr, " "))
	writer.WriteField("text_prompts", string(jsonStr[:]))

	err := writer.Close()

	if err != nil {
		return "", err
	}

	client := &http.Client{}
	req, err := http.NewRequest("POST", "https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image", payload)

	if err != nil {
		return "", err
	}

	authStr := []string{`Bearer`, stability.apiKey}
	req.Header.Add("Authorization", strings.Join(authStr, " "))
	req.Header.Set("Accept", "application/json")
	res, err := client.Do(req)

	if err != nil {
		return "", err
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return "", err
	}

	var response DeepAIResponse
	json.Unmarshal(body, &response)

	if response.Status != "" {
		fmt.Println("Status: ", response.Status)
	}

	return response.Output_url, err
}
