package infra

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"golang.org/x/crypto/bcrypt"
	"google.golang.org/api/idtoken"
)

type UserOAuth struct {
	Email string
	Name  string
	Id    string
}

type ICrypto interface {
	GenerateJWT(id string) string
	GenerateHash(text string) string
	GenerateRandomString(size int) string
	CheckHash(hash, password string) bool
	ExtractToken(token string) (string, error)
	VerifyIdToken(idToken string) (UserOAuth, error)
}

type Crypto struct {
	secretKey       string
	clientId        string
	iosClientId     string
	androidClientId string
}

// create the instance
func NewCrypto(secretKey, clientId, iosClientId, androidClientId string) Crypto {
	return Crypto{
		secretKey,
		clientId,
		iosClientId,
		androidClientId,
	}
}

// Generate the JWT token
func (crypto Crypto) GenerateJWT(id string) string {
	var mySigningKey = []byte(crypto.secretKey)
	token := jwt.New(jwt.SigningMethodHS256)
	claims := token.Claims.(jwt.MapClaims)

	claims["authorized"] = true
	claims["id"] = id
	claims["exp"] = time.Now().Add(time.Minute * 30).Unix()

	tokenString, err := token.SignedString(mySigningKey)

	if err != nil {
		fmt.Println("generate JWT: ", err)
	}

	return tokenString
}

type MyClaims struct {
	jwt.StandardClaims
	Id string `json:"id"`
}

// Extract info from JWT Token
func (crypto Crypto) ExtractToken(tokenString string) (string, error) {
	var err error

	token, err := jwt.ParseWithClaims(tokenString, &MyClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(crypto.secretKey), nil
	})

	if err != nil {
		err = errors.New("your Token has been expired")
		return "", err
	}

	if !token.Valid {
		err = errors.New("your Token is invalid")
		return "", err
	}

	claims := token.Claims.(*MyClaims)
	return claims.Id, err
}

// Generate hash
func (crypto Crypto) GenerateHash(text string) string {
	bytes, err := bcrypt.GenerateFromPassword([]byte(text), bcrypt.DefaultCost)

	if err != nil {
		fmt.Println("generate hash: ", err)
	}

	return string(bytes)
}

// Generate random string
func (crypto Crypto) GenerateRandomString(size int) string {
	const letterBytes = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	b := make([]byte, size)
	for i := range b {
		b[i] = letterBytes[rand.Intn(len(letterBytes))]
	}
	return string(b)
}

// Check Hash
func (crypto Crypto) CheckHash(hash, password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

// Verify Id Token
func (crypto Crypto) VerifyIdToken(idToken string) (UserOAuth, error) {
	payload, _ := idtoken.Validate(context.Background(), idToken, crypto.clientId)

	if payload != nil {
		return UserOAuth{
			Email: payload.Claims["email"].(string),
			Name:  payload.Claims["name"].(string),
			Id:    payload.Claims["sub"].(string),
		}, nil
	}

	payload2, _ := idtoken.Validate(context.Background(), idToken, crypto.iosClientId)

	if payload2 != nil {
		return UserOAuth{
			Email: payload2.Claims["email"].(string),
			Name:  payload2.Claims["name"].(string),
			Id:    payload2.Claims["sub"].(string),
		}, nil
	}

	payload3, err := idtoken.Validate(context.Background(), idToken, crypto.androidClientId)

	if payload3 != nil {
		return UserOAuth{
			Email: payload3.Claims["email"].(string),
			Name:  payload3.Claims["name"].(string),
			Id:    payload3.Claims["sub"].(string),
		}, nil
	}

	return UserOAuth{}, err

}
