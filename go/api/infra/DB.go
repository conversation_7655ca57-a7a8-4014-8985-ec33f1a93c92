package infra

import (
	"fmt"
	"log"

	"github.com/upper/db/v4"
	"github.com/upper/db/v4/adapter/cockroachdb"
)

type IDB interface {
	Connect() db.Session
}

type DB struct {
	host     string
	username string
	password string
	dbname   string
	isSSL    bool
}

// create db instance
func NewDB(host, username, password, dbname string, isSSL bool) *DB {
	return &DB{
		host, username, password, dbname, isSSL,
	}
}

// Connect to cockroach db
func (db *DB) Connect() db.Session {
	options := map[string]string{}

	if db.isSSL {
		options = map[string]string{
			"sslmode":     "verify-full",
			"sslrootcert": "./cert/root.crt",
		}
	}

	var settings = cockroachdb.ConnectionURL{
		Database: db.dbname,
		Host:     db.host,
		User:     db.username,
		Password: db.password,
		Options:  options,
	}

	sess, err := cockroachdb.Open(settings)

	if err != nil {
		fmt.Println("cockroachdb.Open: ", err)
	}

	log.Println("Connected with the DB.")

	return sess
}
