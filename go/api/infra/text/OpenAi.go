package text

import (
	"bytes"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"strings"
)

type IOpenAi interface {
	GenerateImage(tags, language string) (string, error)
}

type OpenAi struct {
	secretKey string
}

// create the instance
func NewOpenAi(secretKey string) OpenAi {
	return OpenAi{
		secretKey,
	}
}

type Message struct {
	Content string
}

type Choice struct {
	Message Message
}

type OpenAIResponse struct {
	Id      string
	Choices []Choice
}

// Generate the text
func (openAi OpenAi) GenerateText(tags, language string) (string, error) {
	client := &http.Client{}

	arrayStr := []string{`{"model": "gpt-3.5-turbo", "messages": [{"role": "user", "content": "`, "create a copy for a post with the following tags in", language, ":", tags, `"}]}`}
	var jsonStr = []byte(strings.Join(arrayStr, " "))
	req, err := http.NewRequest("POST", "https://api.openai.com/v1/chat/completions", bytes.NewBuffer(jsonStr))

	if err != nil {
		return "", err
	}

	authStr := []string{`Bearer`, openAi.secretKey}
	req.Header.Add("Authorization", strings.Join(authStr, " "))
	req.Header.Set("Content-Type", "application/json")
	res, err := client.Do(req)

	if err != nil {
		return "", err
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return "", err
	}

	var response OpenAIResponse
	json.Unmarshal(body, &response)

	return response.Choices[0].Message.Content, err
}
