package infra

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/ses"
	"github.com/aws/aws-sdk-go-v2/service/ses/types"
)

type IMail interface {
	SendEmail(
		Email,
		Name,
		TemplateName string,
		Variables map[string]string,
	) bool
}

type Mail struct {
	from,
	fromName string
	client *ses.Client
}

// create email instance
func NewMail(
	from,
	fromName string) Mail {

	cfg, err := config.LoadDefaultConfig(
		context.Background(),
		config.WithRegion("sa-east-1"),
	)
	if err != nil {
		fmt.Errorf("unable to start aws config: %w", err)
	}

	client := ses.NewFromConfig(cfg)

	return Mail{
		from,
		fromName,
		client,
	}
}

// Send the email
func (mail Mail) SendEmail(
	Email,
	Name,
	TemplateName string,
	Variables map[string]string,
) bool {

	var data string
	if len(Variables) != 0 {
		val, err := json.Marshal(Variables)
		if err != nil {
			fmt.<PERSON>rrorf("unable to marshal template data: %w", err)
		}
		data = string(val)
	}

	source := mail.fromName + " <" + mail.from + ">"

	// Attempt to send the email.
	ctx := context.Background()
	_, err := mail.client.SendTemplatedEmail(ctx, &ses.SendTemplatedEmailInput{
		Source: &source,
		Destination: &types.Destination{
			ToAddresses: []string{
				Name + " <" + Email + ">",
			},
		},
		Template:     &TemplateName,
		TemplateData: &data,
	})

	// Display error messages if they occur.
	if err != nil {
		log.Println("Error occurred while send the email", err)
	}

	return true
}
