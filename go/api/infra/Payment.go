package infra

import (
	"fmt"

	"github.com/stripe/stripe-go/v74"
	"github.com/stripe/stripe-go/v74/paymentintent"
	"github.com/stripe/stripe-go/v74/webhook"
)

type IPayment interface {
	CreatePaymentIntent(amount float64) string
	ValidateHeader(signatureHeader string, payload []byte) stripe.Event
}

type Payment struct {
	key            string
	endpointSecret string
}

// create the instance
func NewPayment(key, endpointSecret string) Payment {
	return Payment{
		key,
		endpointSecret,
	}
}

// Generate the payment intent
func (payment Payment) CreatePaymentIntent(amount float64) string {
	stripe.Key = payment.key

	var value = int64(amount * 100)

	params := &stripe.PaymentIntentParams{
		Amount:   stripe.Int64(value),
		Currency: stripe.String(string(stripe.CurrencyBRL)),
		AutomaticPaymentMethods: &stripe.PaymentIntentAutomaticPaymentMethodsParams{
			Enabled: stripe.Bool(true),
		},
	}

	paymentIntent, err := paymentintent.New(params)

	if err != nil {
		fmt.Println("Error while create the intent: ", err)
		return ""
	}

	return paymentIntent.ClientSecret
}

// Validate the header
func (payment Payment) ValidateHeader(signatureHeader string, payload []byte) stripe.Event {
	stripe.Key = payment.key
	event, err := webhook.ConstructEvent(payload, signatureHeader, payment.endpointSecret)
	if err != nil {
		return stripe.Event{}
	}

	return event
}
