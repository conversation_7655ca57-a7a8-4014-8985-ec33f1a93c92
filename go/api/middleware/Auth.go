package middleware

import (
	"context"
	"net/http"

	"github.com/conteoodo/go/api"
)

var userCtxKey = &contextKey{"Id", "<PERSON>NAme", "Email"}

type contextKey struct {
	Id       string
	FullName string
	Email    string
}

// Middleware decodes the share session cookie and packs the session into context
// https://gqlgen.com/recipes/authentication/
// https://www.bacancytechnology.com/blog/golang-jwt
// https://github.com/99designs/gqlgen/issues/262
// https://medium.com/geekculture/authenticate-go-graphql-with-jwt-436c74340d
func AuthMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			token := r.Header.Get("Authorization")
			if token == "" {
				next.ServeHTTP(w, r)
				return
			}

			userDomain := api.GetUserDomain()
			user, err := userDomain.GetLoggedUser(token)

			if err != nil {
				http.Error(w, err.Error(), http.StatusForbidden)
				return
			}

			ctx := context.WithValue(r.Context(), userCtxKey, &contextKey{
				Id:       user.Id,
				FullName: user.FullName,
				Email:    user.Email,
			})

			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

func GetContext(ctx context.Context) *contextKey {
	raw, _ := ctx.Value(userCtxKey).(*contextKey)
	return raw
}
