package api

import (
	"os"
	"strconv"

	"github.com/conteoodo/go/api/data"
	"github.com/conteoodo/go/api/domain/campaign"
	"github.com/conteoodo/go/api/domain/client"
	"github.com/conteoodo/go/api/domain/goal"
	"github.com/conteoodo/go/api/domain/payment"
	"github.com/conteoodo/go/api/domain/team"
	"github.com/conteoodo/go/api/domain/theme"
	"github.com/conteoodo/go/api/domain/user"
	"github.com/conteoodo/go/api/infra"

	"github.com/upper/db/v4"
)

var session db.Session
var crypto infra.ICrypto
var email infra.Mail
var stripe infra.Payment

func Init() {
	isSSL, _ := strconv.ParseBool(os.Getenv("DATABASE_ISSSL"))

	session = infra.NewDB(
		os.Getenv("DATABASE_HOST"),
		os.Getenv("DATABASE_USERNAME"),
		os.Getenv("DATABASE_PASSWORD"),
		os.Getenv("DATABASE_DBNAME"),
		isSSL,
	).Connect()

	crypto = infra.NewCrypto(os.Getenv("CRYPTO_SECRET"),
		os.Getenv("GOOGLE_CLIENT_ID"),
		os.Getenv("GOOGLE_IOS_CLIENT_ID"),
		os.Getenv("GOOGLE_ANDROID_CLIENT_ID"),
	)
	email = infra.NewMail(
		os.Getenv("EMAIL_FROMEMAIL"),
		os.Getenv("EMAIL_FROMNAME"),
	)
	stripe = infra.NewPayment(
		os.Getenv("PAYMENT_STRIPE_KEY"),
		os.Getenv("PAYMENT_STRIPE_ENDPOINT"),
	)
}

func GetCampaignDomain() campaign.ICampaignDomain {
	return campaign.NewCampaignDomain(
		data.NewCampaignData(session),
	)
}

func GetClientDomain() client.IClientDomain {
	return client.NewClientDomain(
		data.NewClientData(session),
	)
}

func GetPaymentDomain() payment.IPaymentDomain {
	return payment.NewPaymentDomain(
		stripe,
		data.NewProductData(session),
		data.NewUserPaymentData(session),
	)
}

func GetUserDomain() user.IUserDomain {
	return user.NewUserDomain(
		data.NewUserData(session),
		data.NewUserCodeData(session),
		data.NewUserLoginData(session),
		crypto,
		email,
	)
}

func GetGoalDomain() goal.IGoalDomain {
	return goal.NewGoalDomain(
		data.NewGoalData(session),
	// data.NewGoalItemData(session),
	// image.NewStability(os.Getenv("IMAGE_STABILITY_KEY")),
	// text.NewOpenAi(os.Getenv("TEXT_OPENAI_SECRET_KEY")),
	)
}

func GetTeamDomain() team.ITeamDomain {
	return team.NewTeamDomain(
		data.NewTeamData(session),
		data.NewTeamInviteData(session),
		email,
	)
}

func GetThemeDomain() theme.IThemeDomain {
	return theme.NewThemeDomain(
		data.NewThemeData(session),
	)
}
