package goal

import (
	"testing"

	"github.com/conteoodo/go/api/mock"

	"github.com/conteoodo/go/api/data"

	"github.com/golang/mock/gomock"
	"github.com/magiconair/properties/assert"
)

func TestGetGoals(t *testing.T) {
	t.Run("Should get the goals", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		var parsedGoals []data.Goal
		parsedGoals = append(parsedGoals, data.Goal{
			Id:             "Id",
			ThemeId:        "f6b77d0e-131b-479e-ab67-b5d44f628011",
			Language:       "pt-br",
			Name:           "Emagreça em 30 dias",
			ImageClass:     "imageIa",
			TextClass:      "openIa",
			PrefixPrompt:   "#some #prefix",
			NegativePrompt: "#some #negative #prompt",
		})

		goalDataMock := mock.NewMockIGoalData(controller)
		goalDataMock.EXPECT().GetGoals("f6b77d0e-131b-479e-ab67-b5d44f628011", 1).Return(parsedGoals)

		goalDomain := NewGoalDomain(goalDataMock)
		res := goalDomain.GetGoals("f6b77d0e-131b-479e-ab67-b5d44f628011", 1)
		assert.Equal(t, res, parsedGoals)
	})
}
