package goal

import (
	"io/ioutil"
	"os/exec"
	"strings"

	"github.com/conteoodo/go/api/data"
)

// The goal Domain class
// this is where every goal logic related will be located
type IGoalDomain interface {
	GetGoals(themeId string, page int) []data.Goal
	Generate()
}

type GoalDomain struct {
	goalData data.IGoalData
	// goalItemData data.IGoalItemData
	// deepAi       image.Stability
	// openAi       text.OpenAi
}

// return the goal domain instance
func NewGoalDomain(
	goalData data.IGoalData,
	// goalItemData data.IGoalItemData,
	// deepAi image.Stability,
	// openAi text.OpenAi,
) IGoalDomain {
	return GoalDomain{
		goalData,
		// goalItemData,
		// deepAi,
		// openAi,
	}
}

// get paginated goals
func (goalDomain GoalDomain) GetGoals(themeId string, page int) []data.Goal {
	goals := goalDomain.goalData.GetGoals(themeId, page)
	return goals
}

// get all campaigns to be generated
// create the image
// create the text
// create the overlay
// replace parameters
// send to s3
// save in db
func (goalDomain GoalDomain) Generate() {
	// get all campaigns to be generated
	// items := goalDomain.goalItemData.GetValidGoalItems()

	// var text string
	// var err error

	// for _, item := range items {
	// create the image
	// switch strings.ToLower(item.ImageClass) {
	// case "deepai":
	// default:
	// 	image, err := goalDomain.deepAi.GenerateImage(item.Tags)
	// 	if err != nil {
	// 		fmt.Println("openia: ", err)
	// 	}
	// 	fmt.Printf(image)
	// }

	// create the text
	// switch strings.ToLower(item.TextClass) {
	// case "openai":
	// default:
	// 	text, err = goalDomain.openAi.GenerateText(item.Prompt, item.Language)
	// 	if err != nil {
	// 		fmt.Println("default text: ", err)
	// 	}
	// }

	var svg string = `<?xml version="1.0" encoding="utf-8"?>
	<!-- Generator: Adobe Illustrator 27.3.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
	<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
		 width="1080px" height="1920px" viewBox="0 0 1080 1920" style="enable-background:new 0 0 1080 1920;" xml:space="preserve">
	<rect x="0.6" style="fill:${tertiaryColor};" width="1079.2" height="1920"/>
	<polygon style="fill:${primaryColor};" points="1080,901.4 398.6,901.4 398.6,0 385.7,0 385.7,914.2 1080,914.2 "/>
	<rect x="0.6" y="991.1" style="fill:${secondaryColor};" width="1079.2" height="928.9"/>
	<rect x="0.6" style="fill:none;" width="1079.2" height="1920"/>
	<rect x="539.1" style="fill:${primaryColor};" width="540.8" height="712.4"/>
	<g id="image">
		<defs>
			<path id="_x24__x7B_image_x7D_" d="M920.8,880.1c0,223.9-171.7,407.1-381.6,407.1s-381.6-183.2-381.6-407.1V482.4
				c0-223.9,171.7-407.1,381.6-407.1s381.6,183.2,381.6,407.1V880.1z"/>
		</defs>
		<clipPath id="_x24__x7B_image_x7D__00000165203332598959662120000013820265824404178592_">
			<use xlink:href="{#_x24__x7B_image_x7D_}"  style="overflow:visible;"/>
		</clipPath>
		
			<g transform="matrix(1 0 0 1 0 -3.814697e-06)" style="clip-path:url(#_x24__x7B_image_x7D__00000165203332598959662120000013820265824404178592_);">
			
				<image style="overflow:visible;" width="4000" height="6000" xlink:href="${image}"  transform="matrix(0.1908 0 0 0.1908 156.5392 54.3008)">
			</image>
		</g>
	</g>
	<g>
		<g>
			<rect x="192.4" y="1155" style="fill:${primaryColor};" width="697.2" height="322.1"/>
		</g>
	</g>
	<g>
		<path style="fill:${darkColor};" d="M563.6,1780.3c-7.1-6-14.1-12.1-21.2-18.1c-0.8-0.7-1.9-0.7-2.7,0c-7.1,6-14.1,12.1-21.2,18.1
			c-1.9,1.6,0.9,4.4,2.7,2.7c6.6-5.6,13.2-11.3,19.8-16.9c6.6,5.6,13.2,11.3,19.8,16.9C562.7,1784.7,565.5,1781.9,563.6,1780.3z"/>
	</g>
	<rect x="0.6" style="fill:none;" width="1079.2" height="1920"/>
	<g>
		<g>
			<g>
				<g>
					<path style="fill:${primaryColor};" d="M56.1,775.1c0,4.4-3.6,8-8,8s-8-3.6-8-8c0-4.4,3.6-8,8-8S56.1,770.7,56.1,775.1z"/>
					<path style="fill:${primaryColor};" d="M115.1,775.1c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8
						C111.5,767.1,115.1,770.7,115.1,775.1z"/>
				</g>
			</g>
			<g>
				<g>
					<path style="fill:${primaryColor};" d="M56.1,834.1c0,4.4-3.6,8-8,8s-8-3.6-8-8c0-4.4,3.6-8,8-8S56.1,829.6,56.1,834.1z"/>
					<path style="fill:${primaryColor};" d="M115.1,834.1c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8
						C111.5,826,115.1,829.6,115.1,834.1z"/>
				</g>
			</g>
		</g>
		<g>
			<g>
				<g>
					<path style="fill:${primaryColor};" d="M56.1,893c0,4.4-3.6,8-8,8s-8-3.6-8-8c0-4.4,3.6-8,8-8S56.1,888.6,56.1,893z"/>
					<path style="fill:${primaryColor};" d="M115.1,893c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8C111.5,885,115.1,888.6,115.1,893z"
						/>
				</g>
			</g>
			<g>
				<g>
					<path style="fill:${primaryColor};" d="M56.1,952c0,4.4-3.6,8-8,8s-8-3.6-8-8c0-4.4,3.6-8,8-8S56.1,947.5,56.1,952z"/>
					<path style="fill:${primaryColor};" d="M115.1,952c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8C111.5,943.9,115.1,947.5,115.1,952
						z"/>
				</g>
			</g>
		</g>
	</g>
	<g>
		<g>
			<g>
				<g>
					<path style="fill:${lightColor};" d="M852.2,1796c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8
						C848.6,1788,852.2,1791.6,852.2,1796z"/>
					<path style="fill:${lightColor};" d="M911.2,1796c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8
						C907.6,1788,911.2,1791.6,911.2,1796z"/>
				</g>
				<g>
					<path style="fill:${lightColor};" d="M970.1,1796c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8
						C966.5,1788,970.1,1791.6,970.1,1796z"/>
					<path style="fill:${lightColor};" d="M1029,1796c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8C1025.5,1788,1029,1791.6,1029,1796
						z"/>
				</g>
			</g>
			<g>
				<g>
					<path style="fill:${lightColor};" d="M852.2,1855c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8
						C848.6,1846.9,852.2,1850.5,852.2,1855z"/>
					<path style="fill:${lightColor};" d="M911.2,1855c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8
						C907.6,1846.9,911.2,1850.5,911.2,1855z"/>
				</g>
				<g>
					<path style="fill:${lightColor};" d="M970.1,1855c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8
						C966.5,1846.9,970.1,1850.5,970.1,1855z"/>
					<path style="fill:${lightColor};" d="M1029,1855c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8
						C1025.5,1846.9,1029,1850.5,1029,1855z"/>
				</g>
			</g>
		</g>
	</g>
	<rect x="0.6" style="fill:none;" width="1079.2" height="1920"/>
	<polygon style="fill:${tertiaryColor};" points="-0.1,1278.4 113.7,1278.4 115.5,1920.6 128.3,1920.6 126.6,1265.5 -0.1,1265.5 "/>
	<g>
		<polygon style="fill:none;" points="-0.1,1608.5 153,1455.4 93.4,1455.4 -0.1,1548.9 	"/>
		<polygon style="fill:none;" points="-0.1,1674.7 154.7,1519.9 154.7,1460.3 -0.1,1615.1 	"/>
		<polygon style="fill:none;" points="-0.1,1807 154.7,1652.3 154.7,1592.7 -0.1,1747.5 	"/>
		<polygon style="fill:none;" points="-0.1,1920.6 18.7,1920.6 154.7,1784.6 154.7,1725.1 -0.1,1879.9 	"/>
		<polygon style="fill:none;" points="-0.1,1873.2 154.7,1718.5 154.7,1658.9 -0.1,1813.7 	"/>
		<polygon style="fill:none;" points="154.1,1920.6 157.7,1917 157.7,1857.5 94.5,1920.6 	"/>
		<polygon style="fill:none;" points="-0.1,1542.3 86.8,1455.4 27.2,1455.4 -0.1,1482.7 	"/>
		<polygon style="fill:none;" points="-0.1,1740.9 154.7,1586.1 154.7,1526.5 -0.1,1681.3 	"/>
		<polygon style="fill:none;" points="87.9,1920.6 157.7,1850.8 154.7,1791.3 25.3,1920.6 	"/>
		<polygon style="fill:none;" points="20.6,1455.4 -0.1,1455.4 -0.1,1476.1 	"/>
		<polygon style="fill:${primaryColor};" points="-0.1,1482.7 27.2,1455.4 20.6,1455.4 -0.1,1476.1 	"/>
		<polygon style="fill:${primaryColor};" points="-0.1,1548.9 93.4,1455.4 86.8,1455.4 -0.1,1542.3 	"/>
		<polygon style="fill:${primaryColor};" points="-0.1,1615.1 154.7,1460.3 157.7,1455.4 153,1455.4 -0.1,1608.5 	"/>
		<polygon style="fill:${primaryColor};" points="-0.1,1681.3 154.7,1526.5 154.7,1519.9 -0.1,1674.7 	"/>
		<polygon style="fill:${primaryColor};" points="-0.1,1747.5 154.7,1592.7 154.7,1586.1 -0.1,1740.9 	"/>
		<polygon style="fill:${primaryColor};" points="-0.1,1813.7 154.7,1658.9 154.7,1652.3 -0.1,1807 	"/>
		<polygon style="fill:${primaryColor};" points="-0.1,1879.9 154.7,1725.1 154.7,1718.5 -0.1,1873.2 	"/>
		<polygon style="fill:${primaryColor};" points="25.3,1920.6 154.7,1791.3 154.7,1784.6 18.7,1920.6 	"/>
		<polygon style="fill:${primaryColor};" points="94.5,1920.6 157.7,1857.5 157.7,1850.8 87.9,1920.6 	"/>
		<polygon style="fill:${primaryColor};" points="157.7,1920.6 157.7,1917 154.1,1920.6 	"/>
	</g>
	<g>
		<g>
			<g>
				<g>
					<path style="fill:${tertiaryColor};" d="M804.8,108c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8C801.2,99.9,804.8,103.5,804.8,108z
						"/>
					<path style="fill:${tertiaryColor};" d="M863.7,108c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8C860.1,99.9,863.7,103.5,863.7,108z
						"/>
				</g>
				<g>
					<path style="fill:${tertiaryColor};" d="M922.7,108c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8C919.1,99.9,922.7,103.5,922.7,108z
						"/>
					<path style="fill:${tertiaryColor};" d="M981.6,108c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8C978,99.9,981.6,103.5,981.6,108z"
						/>
				</g>
			</g>
			<g>
				<g>
					<path style="fill:${tertiaryColor};" d="M804.8,166.9c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8
						C801.2,158.9,804.8,162.5,804.8,166.9z"/>
					<path style="fill:${tertiaryColor};" d="M863.7,166.9c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8
						C860.1,158.9,863.7,162.5,863.7,166.9z"/>
				</g>
				<g>
					<path style="fill:${tertiaryColor};" d="M922.7,166.9c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8
						C919.1,158.9,922.7,162.5,922.7,166.9z"/>
					<path style="fill:${tertiaryColor};" d="M981.6,166.9c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8
						C978,158.9,981.6,162.5,981.6,166.9z"/>
				</g>
			</g>
		</g>
		<g>
			<g>
				<g>
					<path style="fill:${tertiaryColor};" d="M804.8,225.9c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8
						C801.2,217.8,804.8,221.4,804.8,225.9z"/>
					<path style="fill:${tertiaryColor};" d="M863.7,225.9c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8
						C860.1,217.8,863.7,221.4,863.7,225.9z"/>
				</g>
				<g>
					<path style="fill:${tertiaryColor};" d="M922.7,225.9c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8
						C919.1,217.8,922.7,221.4,922.7,225.9z"/>
					<path style="fill:${tertiaryColor};" d="M981.6,225.9c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8
						C978,217.8,981.6,221.4,981.6,225.9z"/>
				</g>
			</g>
			<g>
				<g>
					<path style="fill:${tertiaryColor};" d="M804.8,284.8c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8
						C801.2,276.8,804.8,280.4,804.8,284.8z"/>
					<path style="fill:${tertiaryColor};" d="M863.7,284.8c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8
						C860.1,276.8,863.7,280.4,863.7,284.8z"/>
				</g>
				<g>
					<path style="fill:${tertiaryColor};" d="M922.7,284.8c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8
						C919.1,276.8,922.7,280.4,922.7,284.8z"/>
					<path style="fill:${tertiaryColor};" d="M981.6,284.8c0,4.4-3.6,8-8,8c-4.4,0-8-3.6-8-8c0-4.4,3.6-8,8-8
						C978,276.8,981.6,280.4,981.6,284.8z"/>
				</g>
			</g>
		</g>
	</g>
	<rect x="193.6" y="1505.8" style="fill:none;" width="696" height="200.8"/>
	<textArea width="200" height="auto" id="_x24__x7B_paragraph_x7D_" transform="matrix(1 0 0 1 403.6992 1541.6299)" style="fill:${darkColor}; font-family:'Roboto-Regular'; font-size:47.7169px;">${paragraph}</textArea>
	<g>
		<rect x="234.5" y="1198.2" style="fill:none;" width="615.4" height="229.8"/>
		
			<text transform="matrix(1 0 0 1 370.2046 1282.8691)" style="fill:${lightColor}; font-family:'PlayfairDisplay-Black'; font-size:108.3239px;">${title}</text>
	</g>
	<g id="logo">
		<defs>
			<rect id="_x24__x7B_logo_x7D_" x="31.9" y="35.2" width="171.2" height="171.2"/>
		</defs>
		<clipPath id="_x24__x7B_logo_x7D__00000083791786754983406640000012310836243758932617_">
			<use xlink:href="${logo}"  style="overflow:visible;"/>
		</clipPath>
	</g>
	<g>
		<rect x="243.9" y="1809.7" style="fill:none;" width="592.3" height="74.1"/>
		
			<text transform="matrix(1 0 0 1 413.0754 1836.7256)" style="fill:${darkColor}; font-family:'PlayfairDisplay-Black'; font-size:34.5147px;">${callToAction}</text>
	</g>
	</svg>`

	// https://github.com/xyproto/mcbanner/blob/main/convert.go
	var svg2 string = strings.Replace(svg, "${title}", "Explore the World", -1)
	svg2 = strings.Replace(svg2, "${paragraph}", "Have you ever dreamed about travelling around the world? This is your opportunity!", -1)
	svg2 = strings.Replace(svg2, "${callToAction}", "DISCOVER NOW!", -1)
	svg2 = strings.Replace(svg2, "${primaryColor}", "#5E8F7D", -1)
	svg2 = strings.Replace(svg2, "${secondaryColor}", "#BE986B", -1)
	svg2 = strings.Replace(svg2, "${tertiaryColor}", "#EBCCB2", -1)
	svg2 = strings.Replace(svg2, "${lightColor}", "#FFF8F2", -1)
	svg2 = strings.Replace(svg2, "${darkColor}", "#343434", -1)
	svg2 = strings.Replace(svg2, "${titleFont}", "PlayfairDisplay-Black", -1)
	svg2 = strings.Replace(svg2, "${paragraphFont}", "Roboto-Regular", -1)
	svg2 = strings.Replace(svg2, "${image}", "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/7QIuUGhvdG9zaG9wIDMuMAA4QklNBAQAAAAAAhIcAgUAYFdpZGUgYW5nbGUgc2hvdCBvZiBhIHNpbmdsZSB0cmVlIGdyb3dpbmcgdW5kZXIgYSBjbG91ZGVkIHNreSBkdXJpbmcgYSBzdW5zZXQgc3Vycm91bmRlZCBieSBncmFzcxwCaQBgV2lkZSBhbmdsZSBzaG90IG9mIGEgc2luZ2xlIHRyZWUgZ3Jvd2luZyB1bmRlciBhIGNsb3VkZWQgc2t5IGR1cmluZyBhIHN1bnNldCBzdXJyb3VuZGVkIGJ5IGdyYXNzHAJVAGBXaWRlIGFuZ2xlIHNob3Qgb2Yg", -1)

	var input []byte = []byte(svg2)

	// Fill the input with SVG image
	inputFilename := "/Users/<USER>/Sites/conteoodo/go/oi.svg"
	outputFilename := "/Users/<USER>/Sites/conteoodo/go/oi.png"

	// write the .svg file
	if err := ioutil.WriteFile(inputFilename, input, 0600); err != nil {
		panic(err)
	}
	// convert the .svg file to the output format (perhaps png)
	if err := exec.Command("rsvg-convert", inputFilename, "-b", "white", "-f", "png", "-o", outputFilename).Run(); err != nil {
		panic(err)
	}
	// read the converted image
	_, err := ioutil.ReadFile(outputFilename)
	if err != nil {
		panic(err)
	}
	// remove both temporary files
	// if err := os.Remove(inputFilename); err != nil {
	// 	panic(err)
	// }
	// if err = os.Remove(outputFilename); err != nil {
	// 	panic(err)
	// }

	// // create the overlay
	// switch strings.ToLower(item.TextClass) {
	// default:
	// 	overlay, err := goalDomain.defaultOverlay.GenerateOverlay(item.Tags)
	// 	if err != nil {
	// 		fmt.Println("default overlay: ", err)
	// 	}
	// }
	// }
}
