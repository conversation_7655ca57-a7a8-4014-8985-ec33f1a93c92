package payment

import (
	"testing"

	"github.com/conteoodo/go/api/mock"

	"github.com/conteoodo/go/api/data"

	"github.com/golang/mock/gomock"
	"github.com/magiconair/properties/assert"
	"github.com/stripe/stripe-go/v74"
)

func TestConfirmAccount(t *testing.T) {
	t.Run("Should throw error if the header is invalid", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		productDataMock := mock.NewMockIProductData(controller)
		userPaymentDataMock := mock.NewMockIUserPaymentData(controller)
		paymentMock := mock.NewMockIPayment(controller)
		paymentMock.EXPECT().ValidateHeader("signatureHeader", []byte{}).Return(stripe.Event{})

		paymentDomain := NewPaymentDomain(paymentMock, productDataMock, userPaymentDataMock)
		res, err := paymentDomain.ValidatePayment("signatureHeader", []byte{})
		assert.Equal(t, err.Error(), "invalid header")
		assert.Equal(t, res, false)
	})

	t.Run("Should throw error if the type is different from the ones treated", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		productDataMock := mock.NewMockIProductData(controller)
		userPaymentDataMock := mock.NewMockIUserPaymentData(controller)
		paymentMock := mock.NewMockIPayment(controller)
		paymentMock.EXPECT().ValidateHeader("signatureHeader", []byte{}).Return(stripe.Event{
			Type: "charge.succeeded",
		})

		paymentDomain := NewPaymentDomain(paymentMock, productDataMock, userPaymentDataMock)
		res, err := paymentDomain.ValidatePayment("signatureHeader", []byte{})
		assert.Equal(t, err.Error(), "unhandled event type")
		assert.Equal(t, res, false)
	})

	t.Run("Should update as paid when the payment has type payment_intent.succeeded", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		message := []byte(`
{
	"ID": "paymentToken"
}
`)

		productDataMock := mock.NewMockIProductData(controller)
		userPaymentDataMock := mock.NewMockIUserPaymentData(controller)
		userPaymentDataMock.EXPECT().Update("paymentToken", data.UserPayment{Status: "paid"}).Return(true)

		paymentMock := mock.NewMockIPayment(controller)
		paymentMock.EXPECT().ValidateHeader("signatureHeader", []byte{}).Return(stripe.Event{
			Type: "payment_intent.succeeded",
			Data: &stripe.EventData{
				Raw: message,
			},
		})

		paymentDomain := NewPaymentDomain(paymentMock, productDataMock, userPaymentDataMock)
		res, _ := paymentDomain.ValidatePayment("signatureHeader", []byte{})
		assert.Equal(t, res, true)
	})

	t.Run("Should update as declined when the payment has type payment_intent.cancelled", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		message := []byte(`
{
	"ID": "paymentToken"
}
`)

		productDataMock := mock.NewMockIProductData(controller)
		userPaymentDataMock := mock.NewMockIUserPaymentData(controller)
		userPaymentDataMock.EXPECT().Update("paymentToken", data.UserPayment{Status: "declined"}).Return(true)

		paymentMock := mock.NewMockIPayment(controller)
		paymentMock.EXPECT().ValidateHeader("signatureHeader", []byte{}).Return(stripe.Event{
			Type: "payment_intent.cancelled",
			Data: &stripe.EventData{
				Raw: message,
			},
		})

		paymentDomain := NewPaymentDomain(paymentMock, productDataMock, userPaymentDataMock)
		res, _ := paymentDomain.ValidatePayment("signatureHeader", []byte{})
		assert.Equal(t, res, true)
	})
}
