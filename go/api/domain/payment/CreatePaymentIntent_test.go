package payment

import (
	"testing"

	"github.com/conteoodo/go/api/mock"

	"github.com/conteoodo/go/api/data"

	"github.com/golang/mock/gomock"
	"github.com/magiconair/properties/assert"
)

func TestCreatePaymentIntent(t *testing.T) {
	t.Run("Should throw error if the product does not exist", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		productDataMock := mock.NewMockIProductData(controller)
		productDataMock.EXPECT().GetOneByCode("product-1").Return(data.Product{})

		paymentMock := mock.NewMockIPayment(controller)
		userPaymentDataMock := mock.NewMockIUserPaymentData(controller)

		paymentDomain := NewPaymentDomain(paymentMock, productDataMock, userPaymentDataMock)
		_, err := paymentDomain.CreatePaymentIntent("userId", "product-1")
		assert.Equal(t, err.Error(), "product does not exist")
	})

	t.Run("Should add credit to the user.", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		productDataMock := mock.NewMockIProductData(controller)
		productDataMock.EXPECT().GetOneByCode("product-1").Return(data.Product{
			Id:    "productId",
			Code:  "product-1",
			Price: 10,
		})

		paymentMock := mock.NewMockIPayment(controller)
		paymentMock.EXPECT().CreatePaymentIntent(float64(10)).Return("paymentToken")

		userPaymentDataMock := mock.NewMockIUserPaymentData(controller)
		userPaymentDataMock.EXPECT().Create("userId", "productId", "paymentToken")

		paymentDomain := NewPaymentDomain(paymentMock, productDataMock, userPaymentDataMock)
		res, _ := paymentDomain.CreatePaymentIntent("userId", "product-1")
		assert.Equal(t, res, "paymentToken")
	})
}
