package payment

import (
	"encoding/json"
	"errors"
	"fmt"

	"github.com/conteoodo/go/api/infra"

	"github.com/conteoodo/go/api/data"

	"github.com/stripe/stripe-go/v74"
)

// The payment Domain class
// this is where every payment logic related will be located
type IPaymentDomain interface {
	// create a payment intent
	CreatePaymentIntent(userId, code string) (string, error)
	ValidatePayment(signatureHeader string, payload []byte) (bool, error)
}

type PaymentDomain struct {
	payment         infra.IPayment
	productData     data.IProductData
	userPaymentData data.IUserPaymentData
}

// return the payment domain instance
func NewPaymentDomain(
	payment infra.IPayment,
	productData data.IProductData,
	userPaymentData data.IUserPaymentData,
) IPaymentDomain {
	return PaymentDomain{
		payment,
		productData,
		userPaymentData,
	}
}

// create the payment by calling the payment data
func (paymentDomain PaymentDomain) CreatePaymentIntent(userId, code string) (string, error) {
	product := paymentDomain.productData.GetOneByCode(code)
	var err error

	if product.Id == "" {
		err = errors.New("product does not exist")
		return "", err
	}

	paymentToken := paymentDomain.payment.CreatePaymentIntent(product.Price)

	paymentDomain.userPaymentData.Create(userId, product.Id, paymentToken)

	return paymentToken, nil
}

// Validate payment
func (paymentDomain PaymentDomain) ValidatePayment(signatureHeader string, payload []byte) (bool, error) {

	event := paymentDomain.payment.ValidateHeader(signatureHeader, payload)

	if event.Type == "" {
		return false, errors.New("invalid header")
	}

	switch event.Type {
	case "payment_intent.succeeded":
		var paymentIntent stripe.PaymentIntent
		err := json.Unmarshal(event.Data.Raw, &paymentIntent)
		if err != nil {
			fmt.Println("Error parsing webhook JSON:", err)
			return false, err
		}

		paymentDomain.userPaymentData.Update(paymentIntent.ID, data.UserPayment{
			Status: "paid",
		})

	case "payment_intent.cancelled":
		var paymentIntent stripe.PaymentIntent
		err := json.Unmarshal(event.Data.Raw, &paymentIntent)
		if err != nil {
			fmt.Println("Error parsing webhook JSON:", err)
			return false, err
		}

		paymentDomain.userPaymentData.Update(paymentIntent.ID, data.UserPayment{
			Status: "declined",
		})
	default:
		fmt.Println("Unhandled event type:", event.Type)
		return false, errors.New("unhandled event type")
	}

	return true, nil
}
