package user

import (
	"testing"

	"github.com/conteoodo/go/api/constant"

	"github.com/conteoodo/go/api/mock"

	"github.com/conteoodo/go/api/data"

	"github.com/golang/mock/gomock"
	"github.com/magiconair/properties/assert"
)

func TestCreateUser(t *testing.T) {
	t.Run("Should throw error if the user already exists", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		var mockedUser = data.User{
			Id:       "Id",
			FullName: "FullName",
			Email:    "Email",
		}

		userDataMock := mock.NewMockIUserData(controller)
		userDataMock.EXPECT().GetOneByEmail("<EMAIL>").Return(mockedUser)

		userCodeDataMock := mock.NewMockIUserCodeData(controller)
		userLoginDataMock := mock.NewMockIUserLoginData(controller)
		cryptoMock := mock.NewMockICrypto(controller)
		mailMock := mock.NewMockIMail(controller)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		_, err := userDomain.CreateUser("eric", "<EMAIL>", "password")
		assert.Equal(t, err.Error(), "user already exists")
	})

	t.Run("Should create the user, a code and send the email", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		var mockedUser = data.User{
			Id:       "Id",
			FullName: "FullName",
			Email:    "Email",
		}

		userDataMock := mock.NewMockIUserData(controller)
		userDataMock.EXPECT().Create("eric", "<EMAIL>", "encryptedPassword").Return(mockedUser)
		userDataMock.EXPECT().GetOneByEmail("<EMAIL>").Return(data.User{})

		userCodeDataMock := mock.NewMockIUserCodeData(controller)
		userCodeDataMock.EXPECT().Create("Id", "code").Return(data.UserCode{})

		userLoginDataMock := mock.NewMockIUserLoginData(controller)

		cryptoMock := mock.NewMockICrypto(controller)
		cryptoMock.EXPECT().GenerateHash("password").Return("encryptedPassword")
		cryptoMock.EXPECT().GenerateRandomString(6).Return("code")

		mailMock := mock.NewMockIMail(controller)
		mailMock.EXPECT().SendEmail(
			"<EMAIL>",
			"eric",
			constant.ConfirmAccount,
			map[string]interface{}{
				"code": "code",
			},
		).Return(true)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		res, _ := userDomain.CreateUser("eric", "<EMAIL>", "password")
		assert.Equal(t, res, true)
	})
}
