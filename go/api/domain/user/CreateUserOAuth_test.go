package user

import (
	"database/sql"
	"errors"
	"testing"

	"github.com/conteoodo/go/api/infra"

	"github.com/conteoodo/go/api/mock"

	"github.com/conteoodo/go/api/data"

	"github.com/golang/mock/gomock"
	"github.com/magiconair/properties/assert"
)

func TestCreateUserOAuth(t *testing.T) {
	t.Run("Should throw error if the token is invalid", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		cryptoMock := mock.NewMockICrypto(controller)
		cryptoMock.EXPECT().VerifyIdToken("idToken").Return(infra.UserOAuth{}, errors.New(""))

		userDataMock := mock.NewMockIUserData(controller)
		userCodeDataMock := mock.NewMockIUserCodeData(controller)
		userLoginDataMock := mock.NewMockIUserLoginData(controller)
		mailMock := mock.NewMockIMail(controller)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		_, err := userDomain.CreateUserOAuth("idToken")
		assert.Equal(t, err.Error(), "invalid token")
	})

	t.Run("Should throw error if user already exists", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		var mockedUser = data.User{
			Id:       "Id",
			FullName: "FullName",
			Email:    "Email",
			AuthId:   sql.NullString{String: "123", Valid: true}}

		var mockedUserOAuth = infra.UserOAuth{
			Email: "<EMAIL>",
			Name:  "Google",
			Id:    "123",
		}

		userDataMock := mock.NewMockIUserData(controller)
		userDataMock.EXPECT().GetOneByAuthId("123").Return(mockedUser)

		cryptoMock := mock.NewMockICrypto(controller)
		cryptoMock.EXPECT().VerifyIdToken("idToken").Return(mockedUserOAuth, nil)

		userCodeDataMock := mock.NewMockIUserCodeData(controller)
		userLoginDataMock := mock.NewMockIUserLoginData(controller)
		mailMock := mock.NewMockIMail(controller)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		_, err := userDomain.CreateUserOAuth("idToken")
		assert.Equal(t, err.Error(), "user already exists")
	})

	t.Run("Should create the user", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		var mockedUser = data.User{}

		var mockedUserOAuth = infra.UserOAuth{
			Email: "<EMAIL>",
			Name:  "Google",
			Id:    "123",
		}

		userDataMock := mock.NewMockIUserData(controller)
		userDataMock.EXPECT().GetOneByAuthId("123").Return(mockedUser)
		userDataMock.EXPECT().CreateOAuth("Google", "<EMAIL>", "123").Return(mockedUser)

		userCodeDataMock := mock.NewMockIUserCodeData(controller)

		userLoginDataMock := mock.NewMockIUserLoginData(controller)

		cryptoMock := mock.NewMockICrypto(controller)
		cryptoMock.EXPECT().VerifyIdToken("idToken").Return(mockedUserOAuth, nil)

		mailMock := mock.NewMockIMail(controller)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		response, _ := userDomain.CreateUserOAuth("idToken")
		assert.Equal(t, response, true)
	})
}
