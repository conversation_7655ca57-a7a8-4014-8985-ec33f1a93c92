package user

import (
	"database/sql"
	"testing"

	"github.com/conteoodo/go/api/mock"

	"github.com/conteoodo/go/api/data"

	"github.com/golang/mock/gomock"
	"github.com/magiconair/properties/assert"
)

func TestSignIn(t *testing.T) {
	t.Run("Should throw error if the user does not exist", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		userDataMock := mock.NewMockIUserData(controller)
		userDataMock.EXPECT().GetOneByEmail("<EMAIL>").Return(data.User{})

		userCodeDataMock := mock.NewMockIUserCodeData(controller)
		userLoginDataMock := mock.NewMockIUserLoginData(controller)
		cryptoMock := mock.NewMockICrypto(controller)
		mailMock := mock.NewMockIMail(controller)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		_, err := userDomain.SignIn("<EMAIL>", "password")
		assert.Equal(t, err.<PERSON>rror(), "invalid user or password")
	})

	t.Run("Should throw error if the password is wrong", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		var mockedUser = data.User{
			Id:       "Id",
			FullName: "FullName",
			Email:    "Email",
			Password: sql.NullString{String: "hash", Valid: true},
		}

		userDataMock := mock.NewMockIUserData(controller)
		userDataMock.EXPECT().GetOneByEmail("<EMAIL>").Return(mockedUser)

		userCodeDataMock := mock.NewMockIUserCodeData(controller)
		userLoginDataMock := mock.NewMockIUserLoginData(controller)
		cryptoMock := mock.NewMockICrypto(controller)
		cryptoMock.EXPECT().CheckHash("hash", "password").Return(false)

		mailMock := mock.NewMockIMail(controller)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		_, err := userDomain.SignIn("<EMAIL>", "password")
		assert.Equal(t, err.Error(), "invalid user or password")
	})

	t.Run("Should return the jwt token", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		var mockedUser = data.User{
			Id:       "Id",
			FullName: "FullName",
			Email:    "Email",
			Password: sql.NullString{String: "hash", Valid: true},
		}

		userDataMock := mock.NewMockIUserData(controller)
		userDataMock.EXPECT().GetOneByEmail("<EMAIL>").Return(mockedUser)

		userCodeDataMock := mock.NewMockIUserCodeData(controller)

		userLoginDataMock := mock.NewMockIUserLoginData(controller)
		userLoginDataMock.EXPECT().Create("Id").Return(data.UserLogin{})

		cryptoMock := mock.NewMockICrypto(controller)
		cryptoMock.EXPECT().CheckHash("hash", "password").Return(true)
		cryptoMock.EXPECT().GenerateJWT("Id").Return("token")

		mailMock := mock.NewMockIMail(controller)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		token, _ := userDomain.SignIn("<EMAIL>", "password")
		assert.Equal(t, token, "token")
	})
}
