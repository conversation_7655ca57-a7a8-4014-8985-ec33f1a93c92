package user

import (
	"database/sql"
	"testing"

	"github.com/conteoodo/go/api/mock"

	"github.com/conteoodo/go/api/data"

	"github.com/golang/mock/gomock"
	"github.com/magiconair/properties/assert"
)

func TestGetLoggedUser(t *testing.T) {
	t.Run("Should get the logged user", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		var mockedUser = data.User{
			Id:       "Id",
			FullName: "FullName",
			Email:    "Email",
			Password: sql.NullString{String: "Hash", Valid: true},
		}

		userDataMock := mock.NewMockIUserData(controller)
		userDataMock.EXPECT().GetOneById("Id").Return(mockedUser)

		userCodeDataMock := mock.NewMockIUserCodeData(controller)
		userLoginDataMock := mock.NewMockIUserLoginData(controller)
		mailMock := mock.NewMockIMail(controller)

		cryptoMock := mock.NewMockICrypto(controller)
		cryptoMock.EXPECT().ExtractToken("token").Return("Id", nil)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		res, _ := userDomain.GetLoggedUser("token")
		assert.Equal(t, res, mockedUser)
	})
}
