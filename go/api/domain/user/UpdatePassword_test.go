package user

import (
	"database/sql"
	"testing"

	"github.com/conteoodo/go/api/mock"

	"github.com/conteoodo/go/api/data"

	"github.com/golang/mock/gomock"
	"github.com/magiconair/properties/assert"
)

func TestUpdatePassword(t *testing.T) {
	t.Run("Should update the password", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		userDataMock := mock.NewMockIUserData(controller)
		userDataMock.EXPECT().Update("userId", data.User{
			Password: sql.NullString{String: "encryptedPassword", Valid: true},
		}).Return(true)

		userCodeDataMock := mock.NewMockIUserCodeData(controller)
		userLoginDataMock := mock.NewMockIUserLoginData(controller)
		cryptoMock := mock.NewMockICrypto(controller)
		cryptoMock.EXPECT().GenerateHash("password").Return("encryptedPassword")
		mailMock := mock.NewMockIMail(controller)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		res := userDomain.UpdatePassword("userId", "password")
		assert.Equal(t, res, true)
	})
}
