package user

import (
	"database/sql"
	"errors"
	"testing"

	"github.com/conteoodo/go/api/infra"

	"github.com/conteoodo/go/api/mock"

	"github.com/conteoodo/go/api/data"

	"github.com/golang/mock/gomock"
	"github.com/magiconair/properties/assert"
)

func TestSignInOAuth(t *testing.T) {
	t.Run("Should throw error if the token is invalid", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		cryptoMock := mock.NewMockICrypto(controller)
		cryptoMock.EXPECT().VerifyIdToken("idToken").Return(infra.UserOAuth{}, errors.New(""))

		userDataMock := mock.NewMockIUserData(controller)
		userCodeDataMock := mock.NewMockIUserCodeData(controller)
		userLoginDataMock := mock.NewMockIUserLoginData(controller)
		mailMock := mock.NewMockIMail(controller)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		_, err := userDomain.SignInOAuth("idToken")
		assert.Equal(t, err.Error(), "invalid token")
	})

	t.Run("Should throw error if user does not exist", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		var mockedUser = data.User{}

		var mockedUserOAuth = infra.UserOAuth{
			Email: "<EMAIL>",
			Name:  "Google",
			Id:    "123",
		}

		userDataMock := mock.NewMockIUserData(controller)
		userDataMock.EXPECT().GetOneByAuthId("123").Return(mockedUser)

		cryptoMock := mock.NewMockICrypto(controller)
		cryptoMock.EXPECT().VerifyIdToken("idToken").Return(mockedUserOAuth, nil)

		userCodeDataMock := mock.NewMockIUserCodeData(controller)
		userLoginDataMock := mock.NewMockIUserLoginData(controller)
		mailMock := mock.NewMockIMail(controller)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		_, err := userDomain.SignInOAuth("idToken")
		assert.Equal(t, err.Error(), "invalid user")
	})

	t.Run("Should return the jwt token", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		var mockedUser = data.User{
			Id:       "Id",
			FullName: "FullName",
			Email:    "Email",
			AuthId:   sql.NullString{String: "123", Valid: true},
		}

		var mockedUserOAuth = infra.UserOAuth{
			Email: "<EMAIL>",
			Name:  "Google",
			Id:    "123",
		}

		userDataMock := mock.NewMockIUserData(controller)
		userDataMock.EXPECT().GetOneByAuthId("123").Return(mockedUser)

		userCodeDataMock := mock.NewMockIUserCodeData(controller)

		userLoginDataMock := mock.NewMockIUserLoginData(controller)
		userLoginDataMock.EXPECT().Create("Id").Return(data.UserLogin{})

		cryptoMock := mock.NewMockICrypto(controller)
		cryptoMock.EXPECT().VerifyIdToken("idToken").Return(mockedUserOAuth, nil)
		cryptoMock.EXPECT().GenerateJWT("Id").Return("token")

		mailMock := mock.NewMockIMail(controller)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		token, _ := userDomain.SignInOAuth("idToken")
		assert.Equal(t, token, "token")
	})
}
