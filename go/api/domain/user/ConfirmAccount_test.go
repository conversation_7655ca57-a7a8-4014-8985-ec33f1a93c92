package user

import (
	"testing"

	"github.com/conteoodo/go/api/mock"

	"github.com/conteoodo/go/api/data"

	"github.com/golang/mock/gomock"
	"github.com/magiconair/properties/assert"
)

func TestConfirmAccount(t *testing.T) {
	t.Run("Should throw error if the user does not exist", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		userDataMock := mock.NewMockIUserData(controller)
		userDataMock.EXPECT().GetOneByEmail("<EMAIL>").Return(data.User{})

		userLoginDataMock := mock.NewMockIUserLoginData(controller)
		userCodeDataMock := mock.NewMockIUserCodeData(controller)
		cryptoMock := mock.NewMockICrypto(controller)
		mailMock := mock.NewMockIMail(controller)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		_, err := userDomain.ConfirmAccount("<EMAIL>", "code")
		assert.Equal(t, err.<PERSON>rror(), "user does not exist")
	})

	t.Run("Should throw error if the user is already confirmed.", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		var mockedUser = data.User{
			Id:          "Id",
			FullName:    "FullName",
			Email:       "Email",
			IsConfirmed: true,
		}

		userDataMock := mock.NewMockIUserData(controller)
		userDataMock.EXPECT().GetOneByEmail("<EMAIL>").Return(mockedUser)

		userCodeDataMock := mock.NewMockIUserCodeData(controller)
		userLoginDataMock := mock.NewMockIUserLoginData(controller)
		cryptoMock := mock.NewMockICrypto(controller)
		mailMock := mock.NewMockIMail(controller)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		_, err := userDomain.ConfirmAccount("<EMAIL>", "code")
		assert.Equal(t, err.Error(), "user is already confirmed")
	})

	t.Run("Should throw error if the code does not exist", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		var mockedUser = data.User{
			Id:       "Id",
			FullName: "FullName",
			Email:    "Email",
		}

		userDataMock := mock.NewMockIUserData(controller)
		userDataMock.EXPECT().GetOneByEmail("<EMAIL>").Return(mockedUser)

		userCodeDataMock := mock.NewMockIUserCodeData(controller)
		userCodeDataMock.EXPECT().GetLastUserCode("Id").Return(data.UserCode{})

		userLoginDataMock := mock.NewMockIUserLoginData(controller)
		cryptoMock := mock.NewMockICrypto(controller)
		mailMock := mock.NewMockIMail(controller)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		_, err := userDomain.ConfirmAccount("<EMAIL>", "code")
		assert.Equal(t, err.Error(), "code does not exist")
	})

	t.Run("Should throw error if the code is invalid.", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		var mockedUser = data.User{
			Id:       "Id",
			FullName: "FullName",
			Email:    "Email",
		}

		userDataMock := mock.NewMockIUserData(controller)
		userDataMock.EXPECT().GetOneByEmail("<EMAIL>").Return(mockedUser)

		var mockedUserCode = data.UserCode{
			Id:     "Id",
			UserId: "UserId",
			Code:   "Code",
		}

		userCodeDataMock := mock.NewMockIUserCodeData(controller)
		userCodeDataMock.EXPECT().GetLastUserCode("Id").Return(mockedUserCode)

		userLoginDataMock := mock.NewMockIUserLoginData(controller)
		cryptoMock := mock.NewMockICrypto(controller)
		mailMock := mock.NewMockIMail(controller)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		_, err := userDomain.ConfirmAccount("<EMAIL>", "code")
		assert.Equal(t, err.Error(), "invalid code")
	})

	t.Run("Should confirm the user", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		var mockedUser = data.User{
			Id:       "Id",
			FullName: "FullName",
			Email:    "Email",
		}

		userDataMock := mock.NewMockIUserData(controller)
		userDataMock.EXPECT().GetOneByEmail("<EMAIL>").Return(mockedUser)
		userDataMock.EXPECT().Update("Id", data.User{
			IsConfirmed: true,
		}).Return(true)

		var mockedUserCode = data.UserCode{
			Id:     "Id",
			UserId: "UserId",
			Code:   "Code",
		}

		userCodeDataMock := mock.NewMockIUserCodeData(controller)
		userCodeDataMock.EXPECT().GetLastUserCode("Id").Return(mockedUserCode)

		userLoginDataMock := mock.NewMockIUserLoginData(controller)
		cryptoMock := mock.NewMockICrypto(controller)
		mailMock := mock.NewMockIMail(controller)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		res, _ := userDomain.ConfirmAccount("<EMAIL>", "Code")
		assert.Equal(t, res, true)
	})
}
