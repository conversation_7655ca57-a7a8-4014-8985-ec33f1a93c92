package user

import (
	"database/sql"
	"errors"

	"github.com/conteoodo/go/api/infra"

	"github.com/conteoodo/go/api/constant"

	"github.com/conteoodo/go/api/data"
)

// The user Domain class
// this is where every user logic related will be located
type IUserDomain interface {
	// create an user in the user pool
	C<PERSON>User(fullName, email, password string) (bool, error)
	CreateUserOAuth(idToken string) (bool, error)
	ConfirmAccount(email, code string) (bool, error)
	ResendCode(email string) (bool, error)
	SignIn(email, password string) (string, error)
	SignInOAuth(idToken string) (string, error)
	GetLoggedUser(header string) (data.User, error)
	UpdateUser(userId, fullName string) bool
	UpdatePassword(userId, password string) bool
	ResetPassword(email string) bool
	ResetPasswordConfirmation(code, password string) (bool, error)
}

type UserDomain struct {
	userData      data.IUserData
	userCodeData  data.IUserCodeData
	userLoginData data.IUserLoginData
	crypto        infra.ICrypto
	mail          infra.IMail
}

// return the user domain instance
func NewUserDomain(
	userData data.IUserData,
	userCodeData data.IUserCodeData,
	userLoginData data.IUserLoginData,
	crypto infra.ICrypto,
	mail infra.IMail,
) IUserDomain {
	return UserDomain{
		userData,
		userCodeData,
		userLoginData,
		crypto,
		mail,
	}
}

// create the user by calling the user data
func (userDomain UserDomain) CreateUser(fullName, email, password string) (bool, error) {
	existentUser := userDomain.userData.GetOneByEmail(email)
	var err error

	if existentUser.Email != "" {
		err = errors.New("user already exists")
		return false, err
	}

	encryptedPassword := userDomain.crypto.GenerateHash(password)

	// create the user
	user := userDomain.userData.Create(fullName, email, encryptedPassword)

	code := userDomain.crypto.GenerateRandomString(6)

	// create the user code
	userDomain.userCodeData.Create(user.Id, code)

	// send email
	userDomain.mail.SendEmail(
		email,
		fullName,
		constant.ConfirmAccount,
		map[string]string{
			"fullName": fullName,
			"code":     code,
		},
	)

	return true, err
}

// create the user with google auth
// https://developers.google.com/identity/gsi/web/guides/verify-google-id-token
func (userDomain UserDomain) CreateUserOAuth(idToken string) (bool, error) {
	userInfo, err := userDomain.crypto.VerifyIdToken(idToken)

	if err != nil {
		err = errors.New("invalid token")
		return false, err
	}

	existentUser := userDomain.userData.GetOneByAuthId(userInfo.Id)

	if existentUser.Email != "" {
		err = errors.New("user already exists")
		return false, err
	}

	// create the user
	userDomain.userData.CreateOAuth(userInfo.Name, userInfo.Email, userInfo.Id)

	return true, err
}

// resend the code
func (userDomain UserDomain) ResendCode(email string) (bool, error) {
	existentUser := userDomain.userData.GetOneByEmail(email)
	var err error

	if existentUser.Email == "" {
		err = errors.New("user does not exist")
		return false, err
	}

	code := userDomain.crypto.GenerateRandomString(6)

	// create the user code
	userDomain.userCodeData.Create(existentUser.Id, code)

	// send email
	userDomain.mail.SendEmail(
		existentUser.Email,
		existentUser.FullName,
		constant.ConfirmAccount,
		map[string]string{
			"fullName": existentUser.FullName,
			"code":     code,
		},
	)

	return true, err
}

// confirm the user
func (userDomain UserDomain) ConfirmAccount(email, code string) (bool, error) {
	existentUser := userDomain.userData.GetOneByEmail(email)
	var err error

	if existentUser.Email == "" {
		err = errors.New("user does not exist")
		return false, err
	}

	if existentUser.IsConfirmed {
		err = errors.New("user is already confirmed")
		return false, err
	}

	lastCode := userDomain.userCodeData.GetLastUserCode(existentUser.Id)

	if lastCode.Code == "" {
		err = errors.New("code does not exist")
		return false, err
	}

	if lastCode.Code != code {
		err = errors.New("invalid code")
		return false, err
	}

	userDomain.userData.Update(existentUser.Id, data.User{
		IsConfirmed: true,
	})
	return true, err
}

// sign in the user
func (userDomain UserDomain) SignIn(email, password string) (string, error) {
	existentUser := userDomain.userData.GetOneByEmail(email)
	var err error

	if existentUser.Email == "" {
		err = errors.New("invalid user or password")
		return "", err
	}

	isValid := userDomain.crypto.CheckHash(existentUser.Password.String, password)

	if !isValid {
		err = errors.New("invalid user or password")
		return "", err
	}

	// create the user login
	userDomain.userLoginData.Create(existentUser.Id)

	return userDomain.crypto.GenerateJWT(existentUser.Id), err
}

// sign in with google
func (userDomain UserDomain) SignInOAuth(idToken string) (string, error) {
	userInfo, err := userDomain.crypto.VerifyIdToken(idToken)

	if err != nil {
		err = errors.New("invalid token")
		return "", err
	}

	existentUser := userDomain.userData.GetOneByAuthId(userInfo.Id)

	if existentUser.Email == "" {
		err = errors.New("invalid user")
		return "", err
	}

	// create the user login
	userDomain.userLoginData.Create(existentUser.Id)

	return userDomain.crypto.GenerateJWT(existentUser.Id), err
}

// update user
func (userDomain UserDomain) UpdateUser(userId, fullName string) bool {
	var user = data.User{}
	user.FullName = fullName

	userDomain.userData.Update(userId, user)
	return true
}

// update password
func (userDomain UserDomain) UpdatePassword(userId, password string) bool {
	var user = data.User{}
	user.Password = sql.NullString{String: userDomain.crypto.GenerateHash(password), Valid: true}

	userDomain.userData.Update(userId, user)
	return true
}

// get user by id
func (userDomain UserDomain) GetLoggedUser(header string) (data.User, error) {
	id, err := userDomain.crypto.ExtractToken(header)

	if err != nil {
		return data.User{}, err
	}

	existentUser := userDomain.userData.GetOneById(id)
	var err2 error

	if existentUser.Email == "" {
		err2 = errors.New("invalid User")
		return data.User{}, err2
	}

	return existentUser, err2
}

// send password email
func (userDomain UserDomain) ResetPassword(email string) bool {
	existentUser := userDomain.userData.GetOneByEmail(email)

	if existentUser.Email == "" {
		return true
	}

	code := userDomain.crypto.GenerateRandomString(6)

	// create the user code
	userDomain.userCodeData.Create(existentUser.Id, code)

	// send email
	userDomain.mail.SendEmail(
		existentUser.Email,
		existentUser.FullName,
		constant.ResetPassword,
		map[string]string{
			"fullName": existentUser.FullName,
			"code":     code,
		},
	)

	return true
}

// confirm the user
func (userDomain UserDomain) ResetPasswordConfirmation(code, password string) (bool, error) {
	existentUser := userDomain.userData.GetUserByCode(code)
	var err error

	if existentUser.Email == "" {
		err = errors.New("code does not exist")
		return false, err
	}

	var user = data.User{}
	user.Password = sql.NullString{String: userDomain.crypto.GenerateHash(password), Valid: true}

	userDomain.userData.Update(existentUser.Id, user)
	return true, err
}
