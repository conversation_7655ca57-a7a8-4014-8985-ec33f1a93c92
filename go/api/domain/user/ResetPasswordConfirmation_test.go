package user

import (
	"database/sql"
	"testing"

	"github.com/conteoodo/go/api/mock"

	"github.com/conteoodo/go/api/data"

	"github.com/golang/mock/gomock"
	"github.com/magiconair/properties/assert"
)

func TestResetPasswordConfirmation(t *testing.T) {
	t.Run("Should throw if the code does not exist", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		userDataMock := mock.NewMockIUserData(controller)
		userDataMock.EXPECT().GetUserByCode("code").Return(data.User{})

		userCodeDataMock := mock.NewMockIUserCodeData(controller)
		userLoginDataMock := mock.NewMockIUserLoginData(controller)
		cryptoMock := mock.NewMockICrypto(controller)
		mailMock := mock.NewMockIMail(controller)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		_, err := userDomain.ResetPasswordConfirmation("code", "password")
		assert.Equal(t, err.Error(), "code does not exist")
	})

	t.Run("Should change the password", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		var mockedUser = data.User{
			Id:       "Id",
			FullName: "FullName",
			Email:    "Email",
		}

		userDataMock := mock.NewMockIUserData(controller)
		userDataMock.EXPECT().GetUserByCode("code").Return(mockedUser)
		userDataMock.EXPECT().Update("Id", data.User{
			Password: sql.NullString{String: "encryptedPassword", Valid: true},
		}).Return(true)

		userCodeDataMock := mock.NewMockIUserCodeData(controller)
		userLoginDataMock := mock.NewMockIUserLoginData(controller)

		cryptoMock := mock.NewMockICrypto(controller)
		cryptoMock.EXPECT().GenerateHash("password").Return("encryptedPassword")

		mailMock := mock.NewMockIMail(controller)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		res, _ := userDomain.ResetPasswordConfirmation("code", "password")
		assert.Equal(t, res, true)
	})
}
