package user

import (
	"testing"

	"github.com/conteoodo/go/api/constant"

	"github.com/conteoodo/go/api/mock"

	"github.com/conteoodo/go/api/data"

	"github.com/golang/mock/gomock"
	"github.com/magiconair/properties/assert"
)

func TestResetPassword(t *testing.T) {
	t.Run("Should return true if the user does not exist", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		userDataMock := mock.NewMockIUserData(controller)
		userDataMock.EXPECT().GetOneByEmail("<EMAIL>").Return(data.User{})

		userCodeDataMock := mock.NewMockIUserCodeData(controller)
		userLoginDataMock := mock.NewMockIUserLoginData(controller)
		cryptoMock := mock.NewMockICrypto(controller)
		mailMock := mock.NewMockIMail(controller)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		response := userDomain.ResetPassword("<EMAIL>")
		assert.Equal(t, response, true)
	})

	t.Run("Should send the code and create a new record in user code table", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		var mockedUser = data.User{
			Id:       "Id",
			FullName: "FullName",
			Email:    "Email",
		}

		userDataMock := mock.NewMockIUserData(controller)
		userDataMock.EXPECT().GetOneByEmail("<EMAIL>").Return(mockedUser)

		userCodeDataMock := mock.NewMockIUserCodeData(controller)
		userCodeDataMock.EXPECT().Create("Id", "code").Return(data.UserCode{})

		userLoginDataMock := mock.NewMockIUserLoginData(controller)

		cryptoMock := mock.NewMockICrypto(controller)
		cryptoMock.EXPECT().GenerateRandomString(6).Return("code")

		mailMock := mock.NewMockIMail(controller)
		mailMock.EXPECT().SendEmail(
			"Email",
			"FullName",
			constant.ResetPassword,
			map[string]interface{}{
				"code": "code",
			},
		).Return(true)

		userDomain := NewUserDomain(userDataMock, userCodeDataMock, userLoginDataMock, cryptoMock, mailMock)
		res := userDomain.ResetPassword("<EMAIL>")
		assert.Equal(t, res, true)
	})
}
