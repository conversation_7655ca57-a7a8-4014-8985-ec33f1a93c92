package client

import (
	"github.com/conteoodo/go/api/data"
)

// The client Domain class
// this is where every client logic related will be located
type IClientDomain interface {
	CreateClient(userId, name, logo, primaryColor, secondaryColor string) data.Client
}

type ClientDomain struct {
	clientData data.IClientData
}

// return the client domain instance
func NewClientDomain(
	clientData data.IClientData,
) IClientDomain {
	return ClientDomain{
		clientData,
	}
}

// save the client by calling the client data
func (clientDomain ClientDomain) CreateClient(userId, name, logo, primaryColor, secondaryColor string) data.Client {
	res := clientDomain.clientData.Create(userId, name, logo, primaryColor, secondaryColor)

	return res
}
