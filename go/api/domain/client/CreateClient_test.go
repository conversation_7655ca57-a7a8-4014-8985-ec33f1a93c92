package client

import (
	"testing"

	"github.com/conteoodo/go/api/mock"

	"github.com/conteoodo/go/api/data"

	"github.com/golang/mock/gomock"
	"github.com/magiconair/properties/assert"
)

func TestCreateClient(t *testing.T) {
	t.Run("Should create the client", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		var client = data.Client{
			UserId:         "userId",
			Name:           "name",
			Logo:           "logo",
			PrimaryColor:   "primaryColor",
			SecondaryColor: "secondaryColor",
		}

		clientDataMock := mock.NewMockIClientData(controller)
		clientDataMock.EXPECT().Create(
			"userId",
			"name",
			"logo",
			"primaryColor",
			"secondaryColor",
		).Return(client)

		clientDomain := NewClientDomain(clientDataMock)
		res := clientDomain.CreateClient(
			"userId", "name", "logo", "primaryColor", "secondaryColor",
		)
		assert.Equal(t, res, client)
	})
}
