package campaign

import (
	"testing"
	"time"

	"github.com/conteoodo/go/api/mock"

	"github.com/conteoodo/go/api/data"

	"github.com/golang/mock/gomock"
	"github.com/magiconair/properties/assert"
)

func TestGetCampaigns(t *testing.T) {
	t.Run("Should get the campaigns by user", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		initialDate := time.Now()

		mockedCampaigns := []data.Campaign{
			{
				Id:          "Id",
				UserId:      "UserId",
				GoalId:      "GoalId",
				InitialDate: initialDate,
				FinalDate:   initialDate,
			},
		}

		campaignDataMock := mock.NewMockICampaignData(controller)
		campaignDataMock.EXPECT().Search(
			"userId",
		).Return(mockedCampaigns)

		campaignDomain := NewCampaignDomain(campaignDataMock)
		res := campaignDomain.GetCampaigns(
			"userId",
		)
		assert.Equal(t, res, mockedCampaigns)
	})
}
