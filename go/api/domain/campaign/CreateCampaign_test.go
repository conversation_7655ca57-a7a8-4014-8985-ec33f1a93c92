package campaign

import (
	"testing"
	"time"

	"github.com/conteoodo/go/api/mock"

	"github.com/conteoodo/go/api/data"

	"github.com/golang/mock/gomock"
	"github.com/magiconair/properties/assert"
)

func TestCreateCampaign(t *testing.T) {
	t.Run("Should create the campaign", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		initialDate := time.Now()

		var arr []string
		arr = append(arr, "item	")

		var mockedCampaign = data.Campaign{
			Id:              "Id",
			UserId:          "UserId",
			GoalId:          "GoalId",
			InitialDate:     initialDate,
			FinalDate:       initialDate,
			SocialNetworks:  "SocialNetworks",
			PostDailyAmount: 1,
			PostHours:       "PostHours",
			WeekDays:        "WeekDays",
			Formats:         "Formats",
			Styles:          "Styles",
		}

		campaignDataMock := mock.NewMockICampaignData(controller)
		campaignDataMock.EXPECT().Create(
			"userId",
			"goalId",
			initialDate,
			initialDate,
			arr,
			1,
			arr,
			arr,
			arr,
			arr,
		).Return(mockedCampaign)

		campaignDomain := NewCampaignDomain(campaignDataMock)
		res := campaignDomain.CreateCampaign(
			"userId",
			"goalId",
			initialDate,
			initialDate,
			arr,
			1,
			arr,
			arr,
			arr,
			arr,
		)
		assert.Equal(t, res, mockedCampaign)
	})
}
