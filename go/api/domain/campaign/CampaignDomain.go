package campaign

import (
	"time"

	"github.com/conteoodo/go/api/data"
)

// The campaign Domain class
// this is where every campaign logic related will be located
type ICampaignDomain interface {
	CreateCampaign(
		userId string,
		goalId string,
		initialDate time.Time,
		finalDate time.Time,
		socialNetworks []string,
		postDailyAmount int,
		postHours []string,
		weekDays []string,
		formats []string,
		styles []string,
	) data.Campaign
	GetCampaigns(
		userId string,
	) []data.Campaign
}

type CampaignDomain struct {
	campaignData data.ICampaignData
}

// return the campaign domain instance
func NewCampaignDomain(
	campaignData data.ICampaignData,
) ICampaignDomain {
	return CampaignDomain{
		campaignData,
	}
}

// create the campaign by calling the campaign data
func (campaignDomain CampaignDomain) CreateCampaign(
	userId string,
	goalId string,
	initialDate time.Time,
	finalDate time.Time,
	socialNetworks []string,
	postDailyAmount int,
	postHours []string,
	weekDays []string,
	formats []string,
	styles []string,
) data.Campaign {
	return campaignDomain.campaignData.Create(
		userId,
		goalId,
		initialDate,
		finalDate,
		socialNetworks,
		postDailyAmount,
		postHours,
		weekDays,
		formats,
		styles,
	)
}

// get campaigns
func (campaignDomain CampaignDomain) GetCampaigns(
	userId string,
) []data.Campaign {
	return campaignDomain.campaignData.Search(
		userId,
	)
}
