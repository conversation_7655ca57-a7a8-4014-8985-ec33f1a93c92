package team

import (
	"testing"

	"github.com/conteoodo/go/api/mock"

	"github.com/conteoodo/go/api/data"

	"github.com/golang/mock/gomock"
)

func TestCreateTeam(t *testing.T) {

	t.Run("Should create the team.", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		var team = data.Team{
			UserId: "userId",
			Name:   "name",
		}

		teamDataMock := mock.NewMockITeamData(controller)
		teamDataMock.EXPECT().Create("userId", "team name").Return(team)

		teamInviteDataMock := mock.NewMockITeamInviteData(controller)
		mailMock := mock.NewMockIMail(controller)

		teamDomain := NewTeamDomain(teamDataMock, teamInviteDataMock, mailMock)
		teamDomain.CreateTeam("userId", "team name")
	})
}
