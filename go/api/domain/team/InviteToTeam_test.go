package team

import (
	"testing"

	"github.com/conteoodo/go/api/constant"

	"github.com/conteoodo/go/api/mock"

	"github.com/conteoodo/go/api/data"

	"github.com/golang/mock/gomock"
)

func TestInviteToTeam(t *testing.T) {

	t.Run("Should invite to the team.", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		var teamInvite = data.TeamInvite{
			TeamId: "teamId",
			Name:   "name",
			Email:  "email",
		}

		teamDataMock := mock.NewMockITeamData(controller)

		teamInviteDataMock := mock.NewMockITeamInviteData(controller)
		teamInviteDataMock.EXPECT().Create("teamId", "name", "email").Return(teamInvite)

		mailMock := mock.NewMockIMail(controller)
		mailMock.EXPECT().SendEmail(
			"email",
			"name",
			constant.InviteToTeam,
			map[string]interface{}{
				"name": "name",
			},
		).Return(true)

		teamDomain := NewTeamDomain(teamDataMock, teamInviteDataMock, mailMock)
		teamDomain.InviteToTeam("teamId", "name", "email")
	})
}
