package team

import (
	"github.com/conteoodo/go/api/infra"

	"github.com/conteoodo/go/api/constant"

	"github.com/conteoodo/go/api/data"
)

// The team Domain class
// this is where every team logic related will be located
type ITeamDomain interface {
	// create a team
	CreateTeam(userId, name string) bool
	InviteToTeam(teamId, name, email string) bool
}

type TeamDomain struct {
	teamData       data.ITeamData
	teamInviteData data.ITeamInviteData
	mail           infra.IMail
}

// return the team domain instance
func NewTeamDomain(
	teamData data.ITeamData,
	teamInviteData data.ITeamInviteData,
	mail infra.IMail,
) ITeamDomain {
	return TeamDomain{
		teamData,
		teamInviteData,
		mail,
	}
}

// create the team by calling the team data
func (teamDomain TeamDomain) CreateTeam(userId, name string) bool {
	teamDomain.teamData.Create(userId, name)

	return true
}

// send an invitation to the team
func (teamDomain TeamDomain) InviteToTeam(teamId, name, email string) bool {

	// send email
	teamDomain.mail.SendEmail(
		email,
		name,
		constant.InviteToTeam,
		map[string]string{
			"name": name,
		},
	)

	teamDomain.teamInviteData.Create(teamId, name, email)

	return true
}
