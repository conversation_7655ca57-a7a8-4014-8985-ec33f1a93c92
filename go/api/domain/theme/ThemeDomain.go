package theme

import (
	"github.com/conteoodo/go/api/data"
)

// The theme Domain class
// this is where every theme logic related will be located
type IThemeDomain interface {
	GetThemes(page int) []data.Theme
}

type ThemeDomain struct {
	themeData data.IThemeData
}

// return the theme domain instance
func NewThemeDomain(
	themeData data.IThemeData,
) IThemeDomain {
	return ThemeDomain{
		themeData,
	}
}

// get paginated themes
func (themeDomain ThemeDomain) GetThemes(page int) []data.Theme {
	themes := themeDomain.themeData.GetThemes(page)
	return themes
}
