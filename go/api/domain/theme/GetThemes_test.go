package theme

import (
	"testing"

	"github.com/conteoodo/go/api/mock"

	"github.com/conteoodo/go/api/data"

	"github.com/golang/mock/gomock"
	"github.com/magiconair/properties/assert"
)

func TestGetThemes(t *testing.T) {
	t.Run("Should get the themes", func(t *testing.T) {
		controller := gomock.NewController(t)
		defer controller.Finish()

		var parsedThemes []data.Theme
		parsedThemes = append(parsedThemes, data.Theme{
			Id:   "Id",
			Name: "Name",
		})

		themeDataMock := mock.NewMockIThemeData(controller)
		themeDataMock.EXPECT().GetThemes(1).Return(parsedThemes)

		themeDomain := NewThemeDomain(themeDataMock)
		res := themeDomain.GetThemes(1)
		assert.Equal(t, res, parsedThemes)
	})
}
