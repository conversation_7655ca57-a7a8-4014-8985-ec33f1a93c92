# Installing

```sh
go get -u github.com/aws/aws-sdk-go/aws
make install
```

# Create a migration 

```sh
cd migrations/
~/go/bin/goose create user sql
```

Run migration:

```sh
make migration-run
```

# Tests

How to run the test:

```sh
~/go/bin/goose postgres "postgresql://root@127.0.0.1:26257/cont-test?sslmode=disable" up
go test -v ./...
```

How to generate mock:

```sh
~/go/bin/mockgen -source=api/data/CampaignData.go -destination=api/mock/CampaignDataMock.go -package=mock
```

# Development

Running the server:

```sh
make run
```

Access [http://localhost:8080](http://localhost:8080) in order to play with it

Generate the code:

```sh
make gql-generate
```

Database:

https://dbdiagram.io/d/63741f4ec9abfc611172efa6

Initial database test seed:


```sh
docker cp ./seed.sql go-db-1:/docker-entrypoint-initdb.d/seed.sql
docker exec -ti go-db-1 sh -c "/cockroach/cockroach sql -d cont --insecure < /docker-entrypoint-initdb.d/seed.sql"
```