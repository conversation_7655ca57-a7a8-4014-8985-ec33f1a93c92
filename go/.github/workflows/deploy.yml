name: Deploy

on:
  push:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Log in to the Container registry
        uses: docker/login-action@v2
        with:
          registry: sjc.vultrcr.com/conteoodo
          username: ${{ secrets.VULTR_RESGISTRY_USERNAME }}
          password: ${{ secrets.VULTR_RESGISTRY_PASSWORD }}

      - name: Install
        run: |
          curl --create-dirs -o cert/root.crt 'https://cockroachlabs.cloud/clusters/75e77815-0017-48b9-90e9-2184e9b6ffb3/cert'

      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          images: conteoodo/go
          tags: |
            sjc.vultrcr.com/conteoodo/go:latest
            sjc.vultrcr.com/conteoodo/go:${{ github.sha }}
  # deploy:
  #   name: Deploy
  #   needs: build
  #   runs-on: ubuntu-latest
  #   steps:
  #     - name: Set the Kubernetes context
  #       uses: azure/k8s-set-context@v2
  #       with:
  #         method: service-account
  #         k8s-url: https://41c1357f-1eab-452f-b8c8-95df16698441.vultr-k8s.com:6443
  #         k8s-secret: ${{ secrets.KUBERNETES_SECRET }}
  #     - name: Checkout source code
  #       uses: actions/checkout@v3
  #     - name: Deploy to the Kubernetes cluster
  #       uses: azure/k8s-deploy@v1
  #       with:
  #         namespace: default
  #         manifests: |
  #           kubernetes/api-deploy.yaml
  #           kubernetes/api-service.yaml
  #         images: |
  #           ghcr.io/conteoodo/go:${{ github.sha }}