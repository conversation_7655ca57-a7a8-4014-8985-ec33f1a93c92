# name: Test

# on:
#   push:

# jobs:
#   test:
#     runs-on: ubuntu-latest
#     timeout-minutes: 5

#     steps:
#       - name: Checkout repository
#         uses: actions/checkout@v2

#       - name: Go
#         uses: actions/setup-go@v2
#         with:
#           go-version: 1.17

#       - name: Build
#         run: go build -v ./...

#       - name: Run go vet
#         run: go vet ./...

#       - name: Install staticcheck
#         run: go install honnef.co/go/tools/cmd/staticcheck@latest

#       - name: Run staticcheck
#         run: staticcheck ./...

#       - name: Install golint
#         run: go install golang.org/x/lint/golint@latest

#       - name: Run golint
#         run: golint ./...

#       - name: Run tests
#         run: go test -race -vet=off ./...