https://www.bacancytechnology.com/blog/golang-jwt
https://app.mailjet.com/signin
https://webrtc.ventures/2022/10/building-a-video-chat-with-the-amazon-chime-sdk/
https://repurpose.io/
https://stackoverflow.com/questions/38299930/how-to-add-a-simple-text-label-to-an-image-in-go
https://github.com/gographics/imagick
https://www.golangprograms.com/how-to-add-watermark-or-merge-two-image.html

//SignUp_test.go
package graph

import (
	"api/api"
	"api/graph/generated"
	"testing"

	"github.com/99designs/gqlgen/client"
	"github.com/99designs/gqlgen/graphql/handler"
	"github.com/magiconair/properties/assert"
)

// https://thacoon.com/posts/gqlgen/
func TestSignUpSuccess(t *testing.T) {
	api.Init()
	// test.GetFixture().Truncate([]string{"user"})

	t.Run("Should create the user", func(t *testing.T) {
		srv := handler.NewDefaultServer(generated.NewExecutableSchema(generated.Config{Resolvers: &Resolver{}}))
		c := client.New(srv)

		var resp struct {
			SignUp bool
		}

		c.Post(`
			mutation ($fullName: String!, $email: String!, $password: String!) {
				signUp(input: {
					fulName: $fullName,
					email: $email,
					password: $password		
				})
			}`,
			&resp,
			client.Var("fullName", "Full Name"),
			client.Var("email", "<EMAIL>"),
			client.Var("password", "password"),
		)

		assert.Equal(t, true, resp.SignUp)
	})
}
