-- +goose Up
-- +goose StatementBegin
CREATE TYPE "social" AS ENUM (
  'facebook',
  'instagram',
  'linkedin',
  'whatsapp',
  'youtube',
  'twitter'
);

CREATE TABLE "user" (
  "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  "full_name" varchar NOT NULL,
  "email" varchar NOT NULL,
  "password" varchar,
  "auth_id" varchar,
  "is_confirmed" BOOL NOT NULL DEFAULT false,
  "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "deleted_at" TIMESTAMP
);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE "user";
-- +goose StatementEnd
