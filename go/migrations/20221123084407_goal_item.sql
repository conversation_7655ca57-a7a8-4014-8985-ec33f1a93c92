-- +goose Up
-- +goose StatementBegin
CREATE TABLE "goal_item" (
  "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  "goal_id" uuid NOT NULL,
  "date" TIMESTAMP,
  "title" varchar NOT NULL,
  "prompt" varchar NOT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "deleted_at" TIMESTAMP
);

ALTER TABLE "goal_item" ADD FOREIGN KEY ("goal_id") REFERENCES "goal" ("id");
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE "goal_item";
-- +goose StatementEnd
