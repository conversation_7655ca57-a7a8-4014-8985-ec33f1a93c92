-- +goose Up
-- +goose StatementBegin
CREATE TYPE "payment_status" AS ENUM (
  'pending',
  'paid',
  'declined'
);

CREATE TABLE "user_payment" (
  "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  "user_id" uuid NOT NULL,
  "product_id" uuid NOT NULL,
  "payment_token" string NOT NULL,
  "status" payment_status NOT NULL DEFAULT 'pending',
  "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "deleted_at" TIMESTAMP
);

ALTER TABLE "user_payment" ADD FOREIGN KEY ("user_id") REFERENCES "user" ("id");
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE "user_payment";
-- +goose StatementEnd
