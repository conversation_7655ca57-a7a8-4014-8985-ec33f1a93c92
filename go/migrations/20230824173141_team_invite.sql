-- +goose Up
-- +goose StatementBegin
CREATE TABLE "team_invite" (
  "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  "team_id" uuid NOT NULL,
  "email" string NOT NULL,
  "name" string NOT NULL,
  "accepted_at" TIMESTAMP,
  "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "deleted_at" TIMESTAMP
);

ALTER TABLE "team_invite" ADD FOREIGN KEY ("team_id") REFERENCES "team" ("id");
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE "team_invite";
-- +goose StatementEnd