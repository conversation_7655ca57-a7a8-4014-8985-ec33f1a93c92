-- +goose Up
-- +goose StatementBegin
CREATE TABLE "story" (
  "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  "goal_item_id" uuid NOT NULL,
  "title" varchar NOT NULL,
  "image" varchar NOT NULL,
  "description" varchar NOT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "deleted_at" TIMESTAMP
);

ALTER TABLE "story" ADD FOREIGN KEY ("goal_item_id") REFERENCES "goal_item" ("id");
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE "story";
-- +goose StatementEnd
