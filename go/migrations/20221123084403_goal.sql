-- +goose Up
-- +goose StatementBegin
CREATE TABLE "goal" (
  "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  "theme_id" uuid NOT NULL,
  "language" varchar NOT NULL,
  "name" varchar NOT NULL,
  "image_class" varchar NOT NULL,
  "text_class" varchar NOT NULL,
  "prefix_prompt" varchar,
  "negative_prompt" varchar,
  "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "deleted_at" TIMESTAMP
);

ALTER TABLE "goal" ADD FOREIGN KEY ("theme_id") REFERENCES "theme" ("id");
ALTER TABLE "campaign" ADD FOREIGN KEY ("goal_id") REFERENCES "goal" ("id");
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE "goal";
-- +goose StatementEnd
