-- +goose Up
-- +goose StatementBegin
CREATE TABLE "client" (
  "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  "user_id" uuid NOT NULL,
  "name" varchar NOT NULL,
  "logo" varchar NOT NULL,
  "primary_color" varchar NOT NULL,
  "secondary_color" varchar NOT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "deleted_at" TIMESTAMP
);

ALTER TABLE "client" ADD FOREIGN KEY ("user_id") REFERENCES "user" ("id");
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE "client";
-- +goose StatementEnd
