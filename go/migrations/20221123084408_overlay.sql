-- +goose Up
-- +goose StatementBegin
CREATE TABLE "overlay" (
  "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  "image" varchar NOT NULL,
  "description" varchar NOT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "deleted_at" TIMESTAMP
);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE "overlay";
-- +goose StatementEnd
