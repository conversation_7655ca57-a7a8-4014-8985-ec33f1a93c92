-- +goose Up
-- +goose StatementBegin
CREATE TABLE "story_social" (
  "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  "story_id" uuid NOT NULL,
  "social_network" social NOT NULL,
  "image" varchar NOT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "deleted_at" TIMESTAMP
);

ALTER TABLE "story_social" ADD FOREIGN KEY ("story_id") REFERENCES "story" ("id");
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE "story_social";
-- +goose StatementEnd
