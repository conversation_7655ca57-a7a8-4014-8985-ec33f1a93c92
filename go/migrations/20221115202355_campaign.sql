-- +goose Up
-- +goose StatementBegin
CREATE TABLE "campaign" (
  "id" uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  "user_id" uuid NOT NULL,
  "initial_date" TIMESTAMP NOT NULL,
  "final_date" TIMES<PERSON>MP NOT NULL,
  "goal_id" uuid NOT NULL,
  "post_daily_amount" int NOT NULL,
  "post_hours" json NOT NULL,
  "week_days" json NOT NULL,
  "formats" json NOT NULL,
  "styles" json NOT NULL,
  "social_networks" json NOT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "deleted_at" TIMESTAMP
);

ALTER TABLE "campaign" ADD FOREIGN KEY ("user_id") REFERENCES "user" ("id");
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE "campaign";
-- +goose StatementEnd
