install:
	go mod tidy
	go mod vendor

run:
	DATABASE_HOST=localhost \
	DATABASE_USERNAME=root \
	DATABASE_PASSWORD= \
	DATABASE_DBNAME=cont \
	DATABASE_ISSSL=false \
	CRYPTO_SECRET=jwt \
	EMAIL_FROMEMAIL="<EMAIL>" \
	EMAIL_FROMNAME="Conteoodo DEV" \
	IMAGE_STABILITY_KEY= \
	TEXT_OPENAI_SECRET_KEY= \
	PAYMENT_STRIPE_KEY= \
	PAYMENT_STRIPE_ENDPOINT= \
	GOOGLE_CLIENT_ID=***********-hie4p879cnpr1sk6mhtanbim9u9saohg.apps.googleusercontent.com \
	GOOGLE_IOS_CLIENT_ID=***********-b4th3s0j2dt3fejsmahiptm6mf84d49i.apps.googleusercontent.com \
	GOOGLE_ANDROID_CLIENT_ID=***********-7tvu4t92ti6alff70d2q47trj091gaj7.apps.googleusercontent.com \
	go run server.go

migration-run:
	cd migrations/ && ~/go/bin/goose postgres "postgresql://root@127.0.0.1:26257/cont?sslmode=disable" up

gql-generate:
	cd graph && go run github.com/99designs/gqlgen generate

sync-template:
	aws --profile=conteoodo --region=sa-east-1 ses update-template --cli-input-json file://template/confirmAccount.json
	aws --profile=conteoodo --region=sa-east-1 ses update-template --cli-input-json file://template/resetPassword.json
