package graph

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.45

import (
	"context"
	"fmt"

	"github.com/conteoodo/go/api"
	"github.com/conteoodo/go/api/middleware"
	"github.com/conteoodo/go/api/utils"
	"github.com/conteoodo/go/graph/model"
)

// SignUp is the resolver for the signUp field.
func (r *mutationResolver) SignUp(ctx context.Context, input model.SignUpDao) (bool, error) {
	userDomain := api.GetUserDomain()
	response, err := userDomain.CreateUser(input.FullName, input.Email, input.Password)
	return response, err
}

// SignUpOAuth is the resolver for the signUpOAuth field.
func (r *mutationResolver) SignUpOAuth(ctx context.Context, input model.SignUpOAuthDao) (bool, error) {
	userDomain := api.GetUserDomain()
	response, err := userDomain.CreateUserOAuth(input.IDToken)
	return response, err
}

// ResendCode is the resolver for the resendCode field.
func (r *mutationResolver) ResendCode(ctx context.Context, input model.ResendCodeDao) (bool, error) {
	userDomain := api.GetUserDomain()
	response, err := userDomain.ResendCode(input.Email)
	return response, err
}

// ConfirmAccount is the resolver for the confirmAccount field.
func (r *mutationResolver) ConfirmAccount(ctx context.Context, input model.ConfirmAccountDao) (bool, error) {
	userDomain := api.GetUserDomain()
	response, err := userDomain.ConfirmAccount(input.Email, input.Code)
	return response, err
}

// SignIn is the resolver for the signIn field.
func (r *mutationResolver) SignIn(ctx context.Context, input model.SignInDao) (model.SignInDto, error) {
	userDomain := api.GetUserDomain()
	token, err := userDomain.SignIn(input.Email, input.Password)
	return model.SignInDto{
		Token: token,
	}, err
}

// SignInOAuth is the resolver for the signInOAuth field.
func (r *mutationResolver) SignInOAuth(ctx context.Context, input model.SignInOAuthDao) (model.SignInDto, error) {
	userDomain := api.GetUserDomain()
	token, err := userDomain.SignInOAuth(input.IDToken)
	return model.SignInDto{
		Token: token,
	}, err
}

// ResetPassword is the resolver for the resetPassword field.
func (r *mutationResolver) ResetPassword(ctx context.Context, input model.ResetPasswordDao) (bool, error) {
	userDomain := api.GetUserDomain()
	response := userDomain.ResetPassword(input.Email)
	return response, nil
}

// ResetPasswordConfirmation is the resolver for the resetPasswordConfirmation field.
func (r *mutationResolver) ResetPasswordConfirmation(ctx context.Context, input model.ResetPasswordConfirmationDao) (bool, error) {
	userDomain := api.GetUserDomain()
	response, err := userDomain.ResetPasswordConfirmation(input.Code, input.Password)
	return response, err
}

// CreateCampaign is the resolver for the createCampaign field.
func (r *mutationResolver) CreateCampaign(ctx context.Context, input model.CreateCampaignDao) (bool, error) {
	user := middleware.GetContext(ctx)

	campaignDomain := api.GetCampaignDomain()
	campaignDomain.CreateCampaign(
		user.Id,
		input.GoalID,
		input.InitialDate,
		input.FinalDate,
		input.SocialNetworks,
		input.PostDailyAmount,
		input.PostHours,
		input.WeekDays,
		input.Formats,
		input.Styles,
	)

	return true, nil
}

// AddCredit is the resolver for the addCredit field.
func (r *mutationResolver) AddCredit(ctx context.Context, input model.AddCreditDao) (string, error) {
	user := middleware.GetContext(ctx)

	paymentDomain := api.GetPaymentDomain()
	paymentToken, err := paymentDomain.CreatePaymentIntent(user.Id, input.Code)
	return paymentToken, err
}

// UpdateUser is the resolver for the updateUser field.
func (r *mutationResolver) UpdateUser(ctx context.Context, input model.UpdateUserDao) (bool, error) {
	user := middleware.GetContext(ctx)
	userDomain := api.GetUserDomain()

	value := userDomain.UpdateUser(user.Id, *input.FullName)
	return value, nil
}

// UpdatePassword is the resolver for the updatePassword field.
func (r *mutationResolver) UpdatePassword(ctx context.Context, input model.UpdatePasswordDao) (bool, error) {
	user := middleware.GetContext(ctx)
	userDomain := api.GetUserDomain()

	value := userDomain.UpdatePassword(user.Id, *input.Password)
	return value, nil
}

// CreateTeam is the resolver for the createTeam field.
func (r *mutationResolver) CreateTeam(ctx context.Context, input *model.CreateTeamDao) (bool, error) {
	user := middleware.GetContext(ctx)

	teamDomain := api.GetTeamDomain()
	response := teamDomain.CreateTeam(user.Id, input.Name)
	return response, nil
}

// InviteToTeam is the resolver for the inviteToTeam field.
func (r *mutationResolver) InviteToTeam(ctx context.Context, id string, input *model.InviteToTeamDao) (bool, error) {
	teamDomain := api.GetTeamDomain()
	response := teamDomain.InviteToTeam(id, input.Name, input.Email)
	return response, nil
}

// CreateClient is the resolver for the createClient field.
func (r *mutationResolver) CreateClient(ctx context.Context, input *model.CreateClientDao) (model.Client, error) {
	user := middleware.GetContext(ctx)

	clientDomain := api.GetClientDomain()
	response := clientDomain.CreateClient(user.Id, input.Name, *input.Logo, *input.PrimaryColor, *input.SecondaryColor)
	return model.Client{
		ID:             response.Id,
		Name:           response.Name,
		Logo:           &response.Logo,
		PrimaryColor:   &response.PrimaryColor,
		SecondaryColor: &response.SecondaryColor,
	}, nil
}

// GenerateCampaign is the resolver for the generateCampaign field.
func (r *mutationResolver) GenerateCampaign(ctx context.Context, input *model.GenerateCampaignDao) (bool, error) {
	panic(fmt.Errorf("not implemented: GenerateCampaign - generateCampaign"))
}

// Campaigns is the resolver for the campaigns field.
func (r *queryResolver) Campaigns(ctx context.Context) ([]*model.Campaign, error) {
	// user := middleware.GetContext(ctx)

	// campaignDomain := api.GetCampaignDomain()
	// campaigns := campaignDomain.GetCampaigns(user.Id)

	return nil, nil
}

// LoggedUser is the resolver for the loggedUser field.
func (r *queryResolver) LoggedUser(ctx context.Context) (*model.User, error) {
	user := middleware.GetContext(ctx)
	return &model.User{
		ID:       user.Id,
		FullName: user.FullName,
		Email:    user.Email,
	}, nil
}

// GetThemes is the resolver for the getThemes field.
func (r *queryResolver) GetThemes(ctx context.Context, input model.GetThemesDao) ([]*model.Theme, error) {
	themeDomain := api.GetThemeDomain()

	page := 1
	if input.Page == nil {
		input.Page = &page
	}

	themes := themeDomain.GetThemes(*input.Page)

	var parsedThemes []*model.Theme
	for _, theme := range themes {
		parsedTheme, _ := utils.TypeConverter[model.Theme](theme)
		parsedThemes = append(parsedThemes, &parsedTheme)
	}

	return parsedThemes, nil
}

// GetGoals is the resolver for the getGoals field.
func (r *queryResolver) GetGoals(ctx context.Context, input model.GetGoalsDao) ([]*model.Goal, error) {
	goalDomain := api.GetGoalDomain()

	page := 1
	if input.Page == nil {
		input.Page = &page
	}

	goals := goalDomain.GetGoals(input.ThemeID, *input.Page)

	var parsedGoals []*model.Goal
	for _, goal := range goals {
		parsedGoal, _ := utils.TypeConverter[model.Goal](goal)
		parsedGoals = append(parsedGoals, &parsedGoal)
	}

	return parsedGoals, nil
}

// Mutation returns MutationResolver implementation.
func (r *Resolver) Mutation() MutationResolver { return &mutationResolver{r} }

// Query returns QueryResolver implementation.
func (r *Resolver) Query() QueryResolver { return &queryResolver{r} }

type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }

// !!! WARNING !!!
// The code below was going to be deleted when updating resolvers. It has been copied here so you have
// one last chance to move it out of harms way if you want. There are two reasons this happens:
//   - When renaming or deleting a resolver the old code will be put in here. You can safely delete
//     it when you're done.
//   - You have helper methods in this file. Move them out to keep these resolver files clean.
func (r *queryResolver) GetTGoals(ctx context.Context, input model.GetGoalsDao) ([]*model.Goal, error) {
	panic(fmt.Errorf("not implemented: GetTGoals - getTGoals"))
}
