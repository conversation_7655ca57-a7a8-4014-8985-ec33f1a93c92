scalar Time

directive @isAuthenticated on FIELD_DEFINITION

type SignInDTO {
  token: String!
}

type User {
	id: String!
	fullName: String!
	email: String!
}

type Client {
	id: String!
	name: String!
	logo: String
	primaryColor: String
	secondaryColor: String
}

type Story {
  id: ID!
  image: String!
  headerText: String!
  bottomText: String!
  hashTags: [String!]!
}

type Campaign {
  id: ID
  initialDate: Time
  finalDate: Time
  socialNetworks: [SocialNetworkType!]!
  stories: [Story!]!
}

type Theme {
  id: ID
  name: String
}

type Goal {
  id: ID
  themeId: ID
  name: String
}

input GetThemesDAO {
  page: Int
}

input GetGoalsDAO {
  themeId: ID!
  page: Int
}

input SignUpDAO {
  fullName: String!
  email: String!
  password: String!
}

input SignUpOAuthDAO {
  idToken: String!
}

input ResendCodeDAO {
  email: String!
}

input ConfirmAccountDAO {
  email: String!
  code: String!
}

input AddCreditDAO {
  code: String!
}

input SignInDAO {
  email: String!
  password: String!
}

input SignInOAuthDAO {
  idToken: String!
}

input Reset<PERSON>asswordDAO {
  email: String!
}

input ResetPasswordConfirmationDAO {
  code: String!
  password: String!
}

input CreateTeamDAO {
  name: String!
}

input InviteToTeamDAO {
  name: String!
  email: String!
}

input CreateClientDAO {
  name: String!
  logo: String
  primaryColor: String
  secondaryColor: String
}

enum SocialNetworkType {
  FACEBOOK
  INSTAGRAM
  LINKEDIN
  WHATSAPP
  YOUTUBE
  TWITTER
}

input CreateCampaignDAO {
  goalId: ID!
  initialDate: Time!
  finalDate: Time!
  socialNetworks: [String!]!
  postDailyAmount: Int!
  postHours: [String!]!
  weekDays: [String!]!
  formats: [String!]!
  styles: [String!]!
}

input UpdateUserDAO {
  fullName: String
}

input UpdatePasswordDAO {
  password: String
}

input UpdateStoryDAO {
  id: ID!
  image: String
  headerText: String
  bottomText: String
  hashTags: [String]
}

input GenerateCampaignDAO {
  id: ID!
}

type Query {
  campaigns: [Campaign] @isAuthenticated
  loggedUser: User @isAuthenticated
  getThemes(input: GetThemesDAO!): [Theme]
  getGoals(input: GetGoalsDAO!): [Goal]
}

type Mutation {
  signUp(input: SignUpDAO!): Boolean!
  signUpOAuth(input: SignUpOAuthDAO!): Boolean!
  resendCode(input: ResendCodeDAO!): Boolean!
  confirmAccount(input: ConfirmAccountDAO!): Boolean!
  signIn(input: SignInDAO!): SignInDTO!
  signInOAuth(input: SignInOAuthDAO!): SignInDTO!
  resetPassword(input: ResetPasswordDAO!): Boolean!
  resetPasswordConfirmation(input: ResetPasswordConfirmationDAO!): Boolean!
  createCampaign(input: CreateCampaignDAO!): Boolean! @isAuthenticated

  addCredit(input: AddCreditDAO!): String! @isAuthenticated
  updateUser(input: UpdateUserDAO!): Boolean! @isAuthenticated
  updatePassword(input: UpdatePasswordDAO!): Boolean! @isAuthenticated
  createTeam(input: CreateTeamDAO): Boolean! @isAuthenticated
  inviteToTeam(id: ID!, input: InviteToTeamDAO): Boolean! @isAuthenticated
  createClient(input: CreateClientDAO): Client! @isAuthenticated
  generateCampaign(input: GenerateCampaignDAO): Boolean! @isAuthenticated
  # updateStory(input: UpdateStoryDAO!): Boolean! @isAuthenticated
}