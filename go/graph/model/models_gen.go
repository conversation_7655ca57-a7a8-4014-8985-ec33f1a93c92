// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package model

import (
	"fmt"
	"io"
	"strconv"
	"time"
)

type AddCreditDao struct {
	Code string `json:"code"`
}

type Campaign struct {
	ID             *string             `json:"id,omitempty"`
	InitialDate    *time.Time          `json:"initialDate,omitempty"`
	FinalDate      *time.Time          `json:"finalDate,omitempty"`
	SocialNetworks []SocialNetworkType `json:"socialNetworks"`
	Stories        []Story             `json:"stories"`
}

type Client struct {
	ID             string  `json:"id"`
	Name           string  `json:"name"`
	Logo           *string `json:"logo,omitempty"`
	PrimaryColor   *string `json:"primaryColor,omitempty"`
	SecondaryColor *string `json:"secondaryColor,omitempty"`
}

type ConfirmAccountDao struct {
	Email string `json:"email"`
	Code  string `json:"code"`
}

type CreateCampaignDao struct {
	GoalID          string    `json:"goalId"`
	InitialDate     time.Time `json:"initialDate"`
	FinalDate       time.Time `json:"finalDate"`
	SocialNetworks  []string  `json:"socialNetworks"`
	PostDailyAmount int       `json:"postDailyAmount"`
	PostHours       []string  `json:"postHours"`
	WeekDays        []string  `json:"weekDays"`
	Formats         []string  `json:"formats"`
	Styles          []string  `json:"styles"`
}

type CreateClientDao struct {
	Name           string  `json:"name"`
	Logo           *string `json:"logo,omitempty"`
	PrimaryColor   *string `json:"primaryColor,omitempty"`
	SecondaryColor *string `json:"secondaryColor,omitempty"`
}

type CreateTeamDao struct {
	Name string `json:"name"`
}

type GenerateCampaignDao struct {
	ID string `json:"id"`
}

type GetGoalsDao struct {
	ThemeID string `json:"themeId"`
	Page    *int   `json:"page,omitempty"`
}

type GetThemesDao struct {
	Page *int `json:"page,omitempty"`
}

type Goal struct {
	ID      *string `json:"id,omitempty"`
	ThemeID *string `json:"themeId,omitempty"`
	Name    *string `json:"name,omitempty"`
}

type InviteToTeamDao struct {
	Name  string `json:"name"`
	Email string `json:"email"`
}

type Mutation struct {
}

type Query struct {
}

type ResendCodeDao struct {
	Email string `json:"email"`
}

type ResetPasswordConfirmationDao struct {
	Code     string `json:"code"`
	Password string `json:"password"`
}

type ResetPasswordDao struct {
	Email string `json:"email"`
}

type SignInDao struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

type SignInDto struct {
	Token string `json:"token"`
}

type SignInOAuthDao struct {
	IDToken string `json:"idToken"`
}

type SignUpDao struct {
	FullName string `json:"fullName"`
	Email    string `json:"email"`
	Password string `json:"password"`
}

type SignUpOAuthDao struct {
	IDToken string `json:"idToken"`
}

type Story struct {
	ID         string   `json:"id"`
	Image      string   `json:"image"`
	HeaderText string   `json:"headerText"`
	BottomText string   `json:"bottomText"`
	HashTags   []string `json:"hashTags"`
}

type Theme struct {
	ID   *string `json:"id,omitempty"`
	Name *string `json:"name,omitempty"`
}

type UpdatePasswordDao struct {
	Password *string `json:"password,omitempty"`
}

type UpdateStoryDao struct {
	ID         string    `json:"id"`
	Image      *string   `json:"image,omitempty"`
	HeaderText *string   `json:"headerText,omitempty"`
	BottomText *string   `json:"bottomText,omitempty"`
	HashTags   []*string `json:"hashTags,omitempty"`
}

type UpdateUserDao struct {
	FullName *string `json:"fullName,omitempty"`
}

type User struct {
	ID       string `json:"id"`
	FullName string `json:"fullName"`
	Email    string `json:"email"`
}

type SocialNetworkType string

const (
	SocialNetworkTypeFacebook  SocialNetworkType = "FACEBOOK"
	SocialNetworkTypeInstagram SocialNetworkType = "INSTAGRAM"
	SocialNetworkTypeLinkedin  SocialNetworkType = "LINKEDIN"
	SocialNetworkTypeWhatsapp  SocialNetworkType = "WHATSAPP"
	SocialNetworkTypeYoutube   SocialNetworkType = "YOUTUBE"
	SocialNetworkTypeTwitter   SocialNetworkType = "TWITTER"
)

var AllSocialNetworkType = []SocialNetworkType{
	SocialNetworkTypeFacebook,
	SocialNetworkTypeInstagram,
	SocialNetworkTypeLinkedin,
	SocialNetworkTypeWhatsapp,
	SocialNetworkTypeYoutube,
	SocialNetworkTypeTwitter,
}

func (e SocialNetworkType) IsValid() bool {
	switch e {
	case SocialNetworkTypeFacebook, SocialNetworkTypeInstagram, SocialNetworkTypeLinkedin, SocialNetworkTypeWhatsapp, SocialNetworkTypeYoutube, SocialNetworkTypeTwitter:
		return true
	}
	return false
}

func (e SocialNetworkType) String() string {
	return string(e)
}

func (e *SocialNetworkType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = SocialNetworkType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid SocialNetworkType", str)
	}
	return nil
}

func (e SocialNetworkType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}
